#!/usr/bin/env python3
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 数据库连接配置
SYNC_DATABASE_URL = 'postgresql://postgres:postgres@localhost:5432/bounty_ams'

def check_model_types():
    """检查动态模型类型"""
    engine = create_engine(SYNC_DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 查询所有模型类型
        result = db.execute(text("SELECT name, display_name, is_active, is_system FROM model_types"))
        model_types = result.fetchall()
        
        print("\n当前动态模型类型:")
        print("=" * 80)
        print(f"{'名称':<20} {'显示名称':<20} {'是否激活':<10} {'是否系统':<10}")
        print("-" * 80)
        
        for model in model_types:
            print(f"{model[0]:<20} {model[1]:<20} {str(model[2]):<10} {str(model[3]):<10}")
            
        print("=" * 80)
        
        # 检查用户和角色模型
        user_model = db.execute(text("SELECT * FROM model_types WHERE name = 'user'")).fetchone()
        role_model = db.execute(text("SELECT * FROM model_types WHERE name = 'user_role'")).fetchone()
        
        if not user_model:
            print("\n⚠️ 警告: 用户模型类型不存在!")
        if not role_model:
            print("\n⚠️ 警告: 角色模型类型不存在!")
            
    except Exception as e:
        print(f"错误: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    check_model_types() 