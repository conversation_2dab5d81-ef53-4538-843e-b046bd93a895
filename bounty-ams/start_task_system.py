#!/usr/bin/env python3
"""
快速启动任务管理系统
"""

import asyncio
import subprocess
import sys
import os
import time
from pathlib import Path

def run_command(command, cwd=None, check=True):
    """运行命令"""
    print(f"🔧 执行命令: {command}")
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd, 
            check=check,
            capture_output=True,
            text=True
        )
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令执行失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        if check:
            sys.exit(1)
        return e

async def init_task_system():
    """初始化任务管理系统"""
    print("🚀 开始初始化任务管理系统...")
    
    backend_dir = Path(__file__).parent / "backend"
    
    try:
        # 1. 初始化动态模型
        print("\n📋 步骤1: 初始化动态模型...")
        result = run_command(
            "python init_task_dynamic_models.py",
            cwd=backend_dir
        )
        
        if result.returncode == 0:
            print("✅ 动态模型初始化成功")
        else:
            print("⚠️ 动态模型初始化可能有问题，但继续执行...")
        
        print("🎉 任务管理系统初始化完成!")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False
    
    return True

def start_backend():
    """启动后端服务"""
    print("\n🔧 启动后端服务...")
    backend_dir = Path(__file__).parent / "backend"
    
    try:
        # 检查是否已有服务在运行
        result = run_command("lsof -i :8000", check=False)
        if result.returncode == 0:
            print("⚠️ 端口8000已被占用，请先停止现有服务")
            return False
        
        print("启动FastAPI服务器...")
        print("📍 服务地址: http://localhost:8000")
        print("📍 API文档: http://localhost:8000/docs")
        print("📍 按 Ctrl+C 停止服务")
        
        # 启动服务
        run_command(
            "python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000",
            cwd=backend_dir
        )
        
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except Exception as e:
        print(f"❌ 启动后端失败: {e}")
        return False
    
    return True

def start_frontend():
    """启动前端服务"""
    print("\n🔧 启动前端服务...")
    frontend_dir = Path(__file__).parent / "frontend"
    
    try:
        # 检查是否已有服务在运行
        result = run_command("lsof -i :3000", check=False)
        if result.returncode == 0:
            print("⚠️ 端口3000已被占用，请先停止现有服务")
            return False
        
        print("启动React开发服务器...")
        print("📍 前端地址: http://localhost:3000")
        print("📍 按 Ctrl+C 停止服务")
        
        # 启动服务
        run_command("npm run dev", cwd=frontend_dir)
        
    except KeyboardInterrupt:
        print("\n🛑 前端服务已停止")
    except Exception as e:
        print(f"❌ 启动前端失败: {e}")
        return False
    
    return True

def check_dependencies():
    """检查依赖"""
    print("🔍 检查系统依赖...")
    
    # 检查Python
    try:
        result = run_command("python --version", check=False)
        if result.returncode != 0:
            print("❌ Python未安装或不在PATH中")
            return False
        print(f"✅ Python: {result.stdout.strip()}")
    except:
        print("❌ 无法检查Python版本")
        return False
    
    # 检查Node.js
    try:
        result = run_command("node --version", check=False)
        if result.returncode != 0:
            print("❌ Node.js未安装或不在PATH中")
            return False
        print(f"✅ Node.js: {result.stdout.strip()}")
    except:
        print("❌ 无法检查Node.js版本")
        return False
    
    # 检查npm
    try:
        result = run_command("npm --version", check=False)
        if result.returncode != 0:
            print("❌ npm未安装或不在PATH中")
            return False
        print(f"✅ npm: {result.stdout.strip()}")
    except:
        print("❌ 无法检查npm版本")
        return False
    
    return True

def show_usage():
    """显示使用说明"""
    print("""
🎯 任务管理系统快速启动脚本

用法:
    python start_task_system.py [命令]

命令:
    init        - 初始化任务管理系统 (创建动态模型和示例数据)
    backend     - 启动后端服务 (FastAPI)
    frontend    - 启动前端服务 (React)
    check       - 检查系统依赖
    help        - 显示此帮助信息

示例:
    python start_task_system.py init      # 初始化系统
    python start_task_system.py backend   # 启动后端
    python start_task_system.py frontend  # 启动前端

完整启动流程:
    1. python start_task_system.py check     # 检查依赖
    2. python start_task_system.py init      # 初始化系统
    3. python start_task_system.py backend   # 启动后端 (新终端)
    4. python start_task_system.py frontend  # 启动前端 (新终端)

系统信息:
    - 后端API: http://localhost:8000
    - API文档: http://localhost:8000/docs
    - 前端界面: http://localhost:3000
    - 数据库: SQLite (backend/bounty_ams.db)

功能特性:
    ✅ 基于动态模型的任务模板管理
    ✅ 可视化工作流设计器
    ✅ 实时任务执行监控
    ✅ 智能Agent管理
    ✅ 完整的API文档
""")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        show_usage()
        return
    
    command = sys.argv[1].lower()
    
    if command == "help":
        show_usage()
    elif command == "check":
        if check_dependencies():
            print("✅ 所有依赖检查通过")
        else:
            print("❌ 依赖检查失败")
            sys.exit(1)
    elif command == "init":
        if not check_dependencies():
            print("❌ 依赖检查失败，请先安装必要的依赖")
            sys.exit(1)
        
        success = asyncio.run(init_task_system())
        if success:
            print("\n🎉 初始化完成! 接下来可以:")
            print("1. python start_task_system.py backend   # 启动后端")
            print("2. python start_task_system.py frontend  # 启动前端")
        else:
            sys.exit(1)
    elif command == "backend":
        if not check_dependencies():
            print("❌ 依赖检查失败")
            sys.exit(1)
        start_backend()
    elif command == "frontend":
        if not check_dependencies():
            print("❌ 依赖检查失败")
            sys.exit(1)
        start_frontend()
    else:
        print(f"❌ 未知命令: {command}")
        show_usage()
        sys.exit(1)

if __name__ == "__main__":
    main()
