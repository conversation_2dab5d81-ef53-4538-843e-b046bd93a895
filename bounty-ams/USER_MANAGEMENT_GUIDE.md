# 用户管理系统使用指南

## 🎯 概述

本系统实现了一个完整的用户管理功能，包括用户CRUD操作、角色权限管理、密码管理、活动日志等功能。

## 📋 功能特性

### 1. 增强的用户模型
- **基础信息**: 用户名、邮箱、密码
- **个人信息**: 真实姓名、手机号、头像、部门、职位
- **安全信息**: 最后登录时间、登录次数、密码修改时间、账户锁定状态
- **管理信息**: 创建者、备注、状态管理

### 2. 角色权限系统
- **4个默认角色**:
  - `超级管理员`: 拥有所有权限
  - `管理员`: 拥有大部分管理权限
  - `操作员`: 可以管理资产和任务
  - `查看者`: 只读权限
- **权限粒度**: 按模块和操作细分权限
- **角色分配**: 支持多角色分配

### 3. 用户活动日志
- **操作记录**: 记录所有用户操作
- **详细信息**: 包含IP地址、用户代理、操作详情
- **审计追踪**: 支持安全审计和问题排查

### 4. 安全特性
- **密码策略**: 最小长度要求、强制修改密码
- **账户锁定**: 登录失败次数限制
- **会话管理**: 登录状态跟踪
- **权限验证**: API级别的权限检查

## 🚀 使用方法

### 访问用户管理
1. 使用管理员账户登录系统
2. 点击右上角用户头像
3. 选择"用户管理"选项

### 用户管理操作

#### 创建用户
1. 在用户管理页面点击"创建用户"
2. 填写用户基本信息：
   - 用户名（必填，3-50字符）
   - 邮箱（必填，有效邮箱格式）
   - 密码（必填，最少6字符）
   - 真实姓名、手机号、部门、职位（可选）
3. 设置用户权限和状态
4. 点击确定创建

#### 编辑用户
1. 在用户列表中找到目标用户
2. 点击操作列的"更多"按钮
3. 选择"编辑"
4. 修改用户信息
5. 保存更改

#### 分配角色
1. 在用户列表中找到目标用户
2. 点击操作列的"更多"按钮
3. 选择"分配角色"
4. 选择一个或多个角色
5. 确认分配

#### 重置密码
1. 在用户列表中找到目标用户
2. 点击操作列的"更多"按钮
3. 选择"重置密码"
4. 输入新密码
5. 选择是否强制用户下次登录时修改密码

#### 查看活动日志
1. 在用户列表中找到目标用户
2. 点击操作列的"更多"按钮
3. 选择"活动日志"
4. 查看用户的操作历史

### 角色管理操作

#### 创建角色
1. 切换到"角色管理"标签页
2. 点击"创建角色"
3. 填写角色信息：
   - 角色名称（英文标识符）
   - 显示名称（中文名称）
   - 描述（可选）
   - 权限列表
4. 保存角色

#### 编辑角色
1. 在角色列表中找到目标角色
2. 点击"编辑"按钮（系统角色不可编辑）
3. 修改角色信息
4. 保存更改

## 🔧 技术实现

### 数据库结构

#### users表增强字段
```sql
-- 个人信息
full_name VARCHAR(255)
phone VARCHAR(20)
avatar_url TEXT
department VARCHAR(100)
position VARCHAR(100)

-- 安全信息
last_login_at TIMESTAMP WITH TIME ZONE
last_login_ip INET
login_count INTEGER DEFAULT 0
password_changed_at TIMESTAMP WITH TIME ZONE
must_change_password BOOLEAN DEFAULT FALSE
account_locked_until TIMESTAMP WITH TIME ZONE
failed_login_attempts INTEGER DEFAULT 0

-- 管理信息
notes TEXT
created_by_user_id UUID REFERENCES users(id)
```

#### 新增表
- `roles`: 角色定义表
- `user_roles`: 用户角色关联表
- `user_activity_logs`: 用户活动日志表

### API端点

#### 用户管理
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `GET /api/users/{user_id}` - 获取用户详情
- `PUT /api/users/{user_id}` - 更新用户
- `DELETE /api/users/{user_id}` - 删除用户
- `POST /api/users/{user_id}/password-reset` - 重置密码

#### 角色管理
- `GET /api/roles` - 获取角色列表
- `POST /api/roles` - 创建角色
- `PUT /api/roles/{role_id}` - 更新角色
- `DELETE /api/roles/{role_id}` - 删除角色

#### 用户角色
- `POST /api/users/{user_id}/roles` - 分配角色
- `GET /api/users/{user_id}/roles` - 获取用户角色

#### 活动日志
- `GET /api/users/{user_id}/activity-logs` - 获取用户活动日志
- `GET /api/activity-logs` - 获取所有活动日志

### 权限系统

#### 权限定义
权限采用 `模块.操作` 的格式，例如：
- `user.create` - 创建用户
- `user.read` - 查看用户
- `user.update` - 更新用户
- `user.delete` - 删除用户
- `role.create` - 创建角色
- `platform.read` - 查看平台
- `project.update` - 更新项目

#### 权限检查
- API级别：使用装饰器进行权限验证
- 前端级别：根据用户角色显示/隐藏功能
- 数据级别：查询时过滤用户可访问的数据

## 📊 统计信息

用户管理页面提供以下统计信息：
- 总用户数
- 活跃用户数
- 管理员用户数
- 锁定用户数

## 🔒 安全建议

1. **定期审查用户权限**: 确保用户只拥有必要的权限
2. **监控活动日志**: 定期检查异常操作
3. **强密码策略**: 要求用户使用强密码
4. **定期密码更新**: 建议用户定期更改密码
5. **及时禁用离职用户**: 员工离职时及时禁用账户

## 🚨 注意事项

1. **超级管理员权限**: 谨慎分配超级管理员权限
2. **系统角色**: 系统预定义的角色不能删除或修改
3. **自我操作限制**: 用户不能删除自己的账户
4. **角色依赖**: 删除角色前确保没有用户使用该角色

## 📞 技术支持

如果在使用过程中遇到问题，请：
1. 检查用户权限是否正确
2. 查看活动日志了解操作历史
3. 确认网络连接和服务状态
4. 联系系统管理员获取帮助
