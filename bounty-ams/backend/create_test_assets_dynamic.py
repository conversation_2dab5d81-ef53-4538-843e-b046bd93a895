#!/usr/bin/env python3
"""
创建测试资产数据
为动态模型系统创建一些测试资产
"""

import asyncio
import logging
from sqlalchemy import select
from database import AsyncSessionLocal
from models_dynamic import ModelType, DynamicEntity, User
from routes.dynamic_models import sync_entity_to_elasticsearch
import uuid
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def create_test_assets():
    """创建测试资产数据"""
    async with AsyncSessionLocal() as db:
        # 获取资产模型类型
        asset_model_query = select(ModelType).where(ModelType.name == 'asset')
        asset_model_result = await db.execute(asset_model_query)
        asset_model = asset_model_result.scalar_one_or_none()
        
        if not asset_model:
            logger.error("Asset model not found")
            return False
        
        # 获取admin用户
        admin_query = select(User).where(User.username == 'admin')
        admin_result = await db.execute(admin_query)
        admin_user = admin_result.scalar_one_or_none()
        
        if not admin_user:
            logger.error("Admin user not found")
            return False
        
        # 获取现有平台和项目
        platform_query = select(ModelType).where(ModelType.name == 'platform')
        platform_model_result = await db.execute(platform_query)
        platform_model = platform_model_result.scalar_one_or_none()
        
        project_query = select(ModelType).where(ModelType.name == 'project')
        project_model_result = await db.execute(project_query)
        project_model = project_model_result.scalar_one_or_none()
        
        platform_ids = []
        project_ids = []
        
        if platform_model:
            platform_entities_query = select(DynamicEntity).where(DynamicEntity.model_type_id == platform_model.id)
            platform_entities_result = await db.execute(platform_entities_query)
            platform_entities = platform_entities_result.scalars().all()
            platform_ids = [str(entity.id) for entity in platform_entities]
        
        if project_model:
            project_entities_query = select(DynamicEntity).where(DynamicEntity.model_type_id == project_model.id)
            project_entities_result = await db.execute(project_entities_query)
            project_entities = project_entities_result.scalars().all()
            project_ids = [str(entity.id) for entity in project_entities]
        
        # 创建测试资产数据
        test_assets = [
            {
                'asset_id': str(uuid.uuid4()),
                'fingerprint_hash': 'hash1',
                'asset_type': 'domain',
                'asset_value': 'example.com',
                'asset_host': 'example.com',
                'ip_address': '***********',
                'confidence': '0.9',
                'status': 'verified',
                'platform_id': platform_ids[0] if platform_ids else None,
                'project_id': project_ids[0] if project_ids else None,
                'discovered_at': datetime.utcnow().isoformat(),
                'country_cn': '中国',
                'city_cn': '北京',
                'application_name': 'Apache',
                'service_name': 'HTTP'
            },
            {
                'asset_id': str(uuid.uuid4()),
                'fingerprint_hash': 'hash2',
                'asset_type': 'ip',
                'asset_value': '***********00',
                'asset_host': '***********00',
                'ip_address': '***********00',
                'asset_port': 443,
                'confidence': '0.8',
                'status': 'discovered',
                'platform_id': platform_ids[0] if platform_ids else None,
                'project_id': project_ids[1] if len(project_ids) > 1 else project_ids[0] if project_ids else None,
                'discovered_at': datetime.utcnow().isoformat(),
                'country_cn': '美国',
                'city_cn': '纽约',
                'application_name': 'Nginx',
                'service_name': 'HTTPS'
            },
            {
                'asset_id': str(uuid.uuid4()),
                'fingerprint_hash': 'hash3',
                'asset_type': 'subdomain',
                'asset_value': 'api.example.com',
                'asset_host': 'api.example.com',
                'ip_address': '***********',
                'confidence': '0.7',
                'status': 'confirmed',
                'platform_id': platform_ids[1] if len(platform_ids) > 1 else platform_ids[0] if platform_ids else None,
                'project_id': project_ids[0] if project_ids else None,
                'discovered_at': datetime.utcnow().isoformat(),
                'country_cn': '中国',
                'city_cn': '上海',
                'application_name': 'Node.js',
                'service_name': 'API'
            },
            {
                'asset_id': str(uuid.uuid4()),
                'fingerprint_hash': 'hash4',
                'asset_type': 'url',
                'asset_value': 'https://test.example.com/admin',
                'asset_host': 'test.example.com',
                'ip_address': '***********',
                'url': 'https://test.example.com/admin',
                'confidence': '0.6',
                'status': 'discovered',
                'platform_id': platform_ids[1] if len(platform_ids) > 1 else platform_ids[0] if platform_ids else None,
                'project_id': project_ids[1] if len(project_ids) > 1 else project_ids[0] if project_ids else None,
                'discovered_at': datetime.utcnow().isoformat(),
                'country_cn': '中国',
                'city_cn': '深圳',
                'application_name': 'WordPress',
                'service_name': 'Web'
            },
            {
                'asset_id': str(uuid.uuid4()),
                'fingerprint_hash': 'hash5',
                'asset_type': 'port',
                'asset_value': '***********:8080',
                'asset_host': '***********',
                'ip_address': '***********',
                'asset_port': 8080,
                'confidence': '0.9',
                'status': 'verified',
                'platform_id': platform_ids[0] if platform_ids else None,
                'project_id': project_ids[0] if project_ids else None,
                'discovered_at': datetime.utcnow().isoformat(),
                'country_cn': '中国',
                'city_cn': '广州',
                'application_name': 'Tomcat',
                'service_name': 'HTTP'
            }
        ]
        
        created_count = 0
        for asset_data in test_assets:
            try:
                # 创建动态实体
                dynamic_entity = DynamicEntity(
                    model_type_id=asset_model.id,
                    entity_data=asset_data,
                    created_by_user_id=admin_user.id
                )
                
                db.add(dynamic_entity)
                await db.flush()
                
                # 同步到Elasticsearch
                await sync_entity_to_elasticsearch(dynamic_entity, asset_model)
                
                created_count += 1
                logger.info(f"Created asset: {asset_data['asset_value']}")
                
            except Exception as e:
                logger.error(f"Failed to create asset {asset_data['asset_value']}: {e}")
                continue
        
        await db.commit()
        
        logger.info(f"✅ Created {created_count} test assets")
        return True

async def main():
    """主函数"""
    logger.info("🚀 开始创建测试资产数据...")
    
    try:
        success = await create_test_assets()
        if success:
            logger.info("🎉 测试资产创建完成！")
        else:
            logger.error("❌ 测试资产创建失败")
    except Exception as e:
        logger.error(f"❌ 创建过程中发生错误: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())