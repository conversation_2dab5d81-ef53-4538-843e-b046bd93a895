#!/usr/bin/env python3
"""
检查平台项目数据库定义及数据的唯一性约束
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 同步数据库连接
SYNC_DATABASE_URL = 'postgresql://postgres:postgres@localhost:5432/bounty_ams'

def check_table_structure(db, table_name):
    """检查表结构"""
    print(f"\n=== {table_name} 表结构 ===")
    
    result = db.execute(text(f"""
        SELECT 
            column_name, 
            data_type, 
            is_nullable, 
            column_default,
            character_maximum_length
        FROM information_schema.columns 
        WHERE table_name = '{table_name}' 
        ORDER BY ordinal_position;
    """))
    
    for row in result:
        print(f"  {row[0]}: {row[1]} (nullable: {row[2]}, default: {row[3]}, max_length: {row[4]})")

def check_constraints(db, table_name):
    """检查约束"""
    print(f"\n=== {table_name} 约束 ===")
    
    result = db.execute(text(f"""
        SELECT 
            tc.constraint_name,
            tc.table_name,
            kcu.column_name,
            tc.constraint_type
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu 
            ON tc.constraint_name = kcu.constraint_name
        WHERE tc.table_name = '{table_name}' 
            AND tc.constraint_type IN ('UNIQUE', 'PRIMARY KEY', 'FOREIGN KEY');
    """))
    
    for row in result:
        print(f"  {row[0]}: {row[1]}.{row[2]} ({row[3]})")

def check_indexes(db, table_name):
    """检查索引"""
    print(f"\n=== {table_name} 索引 ===")
    
    result = db.execute(text(f"""
        SELECT 
            indexname,
            indexdef
        FROM pg_indexes 
        WHERE tablename = '{table_name}';
    """))
    
    for row in result:
        print(f"  {row[0]}: {row[1]}")

def check_platform_project_data(db):
    """检查平台和项目数据的唯一性"""
    print("\n=== 检查平台和项目数据 ===")
    
    # 检查是否存在platform模型
    result = db.execute(text("""
        SELECT id, name, display_name 
        FROM model_types 
        WHERE name IN ('platform', 'project', 'enhanced_asset')
        ORDER BY name;
    """))
    
    print("\n已创建的模型类型:")
    model_types = {}
    for row in result:
        print(f"  {row[1]}: {row[2]} (ID: {row[0]})")
        model_types[row[1]] = row[0]
    
    # 检查platform模型的字段定义
    if 'platform' in model_types:
        print(f"\n=== Platform模型字段定义 ===")
        result = db.execute(text(f"""
            SELECT 
                field_name, 
                field_type, 
                display_name, 
                is_required, 
                is_unique,
                validation_rules
            FROM model_fields 
            WHERE model_type_id = '{model_types['platform']}'
            ORDER BY sort_order;
        """))
        
        for row in result:
            unique_flag = " [UNIQUE]" if row[4] else ""
            required_flag = " [REQUIRED]" if row[3] else ""
            print(f"  {row[0]}: {row[1]} - {row[2]}{unique_flag}{required_flag}")
            if row[5]:
                print(f"    验证规则: {row[5]}")
    
    # 检查project模型的字段定义
    if 'project' in model_types:
        print(f"\n=== Project模型字段定义 ===")
        result = db.execute(text(f"""
            SELECT 
                field_name, 
                field_type, 
                display_name, 
                is_required, 
                is_unique,
                validation_rules
            FROM model_fields 
            WHERE model_type_id = '{model_types['project']}'
            ORDER BY sort_order;
        """))
        
        for row in result:
            unique_flag = " [UNIQUE]" if row[4] else ""
            required_flag = " [REQUIRED]" if row[3] else ""
            print(f"  {row[0]}: {row[1]} - {row[2]}{unique_flag}{required_flag}")
            if row[5]:
                print(f"    验证规则: {row[5]}")
    
    # 检查实际数据
    print(f"\n=== 实际数据检查 ===")
    
    # 检查platform实例
    if 'platform' in model_types:
        result = db.execute(text(f"""
            SELECT 
                id,
                entity_data->>'name' as name,
                entity_data->>'display_name' as display_name,
                entity_data->>'platform_type' as platform_type
            FROM dynamic_entities 
            WHERE model_type_id = '{model_types['platform']}'
            ORDER BY entity_data->>'name';
        """))
        
        platforms = list(result)
        print(f"\nPlatform实例数量: {len(platforms)}")
        for row in platforms:
            print(f"  {row[1]} ({row[2]}) - {row[3]} [ID: {row[0]}]")
        
        # 检查platform name重复
        result = db.execute(text(f"""
            SELECT 
                entity_data->>'name' as name,
                COUNT(*) as count
            FROM dynamic_entities 
            WHERE model_type_id = '{model_types['platform']}'
            GROUP BY entity_data->>'name'
            HAVING COUNT(*) > 1;
        """))
        
        duplicates = list(result)
        if duplicates:
            print(f"\n⚠️  发现重复的Platform名称:")
            for row in duplicates:
                print(f"  {row[0]}: {row[1]}个重复")
        else:
            print(f"\n✅ Platform名称无重复")
    
    # 检查project实例
    if 'project' in model_types:
        result = db.execute(text(f"""
            SELECT 
                id,
                entity_data->>'name' as name,
                entity_data->>'platform_id' as platform_id,
                entity_data->>'external_id' as external_id
            FROM dynamic_entities 
            WHERE model_type_id = '{model_types['project']}'
            ORDER BY entity_data->>'platform_id', entity_data->>'name';
        """))
        
        projects = list(result)
        print(f"\nProject实例数量: {len(projects)}")
        for row in projects:
            print(f"  {row[1]} (Platform: {row[2]}, External: {row[3]}) [ID: {row[0]}]")
        
        # 检查project在同一平台下的重复
        result = db.execute(text(f"""
            SELECT 
                entity_data->>'platform_id' as platform_id,
                entity_data->>'name' as name,
                COUNT(*) as count
            FROM dynamic_entities 
            WHERE model_type_id = '{model_types['project']}'
            GROUP BY entity_data->>'platform_id', entity_data->>'name'
            HAVING COUNT(*) > 1;
        """))
        
        duplicates = list(result)
        if duplicates:
            print(f"\n⚠️  发现同一平台下重复的Project名称:")
            for row in duplicates:
                print(f"  Platform {row[0]} - Project {row[1]}: {row[2]}个重复")
        else:
            print(f"\n✅ 同一平台下Project名称无重复")

def main():
    """主函数"""
    try:
        engine = create_engine(SYNC_DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        with SessionLocal() as db:
            print("🔍 检查平台项目数据库定义及数据的唯一性约束")
            
            # 检查核心表结构
            tables_to_check = ['model_types', 'model_fields', 'dynamic_entities']
            
            for table in tables_to_check:
                try:
                    check_table_structure(db, table)
                    check_constraints(db, table)
                    check_indexes(db, table)
                except Exception as e:
                    print(f"❌ 检查表 {table} 时出错: {e}")
            
            # 检查平台项目数据
            check_platform_project_data(db)
            
            print(f"\n✅ 数据库检查完成")
            
    except Exception as e:
        print(f"❌ 数据库连接错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
