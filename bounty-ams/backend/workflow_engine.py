"""
工作流引擎 - 负责复杂任务流程的编排和执行
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import logging
from jinja2 import Environment, DictLoader, StrictUndefined

from task_template_engine import TaskTemplateEngine, TaskExecution, TaskStatus

logger = logging.getLogger(__name__)

class WorkflowStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"

class StageStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"

@dataclass
class TaskDefinition:
    """工作流中的任务定义"""
    task_id: str
    template_id: str
    parameters: Dict[str, Any]
    condition: str = "always"  # 执行条件
    depends_on: List[str] = field(default_factory=list)  # 依赖的任务
    timeout: Optional[int] = None
    retry_count: int = 0
    on_failure: str = "stop"  # stop, continue, retry

@dataclass
class WorkflowStage:
    """工作流阶段"""
    stage_id: str
    name: str
    tasks: List[TaskDefinition]
    parallel: bool = False  # 是否并行执行任务
    condition: str = "always"  # 阶段执行条件
    timeout: Optional[int] = None
    on_failure: str = "stop"  # stop, continue

@dataclass
class Workflow:
    """工作流定义"""
    workflow_id: str
    name: str
    version: str
    description: str
    parameters: Dict[str, Any]
    stages: List[WorkflowStage]
    error_handling: Dict[str, Any] = field(default_factory=dict)
    output_aggregation: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)

@dataclass
class WorkflowExecution:
    """工作流执行实例"""
    execution_id: str
    workflow_id: str
    parameters: Dict[str, Any]
    status: WorkflowStatus = WorkflowStatus.PENDING
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    current_stage: Optional[str] = None
    context: Dict[str, Any] = field(default_factory=dict)  # 执行上下文
    task_executions: Dict[str, TaskExecution] = field(default_factory=dict)
    stage_status: Dict[str, StageStatus] = field(default_factory=dict)
    output: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    logs: List[str] = field(default_factory=list)

class ConditionEvaluator:
    """条件评估器"""
    
    def __init__(self):
        self.jinja_env = Environment(
            loader=DictLoader({}),
            undefined=StrictUndefined
        )
    
    def evaluate(self, condition: str, context: Dict[str, Any]) -> bool:
        """评估条件表达式"""
        if condition == "always":
            return True
        if condition == "never":
            return False
        
        try:
            # 使用Jinja2模板引擎评估条件
            template = self.jinja_env.from_string(f"{{{{ {condition} }}}}")
            result = template.render(**context)
            
            # 转换为布尔值
            if isinstance(result, str):
                return result.lower() in ('true', '1', 'yes', 'on')
            return bool(result)
            
        except Exception as e:
            logger.error(f"条件评估失败: {condition}, 错误: {e}")
            return False

class WorkflowEngine:
    """工作流引擎"""
    
    def __init__(self, task_template_engine: TaskTemplateEngine, task_scheduler):
        self.template_engine = task_template_engine
        self.task_scheduler = task_scheduler
        self.condition_evaluator = ConditionEvaluator()
        self.workflows: Dict[str, Workflow] = {}
        self.running_executions: Dict[str, WorkflowExecution] = {}
        self.load_builtin_workflows()
    
    def load_builtin_workflows(self):
        """加载内置工作流"""
        # 综合安全扫描工作流
        comprehensive_scan = Workflow(
            workflow_id="comprehensive_scan_v1",
            name="综合安全扫描",
            version="1.0.0",
            description="从子域名发现到漏洞扫描的完整流程",
            parameters={
                "target": {
                    "type": "string",
                    "required": True,
                    "description": "目标域名"
                },
                "scan_depth": {
                    "type": "string",
                    "enum": ["light", "medium", "deep"],
                    "default": "medium",
                    "description": "扫描深度"
                }
            },
            stages=[
                WorkflowStage(
                    stage_id="discovery",
                    name="资产发现阶段",
                    parallel=False,
                    tasks=[
                        TaskDefinition(
                            task_id="subdomain_discovery",
                            template_id="subdomain_discovery_v1",
                            parameters={
                                "target": "{{workflow.target}}",
                                "timeout": 600
                            }
                        ),
                        TaskDefinition(
                            task_id="port_scan",
                            template_id="port_scan_v1",
                            parameters={
                                "targets": "{{subdomain_discovery.output.subdomains}}",
                                "ports": "{{workflow.scan_depth == 'deep' and 'full' or 'common'}}"
                            },
                            condition="subdomain_discovery.status == 'success' and subdomain_discovery.output.subdomains|length > 0",
                            depends_on=["subdomain_discovery"]
                        )
                    ]
                ),
                WorkflowStage(
                    stage_id="identification",
                    name="服务识别阶段",
                    parallel=True,
                    condition="port_scan.status == 'success'",
                    tasks=[
                        TaskDefinition(
                            task_id="service_detection",
                            template_id="service_detection_v1",
                            parameters={
                                "targets": "{{port_scan.output.open_ports}}"
                            },
                            depends_on=["port_scan"]
                        ),
                        TaskDefinition(
                            task_id="web_screenshot",
                            template_id="web_screenshot_v1",
                            parameters={
                                "urls": "{{port_scan.output.web_services}}"
                            },
                            condition="port_scan.output.web_services|length > 0",
                            depends_on=["port_scan"]
                        )
                    ]
                )
            ],
            error_handling={
                "strategy": "continue_on_error",
                "max_stage_failures": 2,
                "notification": {
                    "on_failure": True,
                    "on_success": True,
                    "channels": ["email", "webhook"]
                }
            },
            output_aggregation={
                "merge_strategy": "deep_merge",
                "final_report": {
                    "format": "json",
                    "include_metadata": True,
                    "include_raw_outputs": False
                }
            }
        )
        
        self.workflows[comprehensive_scan.workflow_id] = comprehensive_scan
    
    def register_workflow(self, workflow: Workflow):
        """注册工作流"""
        self.validate_workflow(workflow)
        self.workflows[workflow.workflow_id] = workflow
    
    def validate_workflow(self, workflow: Workflow):
        """验证工作流定义"""
        # 验证阶段和任务的依赖关系
        all_task_ids = set()
        for stage in workflow.stages:
            for task in stage.tasks:
                if task.task_id in all_task_ids:
                    raise ValueError(f"重复的任务ID: {task.task_id}")
                all_task_ids.add(task.task_id)
        
        # 验证依赖关系
        for stage in workflow.stages:
            for task in stage.tasks:
                for dep in task.depends_on:
                    if dep not in all_task_ids:
                        raise ValueError(f"任务 {task.task_id} 依赖的任务 {dep} 不存在")
                
                # 验证模板存在
                if not self.template_engine.get_template(task.template_id):
                    raise ValueError(f"任务 {task.task_id} 使用的模板 {task.template_id} 不存在")
    
    async def execute_workflow(self, workflow_id: str, parameters: Dict[str, Any]) -> WorkflowExecution:
        """执行工作流"""
        workflow = self.workflows.get(workflow_id)
        if not workflow:
            raise ValueError(f"工作流不存在: {workflow_id}")
        
        # 创建执行实例
        execution_id = f"{workflow_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')}"
        execution = WorkflowExecution(
            execution_id=execution_id,
            workflow_id=workflow_id,
            parameters=parameters,
            context={"workflow": parameters}
        )
        
        self.running_executions[execution_id] = execution
        
        try:
            execution.status = WorkflowStatus.RUNNING
            execution.start_time = datetime.utcnow()
            
            # 执行各个阶段
            for stage in workflow.stages:
                execution.current_stage = stage.stage_id
                
                # 评估阶段执行条件
                if not self.condition_evaluator.evaluate(stage.condition, execution.context):
                    execution.stage_status[stage.stage_id] = StageStatus.SKIPPED
                    logger.info(f"阶段 {stage.stage_id} 被跳过，条件不满足: {stage.condition}")
                    continue
                
                # 执行阶段
                await self.execute_stage(stage, execution)
                
                # 检查阶段执行结果
                if execution.stage_status[stage.stage_id] == StageStatus.FAILED:
                    if workflow.error_handling.get("strategy") != "continue_on_error":
                        break
            
            # 聚合输出
            execution.output = await self.aggregate_output(workflow, execution)
            execution.status = WorkflowStatus.SUCCESS
            
        except Exception as e:
            execution.status = WorkflowStatus.FAILED
            execution.error = str(e)
            logger.error(f"工作流执行失败: {execution_id}, 错误: {e}")
        
        finally:
            execution.end_time = datetime.utcnow()
            if execution_id in self.running_executions:
                del self.running_executions[execution_id]
        
        return execution
    
    async def execute_stage(self, stage: WorkflowStage, execution: WorkflowExecution):
        """执行工作流阶段"""
        execution.stage_status[stage.stage_id] = StageStatus.RUNNING
        
        try:
            if stage.parallel:
                # 并行执行任务
                await self.execute_tasks_parallel(stage.tasks, execution)
            else:
                # 串行执行任务
                await self.execute_tasks_sequential(stage.tasks, execution)
            
            execution.stage_status[stage.stage_id] = StageStatus.SUCCESS
            
        except Exception as e:
            execution.stage_status[stage.stage_id] = StageStatus.FAILED
            logger.error(f"阶段执行失败: {stage.stage_id}, 错误: {e}")
            raise
    
    async def execute_tasks_sequential(self, tasks: List[TaskDefinition], execution: WorkflowExecution):
        """串行执行任务"""
        for task_def in tasks:
            if self.should_execute_task(task_def, execution):
                await self.execute_single_task(task_def, execution)
    
    async def execute_tasks_parallel(self, tasks: List[TaskDefinition], execution: WorkflowExecution):
        """并行执行任务"""
        # 创建任务协程
        task_coroutines = []
        for task_def in tasks:
            if self.should_execute_task(task_def, execution):
                task_coroutines.append(self.execute_single_task(task_def, execution))
        
        # 并行执行
        if task_coroutines:
            await asyncio.gather(*task_coroutines, return_exceptions=True)
    
    def should_execute_task(self, task_def: TaskDefinition, execution: WorkflowExecution) -> bool:
        """判断是否应该执行任务"""
        # 检查依赖任务是否完成
        for dep_task_id in task_def.depends_on:
            dep_execution = execution.task_executions.get(dep_task_id)
            if not dep_execution or dep_execution.status != TaskStatus.SUCCESS:
                return False
        
        # 评估执行条件
        return self.condition_evaluator.evaluate(task_def.condition, execution.context)
    
    async def execute_single_task(self, task_def: TaskDefinition, execution: WorkflowExecution):
        """执行单个任务"""
        try:
            # 渲染任务参数
            rendered_params = self.render_task_parameters(task_def.parameters, execution.context)
            
            # 创建任务执行
            task_execution = self.template_engine.render_template(task_def.template_id, rendered_params)
            execution.task_executions[task_def.task_id] = task_execution
            
            # 提交任务到调度器
            result = await self.task_scheduler.submit_task(task_execution)
            
            # 更新执行上下文
            execution.context[task_def.task_id] = {
                "status": result.status.value,
                "output": result.output,
                "execution_time": (result.end_time - result.start_time).total_seconds() if result.end_time and result.start_time else 0
            }
            
        except Exception as e:
            logger.error(f"任务执行失败: {task_def.task_id}, 错误: {e}")
            execution.context[task_def.task_id] = {
                "status": "failed",
                "error": str(e)
            }
            raise
    
    def render_task_parameters(self, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """渲染任务参数"""
        rendered = {}
        for key, value in parameters.items():
            if isinstance(value, str) and "{{" in value and "}}" in value:
                template = self.condition_evaluator.jinja_env.from_string(value)
                rendered[key] = template.render(**context)
            else:
                rendered[key] = value
        return rendered
    
    async def aggregate_output(self, workflow: Workflow, execution: WorkflowExecution) -> Dict[str, Any]:
        """聚合工作流输出"""
        output = {
            "workflow_id": workflow.workflow_id,
            "execution_id": execution.execution_id,
            "status": execution.status.value,
            "start_time": execution.start_time.isoformat() if execution.start_time else None,
            "end_time": execution.end_time.isoformat() if execution.end_time else None,
            "stages": {},
            "tasks": {}
        }
        
        # 聚合阶段结果
        for stage_id, status in execution.stage_status.items():
            output["stages"][stage_id] = status.value
        
        # 聚合任务结果
        for task_id, task_execution in execution.task_executions.items():
            output["tasks"][task_id] = {
                "status": task_execution.status.value,
                "output": task_execution.output,
                "start_time": task_execution.start_time.isoformat() if task_execution.start_time else None,
                "end_time": task_execution.end_time.isoformat() if task_execution.end_time else None
            }
        
        return output
    
    def get_workflow(self, workflow_id: str) -> Optional[Workflow]:
        """获取工作流"""
        return self.workflows.get(workflow_id)
    
    def list_workflows(self) -> List[Workflow]:
        """列出所有工作流"""
        return list(self.workflows.values())
    
    def get_execution_status(self, execution_id: str) -> Optional[WorkflowExecution]:
        """获取执行状态"""
        return self.running_executions.get(execution_id)

# 全局工作流引擎实例
workflow_engine = None  # 需要在初始化时设置
