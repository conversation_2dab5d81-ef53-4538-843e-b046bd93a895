#!/usr/bin/env python3
"""
将用户管理迁移到动态模型系统
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import uuid
import json

# 同步数据库连接
SYNC_DATABASE_URL = 'postgresql://postgres:postgres@localhost:5432/bounty_ams'

def create_user_model_type(db):
    """创建用户动态模型类型"""
    print("\n🔧 创建用户动态模型类型...")
    
    # 获取admin用户ID
    result = db.execute(text("SELECT id FROM users WHERE username = 'admin' LIMIT 1"))
    admin_user = result.fetchone()
    admin_user_id = admin_user[0] if admin_user else None
    
    # 检查是否已存在用户模型
    result = db.execute(text("SELECT id FROM model_types WHERE name = 'user'"))
    existing_model = result.fetchone()
    
    if existing_model:
        print("  ℹ️  用户模型类型已存在")
        return existing_model[0]
    
    # 创建用户模型类型
    user_model_id = str(uuid.uuid4())
    
    insert_sql = f"""
    INSERT INTO model_types (id, name, display_name, description, is_system, created_by_user_id)
    VALUES (
        '{user_model_id}',
        'user',
        '系统用户',
        '系统用户管理模型',
        true,
        {'NULL' if not admin_user_id else f"'{admin_user_id}'"}
    )
    """
    
    try:
        db.execute(text(insert_sql))
        print("  ✅ 用户模型类型创建成功")
        return user_model_id
    except Exception as e:
        print(f"  ❌ 用户模型类型创建失败: {e}")
        return None

def create_user_model_fields(db, model_type_id):
    """创建用户模型字段"""
    print("\n🔧 创建用户模型字段...")
    
    user_fields = [
        {
            'field_name': 'username',
            'display_name': '用户名',
            'field_type': 'text',
            'is_required': True,
            'is_unique': True,
            'sort_order': 1,
            'validation_rules': json.dumps({
                'minLength': 3,
                'maxLength': 50,
                'pattern': '^[a-zA-Z0-9_]+$'
            })
        },
        {
            'field_name': 'email',
            'display_name': '邮箱',
            'field_type': 'email',
            'is_required': True,
            'is_unique': True,
            'sort_order': 2,
            'validation_rules': json.dumps({
                'format': 'email'
            })
        },
        {
            'field_name': 'full_name',
            'display_name': '真实姓名',
            'field_type': 'text',
            'is_required': False,
            'is_unique': False,
            'sort_order': 3,
            'validation_rules': json.dumps({
                'maxLength': 100
            })
        },
        {
            'field_name': 'phone',
            'display_name': '手机号',
            'field_type': 'text',
            'is_required': False,
            'is_unique': False,
            'sort_order': 4,
            'validation_rules': json.dumps({
                'pattern': '^[0-9-+()\\s]*$'
            })
        },
        {
            'field_name': 'department',
            'display_name': '部门',
            'field_type': 'text',
            'is_required': False,
            'is_unique': False,
            'sort_order': 5,
            'validation_rules': json.dumps({
                'maxLength': 100
            })
        },
        {
            'field_name': 'position',
            'display_name': '职位',
            'field_type': 'text',
            'is_required': False,
            'is_unique': False,
            'sort_order': 6,
            'validation_rules': json.dumps({
                'maxLength': 100
            })
        },
        {
            'field_name': 'avatar_url',
            'display_name': '头像URL',
            'field_type': 'url',
            'is_required': False,
            'is_unique': False,
            'sort_order': 7,
            'validation_rules': json.dumps({
                'format': 'url'
            })
        },
        {
            'field_name': 'is_admin',
            'display_name': '管理员权限',
            'field_type': 'boolean',
            'is_required': True,
            'is_unique': False,
            'sort_order': 8,
            'validation_rules': json.dumps({})
        },
        {
            'field_name': 'is_active',
            'display_name': '账户状态',
            'field_type': 'boolean',
            'is_required': True,
            'is_unique': False,
            'sort_order': 9,
            'validation_rules': json.dumps({})
        },
        {
            'field_name': 'notes',
            'display_name': '备注',
            'field_type': 'textarea',
            'is_required': False,
            'is_unique': False,
            'sort_order': 10,
            'validation_rules': json.dumps({
                'maxLength': 500
            })
        }
    ]
    
    for field in user_fields:
        field_id = str(uuid.uuid4())
        
        insert_sql = f"""
        INSERT INTO model_fields (
            id, model_type_id, field_name, display_name, field_type,
            is_required, is_unique, sort_order, validation_rules
        ) VALUES (
            '{field_id}',
            '{model_type_id}',
            '{field['field_name']}',
            '{field['display_name']}',
            '{field['field_type']}',
            {field['is_required']},
            {field['is_unique']},
            {field['sort_order']},
            '{field['validation_rules']}'::jsonb
        )
        """
        
        try:
            db.execute(text(insert_sql))
            print(f"  ✅ 字段 {field['display_name']} 创建成功")
        except Exception as e:
            print(f"  ❌ 字段 {field['display_name']} 创建失败: {e}")

def migrate_existing_users(db, model_type_id):
    """迁移现有用户到动态模型"""
    print("\n🔧 迁移现有用户到动态模型...")
    
    # 获取所有现有用户
    result = db.execute(text("""
        SELECT id, username, email, full_name, phone, department, position,
               avatar_url, is_admin, is_active, notes, created_at, created_by_user_id
        FROM users
    """))
    
    users = list(result)
    print(f"  找到 {len(users)} 个用户需要迁移")
    
    for user in users:
        user_id, username, email, full_name, phone, department, position, \
        avatar_url, is_admin, is_active, notes, created_at, created_by_user_id = user
        
        # 构建entity_data
        entity_data = {
            'username': username,
            'email': email,
            'full_name': full_name,
            'phone': phone,
            'department': department,
            'position': position,
            'avatar_url': avatar_url,
            'is_admin': is_admin,
            'is_active': is_active,
            'notes': notes
        }
        
        # 移除None值
        entity_data = {k: v for k, v in entity_data.items() if v is not None}
        
        # 创建动态实体
        dynamic_entity_id = str(uuid.uuid4())
        
        insert_sql = f"""
        INSERT INTO dynamic_entities (
            id, model_type_id, entity_data, created_at, created_by_user_id
        ) VALUES (
            '{dynamic_entity_id}',
            '{model_type_id}',
            '{json.dumps(entity_data)}'::jsonb,
            '{created_at}',
            {'NULL' if not created_by_user_id else f"'{created_by_user_id}'"}
        )
        """
        
        try:
            db.execute(text(insert_sql))
            print(f"  ✅ 用户 {username} 迁移成功")
        except Exception as e:
            print(f"  ❌ 用户 {username} 迁移失败: {e}")

def create_role_model_type(db):
    """创建角色动态模型类型"""
    print("\n🔧 创建角色动态模型类型...")
    
    # 获取admin用户ID
    result = db.execute(text("SELECT id FROM users WHERE username = 'admin' LIMIT 1"))
    admin_user = result.fetchone()
    admin_user_id = admin_user[0] if admin_user else None
    
    # 检查是否已存在角色模型
    result = db.execute(text("SELECT id FROM model_types WHERE name = 'user_role'"))
    existing_model = result.fetchone()
    
    if existing_model:
        print("  ℹ️  角色模型类型已存在")
        return existing_model[0]
    
    # 创建角色模型类型
    role_model_id = str(uuid.uuid4())
    
    insert_sql = f"""
    INSERT INTO model_types (id, name, display_name, description, is_system, created_by_user_id)
    VALUES (
        '{role_model_id}',
        'user_role',
        '用户角色',
        '用户角色管理模型',
        true,
        {'NULL' if not admin_user_id else f"'{admin_user_id}'"}
    )
    """
    
    try:
        db.execute(text(insert_sql))
        print("  ✅ 角色模型类型创建成功")
        return role_model_id
    except Exception as e:
        print(f"  ❌ 角色模型类型创建失败: {e}")
        return None

def create_role_model_fields(db, model_type_id):
    """创建角色模型字段"""
    print("\n🔧 创建角色模型字段...")
    
    role_fields = [
        {
            'field_name': 'name',
            'display_name': '角色名称',
            'field_type': 'text',
            'is_required': True,
            'is_unique': True,
            'sort_order': 1,
            'validation_rules': json.dumps({
                'minLength': 2,
                'maxLength': 50
            })
        },
        {
            'field_name': 'display_name',
            'display_name': '显示名称',
            'field_type': 'text',
            'is_required': True,
            'is_unique': False,
            'sort_order': 2,
            'validation_rules': json.dumps({
                'maxLength': 100
            })
        },
        {
            'field_name': 'description',
            'display_name': '描述',
            'field_type': 'textarea',
            'is_required': False,
            'is_unique': False,
            'sort_order': 3,
            'validation_rules': json.dumps({
                'maxLength': 500
            })
        },
        {
            'field_name': 'permissions',
            'display_name': '权限列表',
            'field_type': 'json',
            'is_required': True,
            'is_unique': False,
            'sort_order': 4,
            'validation_rules': json.dumps({
                'type': 'array'
            })
        },
        {
            'field_name': 'is_system',
            'display_name': '系统角色',
            'field_type': 'boolean',
            'is_required': True,
            'is_unique': False,
            'sort_order': 5,
            'validation_rules': json.dumps({})
        },
        {
            'field_name': 'is_active',
            'display_name': '启用状态',
            'field_type': 'boolean',
            'is_required': True,
            'is_unique': False,
            'sort_order': 6,
            'validation_rules': json.dumps({})
        }
    ]
    
    for field in role_fields:
        field_id = str(uuid.uuid4())
        
        insert_sql = f"""
        INSERT INTO model_fields (
            id, model_type_id, field_name, display_name, field_type,
            is_required, is_unique, sort_order, validation_rules
        ) VALUES (
            '{field_id}',
            '{model_type_id}',
            '{field['field_name']}',
            '{field['display_name']}',
            '{field['field_type']}',
            {field['is_required']},
            {field['is_unique']},
            {field['sort_order']},
            '{field['validation_rules']}'::jsonb
        )
        """
        
        try:
            db.execute(text(insert_sql))
            print(f"  ✅ 字段 {field['display_name']} 创建成功")
        except Exception as e:
            print(f"  ❌ 字段 {field['display_name']} 创建失败: {e}")

def migrate_existing_roles(db, model_type_id):
    """迁移现有角色到动态模型"""
    print("\n🔧 迁移现有角色到动态模型...")
    
    # 获取所有现有角色
    result = db.execute(text("""
        SELECT id, name, display_name, description, permissions, is_system, is_active, created_at, created_by_user_id
        FROM roles
    """))
    
    roles = list(result)
    print(f"  找到 {len(roles)} 个角色需要迁移")
    
    for role in roles:
        role_id, name, display_name, description, permissions, is_system, is_active, created_at, created_by_user_id = role
        
        # 构建entity_data
        entity_data = {
            'name': name,
            'display_name': display_name,
            'description': description,
            'permissions': permissions,
            'is_system': is_system,
            'is_active': is_active
        }
        
        # 移除None值
        entity_data = {k: v for k, v in entity_data.items() if v is not None}
        
        # 创建动态实体
        dynamic_entity_id = str(uuid.uuid4())
        
        insert_sql = f"""
        INSERT INTO dynamic_entities (
            id, model_type_id, entity_data, created_at, created_by_user_id
        ) VALUES (
            '{dynamic_entity_id}',
            '{model_type_id}',
            '{json.dumps(entity_data, default=str)}'::jsonb,
            '{created_at}',
            {'NULL' if not created_by_user_id else f"'{created_by_user_id}'"}
        )
        """
        
        try:
            db.execute(text(insert_sql))
            print(f"  ✅ 角色 {display_name} 迁移成功")
        except Exception as e:
            print(f"  ❌ 角色 {display_name} 迁移失败: {e}")

def main():
    """主函数"""
    try:
        engine = create_engine(SYNC_DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        with SessionLocal() as db:
            print("🔧 将用户管理迁移到动态模型系统")
            print("=" * 50)
            
            # 1. 创建用户动态模型
            user_model_id = create_user_model_type(db)
            if user_model_id:
                create_user_model_fields(db, user_model_id)
                migrate_existing_users(db, user_model_id)
            
            # 2. 创建角色动态模型
            role_model_id = create_role_model_type(db)
            if role_model_id:
                create_role_model_fields(db, role_model_id)
                migrate_existing_roles(db, role_model_id)
            
            # 提交所有更改
            db.commit()
            
            print(f"\n✅ 用户管理迁移到动态模型系统完成")
            print("\n📋 迁移总结:")
            print("1. ✅ 创建了用户动态模型类型和字段")
            print("2. ✅ 创建了角色动态模型类型和字段")
            print("3. ✅ 迁移了现有用户数据")
            print("4. ✅ 迁移了现有角色数据")
            print("5. ✅ 保持了数据完整性")
            
            print(f"\n🚀 现在用户和角色都使用动态模型系统管理")
            
    except Exception as e:
        print(f"❌ 迁移过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
