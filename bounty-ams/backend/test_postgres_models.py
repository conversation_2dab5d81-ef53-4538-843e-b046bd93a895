#!/usr/bin/env python3
"""
测试PostgreSQL模型关系
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_postgres_connection():
    """测试PostgreSQL连接和模型"""
    print("🔧 测试PostgreSQL连接和模型...")
    
    try:
        from sqlalchemy import create_engine, text
        from sqlalchemy.orm import sessionmaker
        from models_dynamic import User, Role, UserRole, UserActivityLog
        
        SYNC_DATABASE_URL = 'postgresql://postgres:postgres@localhost:5432/bounty_ams'
        engine = create_engine(SYNC_DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        with SessionLocal() as session:
            # 简单查询测试
            result = session.execute(text("SELECT 1"))
            print("  ✅ 数据库连接成功")
            
            # 测试用户查询
            users = session.query(User).limit(1).all()
            print(f"  ✅ 用户查询成功: {len(users)} 个用户")
            
            # 测试角色查询
            roles = session.query(Role).limit(1).all()
            print(f"  ✅ 角色查询成功: {len(roles)} 个角色")
            
            # 测试用户角色关联查询
            user_roles = session.query(UserRole).limit(1).all()
            print(f"  ✅ 用户角色关联查询成功: {len(user_roles)} 个关联")
            
            # 测试活动日志查询
            activity_logs = session.query(UserActivityLog).limit(1).all()
            print(f"  ✅ 活动日志查询成功: {len(activity_logs)} 个日志")
            
        return True
        
    except Exception as e:
        print(f"  ❌ PostgreSQL测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_with_relationships():
    """测试用户关系查询"""
    print("\n🔧 测试用户关系查询...")
    
    try:
        from sqlalchemy import create_engine
        from sqlalchemy.orm import sessionmaker, selectinload
        from models_dynamic import User, Role, UserRole
        
        SYNC_DATABASE_URL = 'postgresql://postgres:postgres@localhost:5432/bounty_ams'
        engine = create_engine(SYNC_DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        with SessionLocal() as session:
            # 查询admin用户及其角色
            admin_user = session.query(User).options(
                selectinload(User.user_roles).selectinload(UserRole.role)
            ).filter(User.username == 'admin').first()
            
            if admin_user:
                print(f"  ✅ 找到admin用户: {admin_user.username}")
                print(f"  ✅ 用户角色数量: {len(admin_user.user_roles)}")
                for user_role in admin_user.user_roles:
                    print(f"    - 角色: {user_role.role.display_name}")
            else:
                print("  ⚠️  未找到admin用户")
            
        return True
        
    except Exception as e:
        print(f"  ❌ 用户关系查询失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 PostgreSQL模型关系测试")
    print("=" * 50)
    
    success_count = 0
    total_tests = 2
    
    # 测试PostgreSQL连接
    if test_postgres_connection():
        success_count += 1
    
    # 测试用户关系查询
    if test_user_with_relationships():
        success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 PostgreSQL模型关系测试通过！")
        print("✅ 模型关系配置正确，可以重启服务")
    else:
        print("⚠️  部分测试失败，需要检查配置")
    
    return 0 if success_count == total_tests else 1

if __name__ == "__main__":
    exit(main())
