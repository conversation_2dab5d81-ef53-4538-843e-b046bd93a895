#!/usr/bin/env python3
"""
测试资产管理页面是否真正使用PostgreSQL数据
"""

import requests
import json

def test_frontend_api_usage():
    """测试前端API使用情况"""
    
    print("🔍 测试资产管理页面是否使用PostgreSQL数据...")
    
    # 登录
    login_response = requests.post('http://localhost:8000/api/auth/login', json={
        'username': 'admin',
        'password': 'password'
    })
    
    if login_response.status_code != 200:
        print("❌ 登录失败")
        return
    
    token = login_response.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    
    print("\n1️⃣ 测试统一资产API（PostgreSQL）")
    # 测试新的统一资产API
    response = requests.get('http://localhost:8000/api/unified-assets/search?limit=3', headers=headers)
    if response.status_code == 200:
        data = response.json()
        assets = data.get('assets', [])
        print(f"   ✅ 成功获取 {len(assets)} 条资产")
        print(f"   📊 总数: {data.get('total', 0)}")
        if assets:
            asset = assets[0]
            print(f"   🔍 示例字段: asset_type={asset.get('asset_type')}, confidence={asset.get('confidence')}")
            print(f"   📝 PostgreSQL字段: asset_id={asset.get('asset_id', 'N/A')}")
    else:
        print(f"   ❌ 失败: {response.status_code}")
    
    print("\n2️⃣ 测试资产类型统计API")
    response = requests.get('http://localhost:8000/api/unified-assets/types', headers=headers)
    if response.status_code == 200:
        data = response.json()
        types = data.get('types', [])
        print(f"   ✅ 获取到 {len(types)} 种资产类型")
        for t in types[:3]:
            print(f"     - {t['type']}: {t['count']} 个")
    else:
        print(f"   ❌ 失败: {response.status_code}")
    
    print("\n3️⃣ 测试资产来源统计API") 
    response = requests.get('http://localhost:8000/api/unified-assets/sources', headers=headers)
    if response.status_code == 200:
        data = response.json()
        sources = data.get('sources', [])
        print(f"   ✅ 获取到 {len(sources)} 种发现来源")
        for s in sources[:3]:
            print(f"     - {s['source']}: {s['count']} 个")
    else:
        print(f"   ❌ 失败: {response.status_code}")
    
    print("\n4️⃣ 测试统计信息API")
    response = requests.get('http://localhost:8000/api/unified-assets/stats', headers=headers)
    if response.status_code == 200:
        data = response.json()
        basic_stats = data.get('basic_stats', {})
        print(f"   ✅ 统计信息:")
        print(f"     - 总资产数: {basic_stats.get('total_assets', 0)}")
        print(f"     - 唯一主机: {basic_stats.get('unique_hosts', 0)}")
        print(f"     - 平均置信度: {basic_stats.get('avg_confidence', 0):.3f}")
    else:
        print(f"   ❌ 失败: {response.status_code}")
    
    print("\n5️⃣ 测试导出功能API")
    export_params = {
        "query": "",
        "platform_id": None,
        "project_id": None,
        "asset_types": [],
        "confidence": None,
        "filters": {}
    }
    response = requests.post('http://localhost:8000/api/unified-assets/export', 
                           headers=headers, json=export_params)
    if response.status_code == 200:
        data = response.json()
        exported_count = data.get('data', {}).get('total_exported', 0)
        print(f"   ✅ 导出功能正常: 可导出 {exported_count} 条资产")
    else:
        print(f"   ❌ 导出失败: {response.status_code}")
    
    print("\n6️⃣ 检查旧API端点状态")
    old_endpoints = [
        ('/api/discovered-assets/search', '发现的资产搜索'),
        ('/api/enhanced-search/assets', '增强搜索'),
        ('/api/assets', '基础资产API')
    ]
    
    for endpoint, name in old_endpoints:
        response = requests.get(f'http://localhost:8000{endpoint}', headers=headers)
        status_emoji = "🟢" if response.status_code == 200 else "🔴" if response.status_code >= 400 else "🟡"
        print(f"   {status_emoji} {name}: {response.status_code}")
    
    print("\n📋 结论:")
    print("✅ 新的统一资产API (PostgreSQL) 已部署并正常工作")
    print("✅ 前端应该使用 /api/unified-assets/* 端点")
    print("⚠️  请确认前端页面实际加载时使用的是新API")

if __name__ == "__main__":
    test_frontend_api_usage()