"""
智能任务调度器 - 负责任务的智能分发和执行管理
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import logging
import heapq
from collections import defaultdict

from task_template_engine import TaskExecution, TaskStatus

logger = logging.getLogger(__name__)

class AgentStatus(Enum):
    ONLINE = "online"
    OFFLINE = "offline"
    BUSY = "busy"
    MAINTENANCE = "maintenance"

@dataclass
class AgentCapability:
    """Agent能力定义"""
    tool_name: str
    version: str
    supported_formats: List[str]
    max_concurrent: int
    resource_requirements: Dict[str, str]
    performance_score: float = 1.0  # 性能评分

@dataclass
class Agent:
    """Agent信息"""
    agent_id: str
    name: str
    status: AgentStatus
    capabilities: List[AgentCapability]
    resource_limits: Dict[str, str]
    current_load: int = 0
    max_capacity: int = 10
    last_heartbeat: datetime = field(default_factory=datetime.utcnow)
    location: Optional[str] = None
    tags: List[str] = field(default_factory=list)
    performance_history: Dict[str, List[float]] = field(default_factory=dict)

@dataclass
class TaskPriority:
    """任务优先级"""
    HIGH = 1
    MEDIUM = 2
    LOW = 3

@dataclass
class QueuedTask:
    """队列中的任务"""
    task: TaskExecution
    priority: int
    queued_at: datetime
    required_capabilities: List[str]
    estimated_duration: int = 300  # 预估执行时间(秒)
    
    def __lt__(self, other):
        # 优先级队列排序：优先级高的先执行，同优先级按入队时间
        if self.priority != other.priority:
            return self.priority < other.priority
        return self.queued_at < other.queued_at

class AgentManager:
    """Agent管理器"""
    
    def __init__(self):
        self.agents: Dict[str, Agent] = {}
        self.agent_tasks: Dict[str, List[str]] = defaultdict(list)  # agent_id -> task_ids
        self.heartbeat_timeout = timedelta(minutes=5)
    
    def register_agent(self, agent: Agent):
        """注册Agent"""
        self.agents[agent.agent_id] = agent
        logger.info(f"Agent注册成功: {agent.agent_id}")
    
    def update_agent_heartbeat(self, agent_id: str):
        """更新Agent心跳"""
        if agent_id in self.agents:
            self.agents[agent_id].last_heartbeat = datetime.utcnow()
            if self.agents[agent_id].status == AgentStatus.OFFLINE:
                self.agents[agent_id].status = AgentStatus.ONLINE
    
    def get_available_agents(self) -> List[Agent]:
        """获取可用的Agent列表"""
        now = datetime.utcnow()
        available = []
        
        for agent in self.agents.values():
            # 检查心跳超时
            if now - agent.last_heartbeat > self.heartbeat_timeout:
                agent.status = AgentStatus.OFFLINE
                continue
            
            # 只返回在线且未满载的Agent
            if agent.status == AgentStatus.ONLINE and agent.current_load < agent.max_capacity:
                available.append(agent)
        
        return available
    
    def agent_has_capability(self, agent: Agent, required_tools: List[str]) -> bool:
        """检查Agent是否具备所需能力"""
        agent_tools = {cap.tool_name for cap in agent.capabilities}
        return all(tool in agent_tools for tool in required_tools)
    
    def get_agent_capability(self, agent: Agent, tool_name: str) -> Optional[AgentCapability]:
        """获取Agent的特定工具能力"""
        for cap in agent.capabilities:
            if cap.tool_name == tool_name:
                return cap
        return None
    
    def update_agent_load(self, agent_id: str, delta: int):
        """更新Agent负载"""
        if agent_id in self.agents:
            self.agents[agent_id].current_load += delta
            self.agents[agent_id].current_load = max(0, self.agents[agent_id].current_load)
    
    def record_task_performance(self, agent_id: str, template_id: str, execution_time: float):
        """记录任务执行性能"""
        if agent_id in self.agents:
            agent = self.agents[agent_id]
            if template_id not in agent.performance_history:
                agent.performance_history[template_id] = []
            
            # 保留最近10次执行记录
            agent.performance_history[template_id].append(execution_time)
            if len(agent.performance_history[template_id]) > 10:
                agent.performance_history[template_id].pop(0)
    
    def get_agent_performance_score(self, agent: Agent, template_id: str) -> float:
        """获取Agent性能评分"""
        if template_id in agent.performance_history:
            history = agent.performance_history[template_id]
            if history:
                # 基于平均执行时间计算评分，执行时间越短评分越高
                avg_time = sum(history) / len(history)
                return max(0.1, 1.0 / (1.0 + avg_time / 300))  # 300秒为基准
        return 0.5  # 默认评分

class IntelligentTaskScheduler:
    """智能任务调度器"""
    
    def __init__(self, agent_manager: AgentManager):
        self.agent_manager = agent_manager
        self.task_queue: List[QueuedTask] = []
        self.running_tasks: Dict[str, TaskExecution] = {}
        self.completed_tasks: Dict[str, TaskExecution] = {}
        self.scheduler_running = False
        self.max_queue_size = 1000
    
    async def start_scheduler(self):
        """启动调度器"""
        self.scheduler_running = True
        asyncio.create_task(self.scheduler_loop())
        logger.info("任务调度器已启动")
    
    async def stop_scheduler(self):
        """停止调度器"""
        self.scheduler_running = False
        logger.info("任务调度器已停止")
    
    async def scheduler_loop(self):
        """调度器主循环"""
        while self.scheduler_running:
            try:
                await self.process_queue()
                await asyncio.sleep(1)  # 1秒检查一次
            except Exception as e:
                logger.error(f"调度器循环错误: {e}")
                await asyncio.sleep(5)
    
    async def submit_task(self, task: TaskExecution, priority: int = TaskPriority.MEDIUM) -> TaskExecution:
        """提交任务"""
        # 检查队列大小
        if len(self.task_queue) >= self.max_queue_size:
            raise Exception("任务队列已满")
        
        # 分析任务所需能力
        required_capabilities = self.analyze_task_requirements(task)
        
        # 尝试立即分配
        agent = await self.select_best_agent(task, required_capabilities)
        
        if agent:
            # 立即执行
            return await self.execute_task_on_agent(task, agent)
        else:
            # 加入队列
            queued_task = QueuedTask(
                task=task,
                priority=priority,
                queued_at=datetime.utcnow(),
                required_capabilities=required_capabilities
            )
            
            heapq.heappush(self.task_queue, queued_task)
            task.status = TaskStatus.PENDING
            logger.info(f"任务已加入队列: {task.execution_id}")
            
            return task
    
    def analyze_task_requirements(self, task: TaskExecution) -> List[str]:
        """分析任务所需的工具能力"""
        # 这里需要根据任务模板分析所需工具
        # 简化实现，实际应该从模板中提取
        template_tool_mapping = {
            "subdomain_discovery_v1": ["subfinder", "assetfinder"],
            "port_scan_v1": ["nmap"],
            "service_detection_v1": ["nmap", "httpx"],
            "web_screenshot_v1": ["httpx", "chromium"],
            "nuclei_scan_v1": ["nuclei"]
        }
        
        return template_tool_mapping.get(task.template_id, [])
    
    async def select_best_agent(self, task: TaskExecution, required_capabilities: List[str]) -> Optional[Agent]:
        """选择最佳Agent"""
        available_agents = self.agent_manager.get_available_agents()
        
        # 过滤具备所需能力的Agent
        capable_agents = [
            agent for agent in available_agents
            if self.agent_manager.agent_has_capability(agent, required_capabilities)
        ]
        
        if not capable_agents:
            return None
        
        # 计算每个Agent的评分
        scored_agents = []
        for agent in capable_agents:
            score = self.calculate_agent_score(agent, task)
            scored_agents.append((score, agent))
        
        # 返回评分最高的Agent
        if scored_agents:
            return max(scored_agents, key=lambda x: x[0])[1]
        
        return None
    
    def calculate_agent_score(self, agent: Agent, task: TaskExecution) -> float:
        """计算Agent评分"""
        # 负载评分 (负载越低评分越高)
        load_score = 1.0 - (agent.current_load / agent.max_capacity)
        
        # 性能评分 (基于历史执行时间)
        performance_score = self.agent_manager.get_agent_performance_score(agent, task.template_id)
        
        # 能力匹配评分
        capability_score = self.calculate_capability_score(agent, task)
        
        # 地理位置评分 (如果有偏好)
        location_score = self.calculate_location_score(agent, task)
        
        # 综合评分
        total_score = (
            load_score * 0.3 +
            performance_score * 0.4 +
            capability_score * 0.2 +
            location_score * 0.1
        )
        
        return total_score
    
    def calculate_capability_score(self, agent: Agent, task: TaskExecution) -> float:
        """计算能力匹配评分"""
        required_tools = self.analyze_task_requirements(task)
        if not required_tools:
            return 1.0
        
        total_score = 0.0
        for tool in required_tools:
            capability = self.agent_manager.get_agent_capability(agent, tool)
            if capability:
                # 基于工具性能评分
                total_score += capability.performance_score
            else:
                return 0.0  # 缺少必需工具
        
        return total_score / len(required_tools)
    
    def calculate_location_score(self, agent: Agent, task: TaskExecution) -> float:
        """计算地理位置评分"""
        # 简化实现，实际可以根据网络延迟、时区等因素计算
        return 1.0
    
    async def process_queue(self):
        """处理任务队列"""
        while self.task_queue:
            # 获取优先级最高的任务
            queued_task = heapq.heappop(self.task_queue)
            
            # 尝试分配Agent
            agent = await self.select_best_agent(queued_task.task, queued_task.required_capabilities)
            
            if agent:
                # 执行任务
                await self.execute_task_on_agent(queued_task.task, agent)
            else:
                # 重新放回队列
                heapq.heappush(self.task_queue, queued_task)
                break  # 没有可用Agent，等待下次循环
    
    async def execute_task_on_agent(self, task: TaskExecution, agent: Agent) -> TaskExecution:
        """在指定Agent上执行任务"""
        try:
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            task.agent_id = agent.agent_id
            task.start_time = datetime.utcnow()
            
            # 更新Agent负载
            self.agent_manager.update_agent_load(agent.agent_id, 1)
            
            # 记录运行中的任务
            self.running_tasks[task.execution_id] = task
            
            # 发送任务到Agent (这里需要实现具体的通信逻辑)
            result = await self.send_task_to_agent(task, agent)
            
            # 更新任务结果
            task.status = TaskStatus.SUCCESS if result.get("success") else TaskStatus.FAILED
            task.end_time = datetime.utcnow()
            task.output = result.get("output")
            task.error = result.get("error")
            
            # 记录性能数据
            if task.start_time and task.end_time:
                execution_time = (task.end_time - task.start_time).total_seconds()
                self.agent_manager.record_task_performance(
                    agent.agent_id, task.template_id, execution_time
                )
            
            # 移动到完成列表
            self.completed_tasks[task.execution_id] = task
            if task.execution_id in self.running_tasks:
                del self.running_tasks[task.execution_id]
            
            logger.info(f"任务执行完成: {task.execution_id}, 状态: {task.status}")
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.end_time = datetime.utcnow()
            task.error = str(e)
            logger.error(f"任务执行失败: {task.execution_id}, 错误: {e}")
        
        finally:
            # 减少Agent负载
            self.agent_manager.update_agent_load(agent.agent_id, -1)
        
        return task
    
    async def send_task_to_agent(self, task: TaskExecution, agent: Agent) -> Dict[str, Any]:
        """发送任务到Agent执行"""
        # 这里需要实现与Agent的通信逻辑
        # 可以使用HTTP API、消息队列、gRPC等方式
        
        # 模拟任务执行
        await asyncio.sleep(2)  # 模拟执行时间
        
        return {
            "success": True,
            "output": {
                "result": f"模拟执行结果 for {task.template_id}",
                "execution_time": 2.0
            }
        }
    
    def get_task_status(self, execution_id: str) -> Optional[TaskExecution]:
        """获取任务状态"""
        # 检查运行中的任务
        if execution_id in self.running_tasks:
            return self.running_tasks[execution_id]
        
        # 检查已完成的任务
        if execution_id in self.completed_tasks:
            return self.completed_tasks[execution_id]
        
        # 检查队列中的任务
        for queued_task in self.task_queue:
            if queued_task.task.execution_id == execution_id:
                return queued_task.task
        
        return None
    
    def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        return {
            "queue_size": len(self.task_queue),
            "running_tasks": len(self.running_tasks),
            "completed_tasks": len(self.completed_tasks),
            "available_agents": len(self.agent_manager.get_available_agents()),
            "total_agents": len(self.agent_manager.agents)
        }
    
    async def cancel_task(self, execution_id: str) -> bool:
        """取消任务"""
        # 从队列中移除
        self.task_queue = [qt for qt in self.task_queue if qt.task.execution_id != execution_id]
        heapq.heapify(self.task_queue)
        
        # 取消运行中的任务 (需要实现Agent通信)
        if execution_id in self.running_tasks:
            task = self.running_tasks[execution_id]
            task.status = TaskStatus.CANCELLED
            task.end_time = datetime.utcnow()
            
            # 减少Agent负载
            if task.agent_id:
                self.agent_manager.update_agent_load(task.agent_id, -1)
            
            # 移动到完成列表
            self.completed_tasks[execution_id] = task
            del self.running_tasks[execution_id]
            
            return True
        
        return False

# 全局调度器实例
task_scheduler = None  # 需要在初始化时设置
