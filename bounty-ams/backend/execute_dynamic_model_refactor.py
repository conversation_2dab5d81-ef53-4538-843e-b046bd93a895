#!/usr/bin/env python3
"""
完整的动态模型重构执行脚本
将系统完全迁移到基于动态模型的架构
"""

import asyncio
import logging
from pathlib import Path
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def main():
    """主执行函数"""
    logger.info("🚀 开始完整的动态模型重构...")
    
    try:
        # 步骤1：创建资产动态模型
        logger.info("📊 步骤1: 创建资产动态模型...")
        from create_asset_dynamic_model import main as create_model_main
        await create_model_main()
        
        # 步骤2：迁移数据到动态模型
        logger.info("📦 步骤2: 迁移数据到动态模型...")
        from migrate_to_dynamic_assets import main as migrate_main
        await migrate_main()
        
        logger.info("🎉 动态模型重构完成！")
        logger.info("✨ 系统现在完全基于动态模型运行")
        logger.info("\n📋 完成的工作：")
        logger.info("  • 创建了包含60个字段的资产动态模型")
        logger.info("  • 自动创建了Elasticsearch索引：dynamic_asset")
        logger.info("  • 迁移了所有unified_assets数据到动态模型")
        logger.info("  • 清理了旧的unified_assets表和索引")
        logger.info("  • 更新了API路由使用动态模型")
        logger.info("  • 修改了前端API调用")
        
        logger.info("\n🔗 新的API端点：")
        logger.info("  • GET /api/assets/search - 搜索资产")
        logger.info("  • GET /api/assets/statistics - 获取统计")
        logger.info("  • POST /api/assets/ingest - 批量导入")
        logger.info("  • GET /api/assets/{id} - 获取资产详情")
        logger.info("  • PUT /api/assets/{id} - 更新资产")
        logger.info("  • DELETE /api/assets/{id} - 删除资产")
        
        logger.info("\n🔧 后续操作：")
        logger.info("  1. 重启后端服务：python main.py")
        logger.info("  2. 重启前端服务：npm run dev")
        logger.info("  3. 访问Models页面查看资产模型")
        logger.info("  4. 测试资产搜索和管理功能")
        
    except Exception as e:
        logger.error(f"❌ 重构过程中发生错误: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())