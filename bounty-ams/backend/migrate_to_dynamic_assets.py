#!/usr/bin/env python3
"""
将unified_assets数据迁移到动态模型系统
完整迁移45+字段到dynamic_asset模型
"""

import asyncio
import logging
from sqlalchemy import select, text
from sqlalchemy.ext.asyncio import AsyncSession
from database import AsyncSessionLocal
from models_dynamic import ModelType, DynamicEntity, User
from elasticsearch_client import get_es_client
import json
from datetime import datetime
import uuid

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def migrate_unified_assets_to_dynamic_model():
    """迁移unified_assets数据到动态模型"""
    async with AsyncSessionLocal() as db:
        # 获取asset模型类型
        asset_model_query = select(ModelType).where(ModelType.name == 'asset')
        asset_model_result = await db.execute(asset_model_query)
        asset_model = asset_model_result.scalar_one_or_none()
        
        if not asset_model:
            logger.error("Asset model type not found. Please run create_asset_dynamic_model.py first")
            return False
        
        # 获取admin用户
        admin_query = select(User).where(User.username == 'admin')
        admin_result = await db.execute(admin_query)
        admin_user = admin_result.scalar_one_or_none()
        
        if not admin_user:
            logger.error("Admin user not found")
            return False
        
        # 检查unified_assets表是否存在
        check_table_query = text("""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.tables 
                WHERE table_name = 'unified_assets'
            )
        """)
        
        table_exists_result = await db.execute(check_table_query)
        table_exists = table_exists_result.scalar()
        
        if not table_exists:
            logger.info("unified_assets table does not exist, skipping migration")
            return True
        
        # 获取unified_assets数据
        unified_assets_query = text("""
            SELECT * FROM unified_assets
            ORDER BY created_at DESC
        """)
        
        unified_assets_result = await db.execute(unified_assets_query)
        unified_assets = unified_assets_result.fetchall()
        
        logger.info(f"Found {len(unified_assets)} unified assets to migrate")
        
        migrated_count = 0
        skipped_count = 0
        
        for asset_row in unified_assets:
            try:
                # 将数据库行转换为字典
                asset_dict = dict(asset_row._mapping)
                
                # 检查是否已经迁移过（根据asset_id）
                if asset_dict.get('asset_id'):
                    existing_query = select(DynamicEntity).where(
                        DynamicEntity.model_type_id == asset_model.id,
                        DynamicEntity.entity_data.op('->>')('asset_id') == asset_dict['asset_id']
                    )
                    existing_result = await db.execute(existing_query)
                    existing_entity = existing_result.scalar_one_or_none()
                    
                    if existing_entity:
                        logger.debug(f"Asset {asset_dict['asset_id']} already migrated, skipping")
                        skipped_count += 1
                        continue
                
                # 构建entity_data
                entity_data = {}
                
                # 所有字段映射
                field_mappings = {
                    'asset_id': 'asset_id',
                    'fingerprint_hash': 'fingerprint_hash',
                    'asset_type': 'asset_type',
                    'asset_value': 'asset_value',
                    'asset_host': 'asset_host',
                    'asset_port': 'asset_port',
                    'asset_service': 'asset_service',
                    'ip_address': 'ip_address',
                    'url': 'url',
                    'transport_protocol': 'transport_protocol',
                    'scan_time': 'scan_time',
                    'website_path': 'website_path',
                    'asn_number': 'asn_number',
                    'asn_organization': 'asn_organization',
                    'international_domain': 'international_domain',
                    'website_host': 'website_host',
                    'icp_unit': 'icp_unit',
                    'icp_number': 'icp_number',
                    'icp_update_time': 'icp_update_time',
                    'website_keywords': 'website_keywords',
                    'website_status_code': 'website_status_code',
                    'website_type': 'website_type',
                    'webpage_title': 'webpage_title',
                    'application_name': 'application_name',
                    'application_level': 'application_level',
                    'application_vendor': 'application_vendor',
                    'application_category': 'application_category',
                    'application_type': 'application_type',
                    'service_product': 'service_product',
                    'service_raw_response': 'service_raw_response',
                    'service_name': 'service_name',
                    'service_version': 'service_version',
                    'service_certificate': 'service_certificate',
                    'country_cn': 'country_cn',
                    'province_cn': 'province_cn',
                    'city_cn': 'city_cn',
                    'district_cn': 'district_cn',
                    'country_en': 'country_en',
                    'province_en': 'province_en',
                    'city_en': 'city_en',
                    'district_en': 'district_en',
                    'isp': 'isp',
                    'certificate_subject': 'certificate_subject',
                    'certificate_subject_country': 'certificate_subject_country',
                    'certificate_subject_organization': 'certificate_subject_organization',
                    'certificate_subject_common_name': 'certificate_subject_common_name',
                    'certificate_issuer': 'certificate_issuer',
                    'certificate_serial_number': 'certificate_serial_number',
                    'confidence': 'confidence',
                    'status': 'status',
                    'source': 'source',
                    'source_task_id': 'source_task_id',
                    'source_task_type': 'source_task_type',
                    'platform_id': 'platform_id',
                    'project_id': 'project_id',
                    'discovered_at': 'discovered_at',
                    'verified_at': 'verified_at',
                    'updated_at': 'updated_at',
                    'parent_asset_id': 'parent_asset_id'
                }
                
                # 复制字段数据
                for db_field, entity_field in field_mappings.items():
                    if db_field in asset_dict and asset_dict[db_field] is not None:
                        value = asset_dict[db_field]
                        
                        # 特殊处理datetime类型
                        if isinstance(value, datetime):
                            entity_data[entity_field] = value.isoformat()
                        # 处理tags字段（JSONB）
                        elif db_field == 'tags' and value:
                            try:
                                if isinstance(value, str):
                                    entity_data[entity_field] = json.loads(value)
                                else:
                                    entity_data[entity_field] = value
                            except (json.JSONDecodeError, TypeError):
                                entity_data[entity_field] = []
                        else:
                            entity_data[entity_field] = value
                
                # 确保必填字段有值
                if not entity_data.get('asset_id'):
                    entity_data['asset_id'] = str(uuid.uuid4())
                
                if not entity_data.get('fingerprint_hash'):
                    # 生成简单的指纹哈希
                    import hashlib
                    fingerprint_data = f"{entity_data.get('asset_type', '')}{entity_data.get('asset_value', '')}{entity_data.get('asset_host', '')}{entity_data.get('asset_port', '')}"
                    entity_data['fingerprint_hash'] = hashlib.md5(fingerprint_data.encode()).hexdigest()
                
                if not entity_data.get('asset_type'):
                    entity_data['asset_type'] = 'unknown'
                
                if not entity_data.get('asset_value'):
                    entity_data['asset_value'] = entity_data.get('asset_host', 'unknown')
                
                # 创建动态实体
                dynamic_entity = DynamicEntity(
                    model_type_id=asset_model.id,
                    entity_data=entity_data,
                    created_by_user_id=admin_user.id,
                    created_at=asset_dict.get('created_at', datetime.utcnow()),
                    updated_at=asset_dict.get('updated_at', datetime.utcnow())
                )
                
                db.add(dynamic_entity)
                await db.flush()
                
                # 同步到Elasticsearch
                from routes.dynamic_models import sync_entity_to_elasticsearch
                await sync_entity_to_elasticsearch(dynamic_entity, asset_model)
                
                migrated_count += 1
                
                if migrated_count % 100 == 0:
                    logger.info(f"Migrated {migrated_count} assets...")
                    
            except Exception as e:
                logger.error(f"Failed to migrate asset {asset_dict.get('asset_id', 'unknown')}: {e}")
                continue
        
        await db.commit()
        
        logger.info(f"✅ Migration completed!")
        logger.info(f"   Migrated: {migrated_count} assets")
        logger.info(f"   Skipped: {skipped_count} assets")
        logger.info(f"   Total processed: {len(unified_assets)} assets")
        
        return True

async def cleanup_unified_assets():
    """清理unified_assets相关数据"""
    async with AsyncSessionLocal() as db:
        try:
            # 删除PostgreSQL表
            logger.info("Cleaning up unified_assets table...")
            await db.execute(text("DROP TABLE IF EXISTS unified_assets"))
            await db.commit()
            logger.info("✅ unified_assets table dropped")
            
            # 删除Elasticsearch索引
            logger.info("Cleaning up Elasticsearch unified_assets index...")
            es_client = await get_es_client()
            if await es_client.indices.exists(index="unified_assets"):
                await es_client.indices.delete(index="unified_assets")
                logger.info("✅ unified_assets index deleted")
            else:
                logger.info("unified_assets index does not exist")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
            raise

async def main():
    """主函数"""
    logger.info("🚀 开始迁移unified_assets到动态模型...")
    
    try:
        # 第一步：迁移数据
        success = await migrate_unified_assets_to_dynamic_model()
        
        if success:
            # 第二步：清理旧数据
            logger.info("🧹 开始清理旧的unified_assets数据...")
            await cleanup_unified_assets()
            
            logger.info("🎉 迁移完成！")
            logger.info("现在所有资产数据都存储在动态模型系统中")
            logger.info("数据可以通过 /api/dynamic-models/entities?model_type_id=<asset_model_id> 访问")
        else:
            logger.error("❌ 迁移失败")
            
    except Exception as e:
        logger.error(f"❌ 迁移过程中发生错误: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())