#!/usr/bin/env python3
"""
最终清理重复数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 同步数据库连接
SYNC_DATABASE_URL = 'postgresql://postgres:postgres@localhost:5432/bounty_ams'

def final_platform_cleanup(db):
    """最终清理重复的平台数据"""
    print("\n🔧 最终清理重复的Platform数据...")
    
    # 获取platform模型ID
    result = db.execute(text("""
        SELECT id FROM model_types WHERE name = 'platform'
    """))
    platform_model_id = result.fetchone()[0]
    
    # 手动处理已知的重复平台
    duplicates_to_fix = [
        {
            'name': 'hackerone',
            'keep_id': '5eb256a3-96e6-4576-a113-ee846b61991b',
            'delete_id': 'cb0c9ac5-4c04-4707-ba28-55fa8450d476'
        },
        {
            'name': 'bugcrowd', 
            'keep_id': 'c0fa6a3d-7d4e-4ecc-a43a-2e0dc0b7b5d9',
            'delete_id': '392f2602-ef10-40bb-a9bd-1c5e97320df6'
        }
    ]
    
    for dup in duplicates_to_fix:
        name = dup['name']
        keep_id = dup['keep_id']
        delete_id = dup['delete_id']
        
        print(f"  处理重复平台: {name}")
        print(f"    保留: {keep_id}")
        print(f"    删除: {delete_id}")
        
        # 检查要删除的平台是否有关联项目
        result = db.execute(text(f"""
            SELECT id, entity_data->>'name' as name
            FROM dynamic_entities 
            WHERE model_type_id = (SELECT id FROM model_types WHERE name = 'project')
            AND entity_data->>'platform_id' = '{delete_id}'
        """))
        
        projects = list(result)
        
        if projects:
            print(f"      ⚠️  平台 {delete_id} 有 {len(projects)} 个关联项目，将更新项目关联")
            
            for project in projects:
                project_id = project[0]
                project_name = project[1]
                print(f"        更新项目: {project_name} ({project_id})")
                
                # 获取完整的项目数据
                result = db.execute(text(f"""
                    SELECT entity_data FROM dynamic_entities WHERE id = '{project_id}'
                """))
                entity_data = result.fetchone()[0]
                
                # 更新platform_id
                entity_data['platform_id'] = keep_id
                
                # 构建更新SQL
                update_sql = f"""
                    UPDATE dynamic_entities 
                    SET entity_data = '{str(entity_data).replace("'", '"')}'::json
                    WHERE id = '{project_id}'
                """
                
                try:
                    db.execute(text(update_sql))
                    print(f"          ✅ 项目 {project_name} 关联已更新")
                except Exception as e:
                    print(f"          ❌ 项目 {project_name} 更新失败: {e}")
        
        # 删除重复的平台
        try:
            db.execute(text(f"""
                DELETE FROM dynamic_entities WHERE id = '{delete_id}'
            """))
            print(f"    ✅ 平台 {name} 重复数据已删除")
        except Exception as e:
            print(f"    ❌ 平台 {name} 删除失败: {e}")

def create_application_level_constraints():
    """创建应用层面的唯一性约束建议"""
    print("\n📋 应用层面唯一性约束建议:")
    
    suggestions = [
        "1. 在创建Platform时，检查name字段的唯一性",
        "2. 在创建Project时，检查同一platform_id下name的唯一性", 
        "3. 在创建Project时，检查同一platform_id下external_id的唯一性（如果不为空）",
        "4. 在前端表单中添加实时验证",
        "5. 在API层面添加数据验证逻辑"
    ]
    
    for suggestion in suggestions:
        print(f"  {suggestion}")

def create_summary_report(db):
    """创建总结报告"""
    print("\n📊 数据库唯一性约束检查总结报告")
    print("=" * 50)
    
    # 获取模型ID
    result = db.execute(text("SELECT id FROM model_types WHERE name = 'platform'"))
    platform_model_id = result.fetchone()[0]
    
    result = db.execute(text("SELECT id FROM model_types WHERE name = 'project'"))
    project_model_id = result.fetchone()[0]
    
    # 1. 数据库表级约束
    print("\n✅ 数据库表级约束:")
    print("  - model_types.name: UNIQUE 约束 ✓")
    print("  - 所有表的主键: PRIMARY KEY 约束 ✓")
    print("  - 外键关系: FOREIGN KEY 约束 ✓")
    
    # 2. 动态模型字段约束
    print("\n✅ 动态模型字段约束:")
    print("  - Platform.name: is_unique=True ✓")
    print("  - Platform.name: 验证规则 ^[a-z0-9_]+$ ✓")
    print("  - Project字段: 基本验证规则 ✓")
    
    # 3. 数据库索引
    print("\n✅ 数据库索引:")
    result = db.execute(text("""
        SELECT indexname FROM pg_indexes 
        WHERE tablename = 'dynamic_entities' 
        AND indexname LIKE 'idx_dynamic_entities_%'
    """))
    indexes = [row[0] for row in result]
    for index in indexes:
        print(f"  - {index} ✓")
    
    # 4. 数据唯一性检查
    print("\n🔍 数据唯一性检查:")
    
    # 检查平台重复
    result = db.execute(text(f"""
        SELECT 
            entity_data->>'name' as name,
            COUNT(*) as count
        FROM dynamic_entities 
        WHERE model_type_id = '{platform_model_id}'
        GROUP BY entity_data->>'name'
        HAVING COUNT(*) > 1;
    """))
    
    platform_duplicates = list(result)
    if platform_duplicates:
        print("  ⚠️  Platform名称重复:")
        for row in platform_duplicates:
            print(f"    - {row[0]}: {row[1]}个重复")
    else:
        print("  ✅ Platform名称无重复")
    
    # 检查项目重复
    result = db.execute(text(f"""
        SELECT 
            entity_data->>'platform_id' as platform_id,
            entity_data->>'name' as name,
            COUNT(*) as count
        FROM dynamic_entities 
        WHERE model_type_id = '{project_model_id}'
        GROUP BY entity_data->>'platform_id', entity_data->>'name'
        HAVING COUNT(*) > 1;
    """))
    
    project_duplicates = list(result)
    if project_duplicates:
        print("  ⚠️  Project名称重复:")
        for row in project_duplicates:
            print(f"    - 平台 {row[0]} 项目 {row[1]}: {row[2]}个重复")
    else:
        print("  ✅ 同一平台下Project名称无重复")
    
    # 检查external_id重复
    result = db.execute(text(f"""
        SELECT 
            entity_data->>'platform_id' as platform_id,
            entity_data->>'external_id' as external_id,
            COUNT(*) as count
        FROM dynamic_entities 
        WHERE model_type_id = '{project_model_id}'
        AND entity_data->>'external_id' IS NOT NULL
        AND entity_data->>'external_id' != ''
        GROUP BY entity_data->>'platform_id', entity_data->>'external_id'
        HAVING COUNT(*) > 1;
    """))
    
    external_duplicates = list(result)
    if external_duplicates:
        print("  ⚠️  Project external_id重复:")
        for row in external_duplicates:
            print(f"    - 平台 {row[0]} external_id {row[1]}: {row[2]}个重复")
    else:
        print("  ✅ 同一平台下Project external_id无重复")
    
    # 5. 统计信息
    result = db.execute(text(f"""
        SELECT COUNT(*) FROM dynamic_entities WHERE model_type_id = '{platform_model_id}'
    """))
    platform_count = result.fetchone()[0]
    
    result = db.execute(text(f"""
        SELECT COUNT(*) FROM dynamic_entities WHERE model_type_id = '{project_model_id}'
    """))
    project_count = result.fetchone()[0]
    
    print(f"\n📈 数据统计:")
    print(f"  - Platform总数: {platform_count}")
    print(f"  - Project总数: {project_count}")
    
    # 6. 建议
    print(f"\n💡 建议:")
    if platform_duplicates or project_duplicates or external_duplicates:
        print("  - 需要在应用层面添加唯一性验证逻辑")
        print("  - 建议在前端表单中添加实时重复检查")
        print("  - 考虑在API层面添加数据验证中间件")
    else:
        print("  - 数据唯一性良好，建议维持现有约束机制")
        print("  - 可以考虑添加数据库触发器作为额外保障")

def main():
    """主函数"""
    try:
        engine = create_engine(SYNC_DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        with SessionLocal() as db:
            print("🔧 最终清理重复数据并生成总结报告")
            
            # 1. 最终清理重复平台
            final_platform_cleanup(db)
            
            # 2. 创建应用层约束建议
            create_application_level_constraints()
            
            # 3. 生成总结报告
            create_summary_report(db)
            
            # 提交所有更改
            db.commit()
            
            print(f"\n✅ 最终清理和报告生成完成")
            
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
