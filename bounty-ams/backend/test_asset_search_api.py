#!/usr/bin/env python3
"""
测试资产搜索API
"""

import asyncio
from sqlalchemy import select
from database import AsyncSessionLocal
from models_dynamic import ModelType, DynamicEntity
import json

async def test_asset_search():
    """测试资产搜索功能"""
    async with AsyncSessionLocal() as db:
        # 获取资产模型
        asset_model_query = select(ModelType).where(ModelType.name == 'asset')
        asset_model_result = await db.execute(asset_model_query)
        asset_model = asset_model_result.scalar_one_or_none()
        
        if not asset_model:
            print("❌ Asset model not found")
            return
        
        print(f"✅ Asset model found: {asset_model.id}")
        
        # 获取资产实体
        asset_entities_query = select(DynamicEntity).where(DynamicEntity.model_type_id == asset_model.id)
        asset_entities_result = await db.execute(asset_entities_query)
        asset_entities = asset_entities_result.scalars().all()
        
        print(f"Found {len(asset_entities)} assets:")
        
        # 统计按项目分组的资产数量
        project_counts = {}
        platform_counts = {}
        
        for entity in asset_entities:
            project_id = entity.entity_data.get('project_id')
            platform_id = entity.entity_data.get('platform_id')
            
            if project_id:
                project_counts[project_id] = project_counts.get(project_id, 0) + 1
            
            if platform_id:
                platform_counts[platform_id] = platform_counts.get(platform_id, 0) + 1
            
            print(f"  Asset: {entity.entity_data.get('asset_value')}")
            print(f"    Project ID: {project_id}")
            print(f"    Platform ID: {platform_id}")
            print()
        
        print("项目资产统计:")
        for project_id, count in project_counts.items():
            print(f"  项目 {project_id}: {count} 个资产")
        
        print("\n平台资产统计:")
        for platform_id, count in platform_counts.items():
            print(f"  平台 {platform_id}: {count} 个资产")

if __name__ == "__main__":
    asyncio.run(test_asset_search())