#!/usr/bin/env python3
"""
测试资产管理V2统一API
"""

import asyncio
import json
from datetime import datetime
import aiohttp

async def test_unified_api():
    """测试统一API端点"""
    base_url = "http://localhost:8000"
    
    # 首先登录获取token
    async with aiohttp.ClientSession() as session:
        # 登录
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        try:
            async with session.post(f"{base_url}/api/auth/login", json=login_data) as response:
                if response.status == 200:
                    login_result = await response.json()
                    token = login_result.get("access_token")
                    print(f"✅ 登录成功，获取token: {token[:20]}...")
                else:
                    print(f"❌ 登录失败: {response.status}")
                    return
        except Exception as e:
            print(f"❌ 登录请求失败: {e}")
            return
        
        # 设置认证头
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        # 测试健康检查
        print("\n🔍 测试健康检查...")
        try:
            async with session.get(f"{base_url}/api/assets-v2-unified/health") as response:
                result = await response.json()
                print(f"✅ 健康检查: {result}")
        except Exception as e:
            print(f"❌ 健康检查失败: {e}")
        
        # 测试统计信息
        print("\n📊 测试统计信息...")
        try:
            async with session.get(f"{base_url}/api/assets-v2-unified/statistics", headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ 统计信息: {json.dumps(result, indent=2)}")
                else:
                    error = await response.text()
                    print(f"❌ 统计信息失败 ({response.status}): {error}")
        except Exception as e:
            print(f"❌ 统计信息请求失败: {e}")
        
        # 测试搜索
        print("\n🔍 测试搜索...")
        search_params = {
            "query": "*",
            "page": 1,
            "size": 5,
            "include_aggregations": True
        }
        
        try:
            async with session.post(f"{base_url}/api/assets-v2-unified/search", 
                                  json=search_params, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ 搜索结果: 找到 {result.get('data', {}).get('total', 0)} 条数据")
                    
                    # 显示前几条数据
                    hits = result.get('data', {}).get('hits', [])
                    if hits:
                        print("📋 前几条数据:")
                        for i, hit in enumerate(hits[:3]):
                            print(f"  {i+1}. {hit.get('asset_type', 'unknown')}: {hit.get('asset_value', 'N/A')}")
                else:
                    error = await response.text()
                    print(f"❌ 搜索失败 ({response.status}): {error}")
        except Exception as e:
            print(f"❌ 搜索请求失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_unified_api())
