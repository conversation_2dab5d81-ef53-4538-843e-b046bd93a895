"""
资产管理系统 V3.0
基于Elasticsearch的统一资产管理系统

核心特性：
1. 多数据源统一摄取（Agent、手动导入、API）
2. 智能数据清洗和去重
3. 动态字段映射
4. ES主存储 + PostgreSQL关联
5. Kibana深度集成
6. 高性能搜索和分析
"""

from enum import Enum
from dataclasses import dataclass, asdict
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, timedelta
import json
import hashlib
import logging
from elasticsearch import AsyncElasticsearch
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)


class DataSource(str, Enum):
    """数据源类型"""
    AGENT_SCAN = "agent_scan"           # Agent扫描发现
    MANUAL_IMPORT = "manual_import"     # 手动导入
    API_FETCH = "api_fetch"             # API获取
    THIRD_PARTY = "third_party"         # 第三方集成
    BULK_UPLOAD = "bulk_upload"         # 批量上传


class AssetType(str, Enum):
    """资产类型"""
    DOMAIN = "domain"                   # 域名
    SUBDOMAIN = "subdomain"             # 子域名
    IP = "ip"                          # IP地址
    PORT = "port"                      # 端口
    URL = "url"                        # URL
    CERTIFICATE = "certificate"         # 证书
    EMAIL = "email"                    # 邮箱
    PHONE = "phone"                    # 电话
    SOCIAL_MEDIA = "social_media"      # 社交媒体
    TECHNOLOGY = "technology"           # 技术栈
    VULNERABILITY = "vulnerability"     # 漏洞
    SERVICE = "service"                # 服务
    APPLICATION = "application"         # 应用
    DATABASE = "database"              # 数据库
    NETWORK_DEVICE = "network_device"  # 网络设备
    CLOUD_RESOURCE = "cloud_resource"  # 云资源
    API_ENDPOINT = "api_endpoint"      # API端点
    FILE = "file"                      # 文件
    CREDENTIAL = "credential"          # 凭据
    OTHER = "other"                    # 其他


class ProcessingStatus(str, Enum):
    """处理状态"""
    PENDING = "pending"                # 待处理
    PROCESSING = "processing"          # 处理中
    PROCESSED = "processed"            # 已处理
    FAILED = "failed"                  # 处理失败
    DUPLICATE = "duplicate"            # 重复数据
    INVALID = "invalid"                # 无效数据


class QualityLevel(str, Enum):
    """数据质量等级"""
    HIGH = "high"                      # 高质量
    MEDIUM = "medium"                  # 中等质量
    LOW = "low"                        # 低质量
    UNKNOWN = "unknown"                # 未知质量


@dataclass
class AssetMetadata:
    """资产元数据"""
    asset_id: str                      # 资产唯一ID
    asset_type: AssetType              # 资产类型
    data_source: DataSource            # 数据源
    source_id: Optional[str] = None    # 源系统ID
    confidence: float = 1.0            # 置信度 (0-1)
    quality_level: QualityLevel = QualityLevel.UNKNOWN
    processing_status: ProcessingStatus = ProcessingStatus.PENDING
    created_at: datetime = None
    updated_at: datetime = None
    processed_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = datetime.utcnow()


@dataclass
class AssetData:
    """资产核心数据"""
    value: str                         # 资产值（主要标识）
    name: Optional[str] = None         # 资产名称
    description: Optional[str] = None  # 描述
    tags: List[str] = None            # 标签
    attributes: Dict[str, Any] = None  # 动态属性
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.attributes is None:
            self.attributes = {}


@dataclass
class AssetRelation:
    """资产关联关系"""
    platform_id: Optional[str] = None  # 平台ID
    project_id: Optional[str] = None    # 项目ID
    parent_asset_id: Optional[str] = None  # 父资产ID
    related_assets: List[str] = None    # 相关资产ID列表
    
    def __post_init__(self):
        if self.related_assets is None:
            self.related_assets = []


@dataclass
class ProcessingInfo:
    """处理信息"""
    dedup_hash: Optional[str] = None    # 去重哈希
    field_mappings: Dict[str, str] = None  # 字段映射记录
    validation_errors: List[str] = None  # 验证错误
    processing_notes: Optional[str] = None  # 处理备注
    
    def __post_init__(self):
        if self.field_mappings is None:
            self.field_mappings = {}
        if self.validation_errors is None:
            self.validation_errors = []


@dataclass
class Asset:
    """完整资产对象"""
    metadata: AssetMetadata
    data: AssetData
    relation: AssetRelation
    processing: ProcessingInfo
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "metadata": asdict(self.metadata),
            "data": asdict(self.data),
            "relation": asdict(self.relation),
            "processing": asdict(self.processing)
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Asset':
        """从字典创建资产对象"""
        return cls(
            metadata=AssetMetadata(**data["metadata"]),
            data=AssetData(**data["data"]),
            relation=AssetRelation(**data["relation"]),
            processing=ProcessingInfo(**data["processing"])
        )
    
    def generate_id(self) -> str:
        """生成资产ID"""
        # 基于资产类型和值生成唯一ID
        content = f"{self.metadata.asset_type}:{self.data.value}"
        return hashlib.sha256(content.encode()).hexdigest()[:16]
    
    def generate_dedup_hash(self) -> str:
        """生成去重哈希"""
        # 基于关键字段生成去重哈希
        key_fields = [
            self.metadata.asset_type,
            self.data.value,
            self.relation.platform_id or "",
            self.relation.project_id or ""
        ]
        content = "|".join(str(field) for field in key_fields)
        return hashlib.md5(content.encode()).hexdigest()


class FieldMappingRule:
    """字段映射规则"""
    
    def __init__(self):
        # 标准字段映射配置
        self.standard_mappings = {
            # 域名相关
            "domain": ["domain", "hostname", "host", "site", "website"],
            "subdomain": ["subdomain", "sub_domain", "sub", "child_domain"],
            
            # IP相关
            "ip": ["ip", "ip_address", "ipv4", "ipv6", "address"],
            "port": ["port", "service_port", "tcp_port", "udp_port"],
            
            # URL相关
            "url": ["url", "link", "uri", "endpoint", "path"],
            "protocol": ["protocol", "scheme", "service"],
            
            # 通用字段
            "title": ["title", "name", "label", "display_name"],
            "description": ["description", "desc", "summary", "info"],
            "status": ["status", "state", "condition"],
            "confidence": ["confidence", "score", "reliability", "trust"],
            "tags": ["tags", "labels", "categories", "keywords"],
        }
    
    def map_fields(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """映射字段到标准格式"""
        mapped_data = {}
        
        for standard_field, possible_names in self.standard_mappings.items():
            for field_name in possible_names:
                if field_name in raw_data:
                    mapped_data[standard_field] = raw_data[field_name]
                    break
        
        # 保留未映射的字段到attributes中
        mapped_data["attributes"] = {}
        for key, value in raw_data.items():
            if key not in [name for names in self.standard_mappings.values() for name in names]:
                mapped_data["attributes"][key] = value
        
        return mapped_data


class DataQualityChecker:
    """数据质量检查器"""
    
    def __init__(self):
        self.validation_rules = {
            AssetType.DOMAIN: self._validate_domain,
            AssetType.IP: self._validate_ip,
            AssetType.URL: self._validate_url,
            AssetType.EMAIL: self._validate_email,
            AssetType.PORT: self._validate_port,
        }
    
    def check_quality(self, asset: Asset) -> Tuple[QualityLevel, List[str]]:
        """检查资产数据质量"""
        errors = []
        
        # 基础验证
        if not asset.data.value or not asset.data.value.strip():
            errors.append("资产值不能为空")
        
        # 类型特定验证
        if asset.metadata.asset_type in self.validation_rules:
            validator = self.validation_rules[asset.metadata.asset_type]
            type_errors = validator(asset.data.value)
            errors.extend(type_errors)
        
        # 确定质量等级
        if not errors:
            quality_level = QualityLevel.HIGH
        elif len(errors) <= 2:
            quality_level = QualityLevel.MEDIUM
        else:
            quality_level = QualityLevel.LOW
        
        return quality_level, errors
    
    def _validate_domain(self, value: str) -> List[str]:
        """验证域名格式"""
        errors = []
        import re
        
        domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        if not re.match(domain_pattern, value):
            errors.append("域名格式不正确")
        
        if len(value) > 253:
            errors.append("域名长度超过限制")
        
        return errors
    
    def _validate_ip(self, value: str) -> List[str]:
        """验证IP地址格式"""
        errors = []
        import ipaddress
        
        try:
            ipaddress.ip_address(value)
        except ValueError:
            errors.append("IP地址格式不正确")
        
        return errors
    
    def _validate_url(self, value: str) -> List[str]:
        """验证URL格式"""
        errors = []
        from urllib.parse import urlparse
        
        try:
            result = urlparse(value)
            if not all([result.scheme, result.netloc]):
                errors.append("URL格式不完整")
        except Exception:
            errors.append("URL格式不正确")
        
        return errors
    
    def _validate_email(self, value: str) -> List[str]:
        """验证邮箱格式"""
        errors = []
        import re
        
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, value):
            errors.append("邮箱格式不正确")
        
        return errors
    
    def _validate_port(self, value: str) -> List[str]:
        """验证端口号"""
        errors = []
        
        try:
            port = int(value)
            if not (1 <= port <= 65535):
                errors.append("端口号超出有效范围(1-65535)")
        except ValueError:
            errors.append("端口号必须是数字")
        
        return errors


class AssetManagerV3:
    """资产管理器 V3.0 - 统一资产管理系统"""

    def __init__(
        self,
        es_client: AsyncElasticsearch,
        db_session: AsyncSession
    ):
        self.es_client = es_client
        self.db_session = db_session

        # 初始化组件（延迟加载）
        self.index_manager = None
        self.storage_service = None
        self.search_service = None
        self.ingestion_pipeline = None
        self.deduplicator = None
        self.data_cleaner = None

        # 配置
        self.auto_dedup = True
        self.auto_clean = True
        self.batch_size = 100

    async def initialize(self) -> bool:
        """初始化资产管理器"""
        try:
            # 延迟导入避免循环依赖
            import asset_index_manager
            import asset_storage_service
            import asset_ingestion_pipeline
            import asset_deduplication

            # 初始化索引管理器
            self.index_manager = await asset_index_manager.create_asset_index_manager(self.es_client)

            # 初始化存储和搜索服务
            self.storage_service = asset_storage_service.AssetStorageService(self.es_client, self.index_manager)
            self.search_service = asset_storage_service.AssetSearchService(self.es_client)

            # 初始化数据处理组件
            self.ingestion_pipeline = asset_ingestion_pipeline.DataIngestionPipeline()
            self.deduplicator = asset_deduplication.AssetDeduplicator(self.es_client)
            self.data_cleaner = asset_deduplication.DataCleaner()

            logger.info("✅ 资产管理器 V3.0 初始化完成")
            return True

        except Exception as e:
            logger.error(f"❌ 资产管理器初始化失败: {e}")
            return False

    async def ingest_assets(
        self,
        raw_data_list: List[Dict[str, Any]],
        data_source: DataSource,
        asset_type: Optional[AssetType] = None,
        source_id: Optional[str] = None,
        platform_id: Optional[str] = None,
        project_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """摄取资产数据"""
        try:
            # 1. 数据摄取
            assets, ingestion_stats = await self.ingestion_pipeline.ingest_batch_assets(
                raw_data_list=raw_data_list,
                data_source=data_source,
                asset_type=asset_type,
                source_id=source_id,
                platform_id=platform_id,
                project_id=project_id
            )

            # 2. 数据清洗
            if self.auto_clean:
                cleaned_assets = []
                for asset in assets:
                    cleaned_asset = self.data_cleaner.clean_asset(asset)
                    cleaned_assets.append(cleaned_asset)
                assets = cleaned_assets

            # 3. 去重处理
            final_assets = []
            dedup_stats = {"duplicates_found": 0, "duplicates_merged": 0}

            if self.auto_dedup:
                for asset in assets:
                    duplicates = await self.deduplicator.find_duplicates(asset)

                    if duplicates:
                        dedup_stats["duplicates_found"] += len(duplicates)
                        # 合并重复资产
                        merged_asset = await self.deduplicator.merge_duplicates(asset, duplicates)
                        final_assets.append(merged_asset)
                        dedup_stats["duplicates_merged"] += 1
                    else:
                        final_assets.append(asset)
            else:
                final_assets = assets

            # 4. 存储资产
            success_count, error_count, errors = await self.storage_service.store_assets_batch(final_assets)

            # 5. 同步到PostgreSQL（如果需要）
            sync_stats = await self._sync_to_postgres(final_assets)

            return {
                "status": "success",
                "ingestion": ingestion_stats,
                "deduplication": dedup_stats,
                "storage": {
                    "success": success_count,
                    "errors": error_count,
                    "error_details": errors
                },
                "sync": sync_stats,
                "total_processed": len(final_assets)
            }

        except Exception as e:
            logger.error(f"摄取资产失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

    async def search_assets(
        self,
        query: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None,
        sort: Optional[List[Dict[str, str]]] = None,
        page: int = 1,
        size: int = 20
    ) -> Dict[str, Any]:
        """搜索资产"""
        return await self.search_service.search_assets(
            query=query,
            filters=filters,
            sort=sort,
            page=page,
            size=size
        )

    async def get_statistics(self) -> Dict[str, Any]:
        """获取资产统计信息"""
        return await self.search_service.get_asset_statistics()

    async def get_asset(self, asset_id: str) -> Optional[Asset]:
        """获取单个资产"""
        return await self.storage_service.get_asset(asset_id)

    async def update_asset(self, asset_id: str, updates: Dict[str, Any]) -> bool:
        """更新资产"""
        return await self.storage_service.update_asset(asset_id, updates)

    async def delete_asset(self, asset_id: str) -> bool:
        """删除资产"""
        return await self.storage_service.delete_asset(asset_id)

    async def _sync_to_postgres(self, assets: List[Asset]) -> Dict[str, Any]:
        """同步资产到PostgreSQL"""
        # TODO: 实现PostgreSQL同步逻辑
        return {
            "synced": 0,
            "skipped": len(assets),
            "note": "PostgreSQL同步功能待实现"
        }


async def create_asset_manager_v3(
    es_client: AsyncElasticsearch,
    db_session: AsyncSession
) -> AssetManagerV3:
    """创建资产管理器实例"""
    manager = AssetManagerV3(es_client, db_session)
    await manager.initialize()
    return manager
