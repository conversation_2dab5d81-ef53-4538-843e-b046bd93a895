#!/usr/bin/env python3
"""
资产管理系统 V3.0 简化初始化脚本
"""

import asyncio
import logging
from datetime import datetime

from elasticsearch_client import get_es_client

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def check_elasticsearch_connection():
    """检查Elasticsearch连接"""
    try:
        es_client = await get_es_client()
        info = await es_client.info()
        logger.info(f"✅ Elasticsearch连接正常: {info['version']['number']}")
        return es_client
    except Exception as e:
        logger.error(f"❌ Elasticsearch连接失败: {e}")
        return None


async def create_index_template(es_client):
    """创建索引模板"""
    try:
        template_name = "assets-v3-template"
        template = {
            "index_patterns": ["assets-v3-*"],
            "priority": 100,  # 设置更高的优先级
            "template": {
                "settings": {
                    "number_of_shards": 3,
                    "number_of_replicas": 1,
                    "index": {
                        "refresh_interval": "5s",
                        "max_result_window": 50000
                    },
                    "analysis": {
                        "analyzer": {
                            "asset_analyzer": {
                                "type": "custom",
                                "tokenizer": "keyword",
                                "filter": ["lowercase", "trim"]
                            }
                        }
                    }
                },
                "mappings": {
                    "properties": {
                        "metadata": {
                            "type": "object",
                            "properties": {
                                "asset_id": {"type": "keyword"},
                                "asset_type": {"type": "keyword"},
                                "data_source": {"type": "keyword"},
                                "confidence": {"type": "float"},
                                "quality_level": {"type": "keyword"},
                                "processing_status": {"type": "keyword"},
                                "created_at": {"type": "date"},
                                "updated_at": {"type": "date"}
                            }
                        },
                        "data": {
                            "type": "object",
                            "properties": {
                                "value": {
                                    "type": "text",
                                    "analyzer": "asset_analyzer",
                                    "fields": {
                                        "keyword": {"type": "keyword"}
                                    }
                                },
                                "name": {"type": "text"},
                                "description": {"type": "text"},
                                "tags": {"type": "keyword"},
                                "attributes": {"type": "object", "dynamic": True}
                            }
                        },
                        "relation": {
                            "type": "object",
                            "properties": {
                                "platform_id": {"type": "keyword"},
                                "project_id": {"type": "keyword"},
                                "parent_asset_id": {"type": "keyword"},
                                "related_assets": {"type": "keyword"}
                            }
                        },
                        "processing": {
                            "type": "object",
                            "properties": {
                                "dedup_hash": {"type": "keyword"},
                                "validation_errors": {"type": "keyword"},
                                "processing_notes": {"type": "text"}
                            }
                        },
                        "full_text": {"type": "text"},
                        "@timestamp": {"type": "date"}
                    }
                }
            }
        }
        
        await es_client.indices.put_index_template(
            name=template_name,
            body=template
        )
        
        logger.info(f"✅ 创建索引模板: {template_name}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建索引模板失败: {e}")
        return False


async def create_current_index(es_client):
    """创建当前索引"""
    try:
        current_date = datetime.utcnow()
        index_name = f"assets-v3-{current_date.strftime('%Y-%m')}"
        alias_name = "assets-current"
        
        # 检查索引是否已存在
        if await es_client.indices.exists(index=index_name):
            logger.info(f"索引 {index_name} 已存在")
        else:
            await es_client.indices.create(index=index_name)
            logger.info(f"✅ 创建索引: {index_name}")
        
        # 设置别名
        try:
            await es_client.indices.put_alias(
                index=index_name,
                name=alias_name
            )
            logger.info(f"✅ 设置别名: {alias_name} -> {index_name}")
        except Exception as e:
            logger.warning(f"设置别名失败: {e}")
        
        return index_name
        
    except Exception as e:
        logger.error(f"❌ 创建索引失败: {e}")
        return None


async def insert_sample_data(es_client, index_name):
    """插入示例数据"""
    try:
        sample_assets = [
            {
                "metadata": {
                    "asset_id": "domain_001",
                    "asset_type": "domain",
                    "data_source": "manual_import",
                    "confidence": 1.0,
                    "quality_level": "high",
                    "processing_status": "processed",
                    "created_at": datetime.utcnow().isoformat(),
                    "updated_at": datetime.utcnow().isoformat()
                },
                "data": {
                    "value": "example.com",
                    "name": "示例网站",
                    "description": "这是一个示例域名",
                    "tags": ["web", "demo"],
                    "attributes": {"category": "website"}
                },
                "relation": {
                    "platform_id": None,
                    "project_id": None,
                    "parent_asset_id": None,
                    "related_assets": []
                },
                "processing": {
                    "dedup_hash": "abc123",
                    "validation_errors": [],
                    "processing_notes": "示例数据"
                },
                "full_text": "example.com 示例网站 这是一个示例域名 web demo",
                "@timestamp": datetime.utcnow().isoformat()
            },
            {
                "metadata": {
                    "asset_id": "ip_001",
                    "asset_type": "ip",
                    "data_source": "manual_import",
                    "confidence": 0.9,
                    "quality_level": "high",
                    "processing_status": "processed",
                    "created_at": datetime.utcnow().isoformat(),
                    "updated_at": datetime.utcnow().isoformat()
                },
                "data": {
                    "value": "***********",
                    "name": "内网网关",
                    "description": "内部网络网关地址",
                    "tags": ["gateway", "internal"],
                    "attributes": {"network": "internal"}
                },
                "relation": {
                    "platform_id": None,
                    "project_id": None,
                    "parent_asset_id": None,
                    "related_assets": []
                },
                "processing": {
                    "dedup_hash": "def456",
                    "validation_errors": [],
                    "processing_notes": "示例IP数据"
                },
                "full_text": "*********** 内网网关 内部网络网关地址 gateway internal",
                "@timestamp": datetime.utcnow().isoformat()
            }
        ]
        
        # 批量插入数据
        for i, asset in enumerate(sample_assets):
            await es_client.index(
                index=index_name,
                id=asset["metadata"]["asset_id"],
                body=asset
            )
        
        # 刷新索引
        await es_client.indices.refresh(index=index_name)
        
        logger.info(f"✅ 插入 {len(sample_assets)} 个示例资产")
        return True
        
    except Exception as e:
        logger.error(f"❌ 插入示例数据失败: {e}")
        return False


async def test_search(es_client):
    """测试搜索功能"""
    try:
        # 基础搜索
        response = await es_client.search(
            index="assets-current",
            body={
                "query": {"match_all": {}},
                "size": 10
            }
        )
        
        total = response["hits"]["total"]["value"]
        logger.info(f"✅ 搜索测试成功: 找到 {total} 个资产")
        
        # 类型过滤搜索
        response2 = await es_client.search(
            index="assets-current",
            body={
                "query": {
                    "term": {"metadata.asset_type": "domain"}
                },
                "size": 10
            }
        )
        
        domain_count = response2["hits"]["total"]["value"]
        logger.info(f"✅ 过滤测试成功: 找到 {domain_count} 个域名")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 搜索测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("🚀 资产管理系统 V3.0 简化初始化开始...")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 检查Elasticsearch连接
    es_client = await check_elasticsearch_connection()
    if not es_client:
        print("❌ 初始化失败: Elasticsearch连接不可用")
        return
    
    # 2. 创建索引模板
    if not await create_index_template(es_client):
        print("❌ 初始化失败: 索引模板创建失败")
        return
    
    # 3. 创建当前索引
    index_name = await create_current_index(es_client)
    if not index_name:
        print("❌ 初始化失败: 索引创建失败")
        return
    
    # 4. 插入示例数据
    if not await insert_sample_data(es_client, index_name):
        print("❌ 初始化失败: 示例数据插入失败")
        return
    
    # 5. 测试搜索功能
    if not await test_search(es_client):
        print("❌ 初始化失败: 搜索功能测试失败")
        return
    
    print("\n🎉 资产管理系统 V3.0 简化初始化完成！")
    print("📋 已完成:")
    print("   • Elasticsearch索引模板创建")
    print("   • 当前索引创建和别名设置")
    print("   • 示例数据插入")
    print("   • 搜索功能验证")
    print("\n🌐 可以开始使用API:")
    print("   • 搜索资产: GET /api/assets-v3/search")
    print("   • 摄取数据: POST /api/assets-v3/ingest")
    print("   • 上传CSV: POST /api/assets-v3/ingest/csv")
    print("   • 统计信息: GET /api/assets-v3/statistics")
    print("   • API文档: http://localhost:8000/docs")


if __name__ == "__main__":
    asyncio.run(main())
