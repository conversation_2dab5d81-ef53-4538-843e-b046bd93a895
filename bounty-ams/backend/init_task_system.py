#!/usr/bin/env python3
"""
初始化任务管理系统
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from models.task_models import (
    Base, TaskTemplate, Workflow, Agent, 
    TaskStatus, WorkflowStatus, AgentStatus,
    create_tables, generate_uuid
)
from task_template_engine import template_engine
from workflow_engine import WorkflowEngine
from intelligent_task_scheduler import IntelligentTaskScheduler, AgentManager

# 数据库配置
DATABASE_URL = "sqlite:///./task_system.db"  # 使用SQLite进行演示
engine = create_engine(DATABASE_URL, echo=True)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def init_database():
    """初始化数据库"""
    print("🔧 初始化数据库...")
    create_tables(engine)
    print("✅ 数据库初始化完成")

def create_sample_templates():
    """创建示例任务模板"""
    print("📝 创建示例任务模板...")
    
    db = SessionLocal()
    try:
        # 子域名发现模板
        subdomain_template = TaskTemplate(
            template_id="subdomain_discovery_v1",
            name="子域名发现",
            version="1.0.0",
            description="使用多种工具进行子域名发现",
            category="reconnaissance",
            tags=["subdomain", "discovery", "passive"],
            parameters={
                "target": {
                    "type": "string",
                    "required": True,
                    "description": "目标域名",
                    "pattern": r"^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
                },
                "wordlist": {
                    "type": "string",
                    "required": False,
                    "default": "common.txt",
                    "description": "字典文件路径"
                },
                "timeout": {
                    "type": "integer",
                    "required": False,
                    "default": 300,
                    "minimum": 60,
                    "maximum": 3600,
                    "description": "超时时间(秒)"
                }
            },
            tools=[
                {
                    "tool_name": "subfinder",
                    "version": ">=2.6.0",
                    "command": "subfinder",
                    "args": ["-d", "{{target}}", "-o", "{{output_file}}", "-timeout", "{{timeout}}", "-silent"],
                    "env": {"SUBFINDER_CONFIG": "/config/subfinder.yaml"},
                    "output_format": "text",
                    "output_parser": "line_split",
                    "timeout": 300,
                    "retry_count": 3
                },
                {
                    "tool_name": "assetfinder",
                    "version": ">=0.1.0",
                    "command": "assetfinder",
                    "args": ["--subs-only", "{{target}}"],
                    "output_format": "text",
                    "output_parser": "line_split",
                    "timeout": 300,
                    "retry_count": 3
                }
            ],
            execution={
                "strategy": "parallel",
                "merge_results": True,
                "deduplication": True,
                "timeout": "{{timeout}}",
                "retry": {
                    "max_attempts": 3,
                    "backoff": "exponential"
                }
            },
            output_schema={
                "type": "object",
                "properties": {
                    "subdomains": {
                        "type": "array",
                        "items": {"type": "string"}
                    },
                    "metadata": {
                        "type": "object",
                        "properties": {
                            "total_found": {"type": "integer"},
                            "tools_used": {"type": "array"},
                            "execution_time": {"type": "number"}
                        }
                    }
                }
            },
            created_by="system"
        )
        
        # 端口扫描模板
        port_scan_template = TaskTemplate(
            template_id="port_scan_v1",
            name="端口扫描",
            version="1.0.0",
            description="使用nmap进行端口扫描",
            category="scanning",
            tags=["port", "scan", "nmap"],
            parameters={
                "targets": {
                    "type": "array",
                    "items": {"type": "string"},
                    "required": True,
                    "description": "目标列表"
                },
                "ports": {
                    "type": "string",
                    "required": False,
                    "default": "common",
                    "enum": ["common", "full", "top1000"],
                    "description": "端口范围"
                },
                "scan_type": {
                    "type": "string",
                    "required": False,
                    "default": "syn",
                    "enum": ["syn", "tcp", "udp"],
                    "description": "扫描类型"
                }
            },
            tools=[
                {
                    "tool_name": "nmap",
                    "version": ">=7.80",
                    "command": "nmap",
                    "args": [
                        "-sS",
                        "-p", "{{port_range}}",
                        "-oX", "{{output_file}}",
                        "{{targets_file}}"
                    ],
                    "output_format": "xml",
                    "output_parser": "nmap_xml",
                    "timeout": 1800,
                    "retry_count": 2
                }
            ],
            execution={
                "strategy": "sequential",
                "timeout": 1800,
                "retry": {
                    "max_attempts": 2,
                    "backoff": "linear"
                }
            },
            output_schema={
                "type": "object",
                "properties": {
                    "open_ports": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "host": {"type": "string"},
                                "port": {"type": "integer"},
                                "protocol": {"type": "string"},
                                "service": {"type": "string"},
                                "state": {"type": "string"}
                            }
                        }
                    },
                    "web_services": {
                        "type": "array",
                        "items": {"type": "string"}
                    }
                }
            },
            created_by="system"
        )
        
        # Nuclei扫描模板
        nuclei_template = TaskTemplate(
            template_id="nuclei_scan_v1",
            name="Nuclei漏洞扫描",
            version="1.0.0",
            description="使用Nuclei进行漏洞扫描",
            category="vulnerability",
            tags=["vulnerability", "nuclei", "scan"],
            parameters={
                "targets": {
                    "type": "array",
                    "items": {"type": "string"},
                    "required": True,
                    "description": "目标URL列表"
                },
                "severity": {
                    "type": "string",
                    "required": False,
                    "default": "medium,high,critical",
                    "description": "漏洞严重程度"
                },
                "templates": {
                    "type": "string",
                    "required": False,
                    "default": "all",
                    "description": "模板路径"
                }
            },
            tools=[
                {
                    "tool_name": "nuclei",
                    "version": ">=2.9.0",
                    "command": "nuclei",
                    "args": [
                        "-l", "{{targets_file}}",
                        "-s", "{{severity}}",
                        "-o", "{{output_file}}",
                        "-json"
                    ],
                    "output_format": "json",
                    "output_parser": "nuclei_json",
                    "timeout": 3600,
                    "retry_count": 1
                }
            ],
            execution={
                "strategy": "sequential",
                "timeout": 3600,
                "retry": {
                    "max_attempts": 1,
                    "backoff": "none"
                }
            },
            output_schema={
                "type": "object",
                "properties": {
                    "vulnerabilities": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "template_id": {"type": "string"},
                                "info": {"type": "object"},
                                "matched_at": {"type": "string"},
                                "severity": {"type": "string"}
                            }
                        }
                    }
                }
            },
            created_by="system"
        )
        
        # 添加到数据库
        db.add_all([subdomain_template, port_scan_template, nuclei_template])
        db.commit()
        
        print("✅ 示例任务模板创建完成")
        
    except Exception as e:
        print(f"❌ 创建任务模板失败: {e}")
        db.rollback()
    finally:
        db.close()

def create_sample_workflows():
    """创建示例工作流"""
    print("🔄 创建示例工作流...")
    
    db = SessionLocal()
    try:
        # 综合安全扫描工作流
        comprehensive_workflow = Workflow(
            workflow_id="comprehensive_scan_v1",
            name="综合安全扫描",
            version="1.0.0",
            description="从子域名发现到漏洞扫描的完整流程",
            parameters={
                "target": {
                    "type": "string",
                    "required": True,
                    "description": "目标域名"
                },
                "scan_depth": {
                    "type": "string",
                    "enum": ["light", "medium", "deep"],
                    "default": "medium",
                    "description": "扫描深度"
                }
            },
            stages=[
                {
                    "stage_id": "discovery",
                    "name": "资产发现阶段",
                    "parallel": False,
                    "condition": "always",
                    "on_failure": "stop",
                    "tasks": [
                        {
                            "task_id": "subdomain_discovery",
                            "template_id": "subdomain_discovery_v1",
                            "parameters": {
                                "target": "{{workflow.target}}",
                                "timeout": 600
                            },
                            "condition": "always",
                            "depends_on": [],
                            "retry_count": 0,
                            "on_failure": "stop"
                        },
                        {
                            "task_id": "port_scan",
                            "template_id": "port_scan_v1",
                            "parameters": {
                                "targets": "{{subdomain_discovery.output.subdomains}}",
                                "ports": "{{workflow.scan_depth == 'deep' and 'full' or 'common'}}"
                            },
                            "condition": "subdomain_discovery.status == 'success' and subdomain_discovery.output.subdomains|length > 0",
                            "depends_on": ["subdomain_discovery"],
                            "retry_count": 0,
                            "on_failure": "continue"
                        }
                    ]
                },
                {
                    "stage_id": "vulnerability",
                    "name": "漏洞扫描阶段",
                    "parallel": False,
                    "condition": "port_scan.status == 'success'",
                    "on_failure": "continue",
                    "tasks": [
                        {
                            "task_id": "nuclei_scan",
                            "template_id": "nuclei_scan_v1",
                            "parameters": {
                                "targets": "{{port_scan.output.web_services}}",
                                "severity": "{{workflow.scan_depth == 'light' and 'high,critical' or 'medium,high,critical'}}"
                            },
                            "condition": "port_scan.output.web_services|length > 0",
                            "depends_on": ["port_scan"],
                            "retry_count": 0,
                            "on_failure": "continue"
                        }
                    ]
                }
            ],
            error_handling={
                "strategy": "continue_on_error",
                "max_stage_failures": 2,
                "notification": {
                    "on_failure": True,
                    "on_success": True,
                    "channels": ["email", "webhook"]
                }
            },
            output_aggregation={
                "merge_strategy": "deep_merge",
                "final_report": {
                    "format": "json",
                    "include_metadata": True,
                    "include_raw_outputs": False
                }
            },
            created_by="system"
        )
        
        db.add(comprehensive_workflow)
        db.commit()
        
        print("✅ 示例工作流创建完成")
        
    except Exception as e:
        print(f"❌ 创建工作流失败: {e}")
        db.rollback()
    finally:
        db.close()

def create_sample_agents():
    """创建示例Agent"""
    print("🤖 创建示例Agent...")
    
    db = SessionLocal()
    try:
        # Scanner Agent 1
        agent1 = Agent(
            agent_id="scanner-001",
            name="Scanner-01",
            status=AgentStatus.ONLINE,
            capabilities=[
                {
                    "tool_name": "subfinder",
                    "version": "2.6.3",
                    "supported_formats": ["json", "text"],
                    "max_concurrent": 5,
                    "resource_requirements": {"cpu": "0.5", "memory": "512Mi"},
                    "performance_score": 1.0
                },
                {
                    "tool_name": "assetfinder",
                    "version": "0.1.1",
                    "supported_formats": ["text"],
                    "max_concurrent": 3,
                    "resource_requirements": {"cpu": "0.3", "memory": "256Mi"},
                    "performance_score": 0.9
                },
                {
                    "tool_name": "httpx",
                    "version": "1.3.7",
                    "supported_formats": ["json"],
                    "max_concurrent": 10,
                    "resource_requirements": {"cpu": "1.0", "memory": "1Gi"},
                    "performance_score": 1.1
                }
            ],
            resource_limits={"max_cpu": "4.0", "max_memory": "8Gi", "max_concurrent_tasks": 20},
            current_load=0,
            max_capacity=10,
            last_heartbeat=datetime.utcnow(),
            location="Beijing",
            tags=["reconnaissance", "discovery"],
            performance_history={},
            api_key="scanner001_api_key_demo"
        )
        
        # Scanner Agent 2
        agent2 = Agent(
            agent_id="scanner-002",
            name="Scanner-02",
            status=AgentStatus.ONLINE,
            capabilities=[
                {
                    "tool_name": "nmap",
                    "version": "7.94",
                    "supported_formats": ["xml", "json"],
                    "max_concurrent": 3,
                    "resource_requirements": {"cpu": "2.0", "memory": "2Gi"},
                    "performance_score": 1.2
                },
                {
                    "tool_name": "nuclei",
                    "version": "2.9.15",
                    "supported_formats": ["json"],
                    "max_concurrent": 5,
                    "resource_requirements": {"cpu": "1.5", "memory": "1.5Gi"},
                    "performance_score": 1.0
                }
            ],
            resource_limits={"max_cpu": "8.0", "max_memory": "16Gi", "max_concurrent_tasks": 15},
            current_load=0,
            max_capacity=15,
            last_heartbeat=datetime.utcnow(),
            location="Shanghai",
            tags=["scanning", "vulnerability"],
            performance_history={},
            api_key="scanner002_api_key_demo"
        )
        
        db.add_all([agent1, agent2])
        db.commit()
        
        print("✅ 示例Agent创建完成")
        
    except Exception as e:
        print(f"❌ 创建Agent失败: {e}")
        db.rollback()
    finally:
        db.close()

async def init_engines():
    """初始化引擎"""
    print("⚙️ 初始化任务引擎...")
    
    # 初始化Agent管理器
    agent_manager = AgentManager()
    
    # 初始化任务调度器
    task_scheduler = IntelligentTaskScheduler(agent_manager)
    
    # 初始化工作流引擎
    workflow_engine = WorkflowEngine(template_engine, task_scheduler)
    
    # 从数据库加载模板和工作流
    db = SessionLocal()
    try:
        # 加载模板
        templates = db.query(TaskTemplate).filter(TaskTemplate.is_active == True).all()
        for template in templates:
            template_engine.register_template_from_db(template)
        
        # 加载工作流
        workflows = db.query(Workflow).filter(Workflow.is_active == True).all()
        for workflow in workflows:
            workflow_engine.register_workflow_from_db(workflow)
        
        # 加载Agent
        agents = db.query(Agent).filter(Agent.is_active == True).all()
        for agent in agents:
            agent_manager.register_agent_from_db(agent)
        
        print(f"✅ 加载了 {len(templates)} 个模板, {len(workflows)} 个工作流, {len(agents)} 个Agent")
        
    finally:
        db.close()
    
    # 启动调度器
    await task_scheduler.start_scheduler()
    
    print("✅ 任务引擎初始化完成")
    
    return agent_manager, task_scheduler, workflow_engine

def main():
    """主函数"""
    print("🚀 开始初始化任务管理系统...")
    
    try:
        # 1. 初始化数据库
        init_database()
        
        # 2. 创建示例数据
        create_sample_templates()
        create_sample_workflows()
        create_sample_agents()
        
        # 3. 初始化引擎
        print("⚙️ 启动任务引擎...")
        # 注意：这里只是演示，实际应该在FastAPI应用启动时初始化
        
        print("🎉 任务管理系统初始化完成!")
        print("\n📋 系统信息:")
        print("- 数据库: SQLite (task_system.db)")
        print("- 任务模板: 3个 (子域名发现、端口扫描、Nuclei扫描)")
        print("- 工作流: 1个 (综合安全扫描)")
        print("- Agent: 2个 (Scanner-01, Scanner-02)")
        print("\n🔗 接下来可以:")
        print("1. 启动FastAPI后端服务")
        print("2. 启动前端React应用")
        print("3. 通过Web界面管理任务和工作流")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
