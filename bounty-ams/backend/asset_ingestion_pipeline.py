"""
资产数据摄取管道
统一处理来自多个数据源的资产数据
"""

from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
import asyncio
import logging
import json
from dataclasses import asdict

from asset_management_v3 import (
    Asset, AssetMetadata, AssetData, AssetRelation, ProcessingInfo,
    DataSource, AssetType, ProcessingStatus, QualityLevel,
    FieldMappingRule, DataQualityChecker
)

logger = logging.getLogger(__name__)


class DataIngestionPipeline:
    """数据摄取管道"""
    
    def __init__(self):
        self.field_mapper = FieldMappingRule()
        self.quality_checker = DataQualityChecker()
        self.batch_size = 100
        self.max_retries = 3
        
    async def ingest_single_asset(
        self,
        raw_data: Dict[str, Any],
        data_source: DataSource,
        asset_type: Optional[AssetType] = None,
        source_id: Optional[str] = None,
        platform_id: Optional[str] = None,
        project_id: Optional[str] = None
    ) -> <PERSON><PERSON>[Asset, bool]:
        """摄取单个资产
        
        Returns:
            <PERSON><PERSON>[Asset, bool]: (资产对象, 是否成功)
        """
        try:
            # 1. 字段映射
            mapped_data = self.field_mapper.map_fields(raw_data)
            
            # 2. 自动检测资产类型（如果未指定）
            if asset_type is None:
                asset_type = self._detect_asset_type(mapped_data)
            
            # 3. 构建资产对象
            asset = self._build_asset(
                mapped_data=mapped_data,
                data_source=data_source,
                asset_type=asset_type,
                source_id=source_id,
                platform_id=platform_id,
                project_id=project_id
            )
            
            # 4. 数据质量检查
            quality_level, validation_errors = self.quality_checker.check_quality(asset)
            asset.metadata.quality_level = quality_level
            asset.processing.validation_errors = validation_errors
            
            # 5. 生成ID和哈希
            asset.metadata.asset_id = asset.generate_id()
            asset.processing.dedup_hash = asset.generate_dedup_hash()
            
            # 6. 设置处理状态
            if validation_errors:
                asset.metadata.processing_status = ProcessingStatus.INVALID
            else:
                asset.metadata.processing_status = ProcessingStatus.PROCESSED
                asset.metadata.processed_at = datetime.utcnow()
            
            logger.debug(f"成功摄取资产: {asset.metadata.asset_id}")
            return asset, True
            
        except Exception as e:
            logger.error(f"摄取资产失败: {e}")
            # 创建错误资产对象
            error_asset = self._create_error_asset(raw_data, data_source, str(e))
            return error_asset, False
    
    async def ingest_batch_assets(
        self,
        raw_data_list: List[Dict[str, Any]],
        data_source: DataSource,
        asset_type: Optional[AssetType] = None,
        source_id: Optional[str] = None,
        platform_id: Optional[str] = None,
        project_id: Optional[str] = None
    ) -> Tuple[List[Asset], Dict[str, int]]:
        """批量摄取资产
        
        Returns:
            Tuple[List[Asset], Dict[str, int]]: (资产列表, 统计信息)
        """
        assets = []
        stats = {
            "total": len(raw_data_list),
            "success": 0,
            "failed": 0,
            "invalid": 0,
            "duplicate": 0
        }
        
        # 分批处理
        for i in range(0, len(raw_data_list), self.batch_size):
            batch = raw_data_list[i:i + self.batch_size]
            
            # 并发处理批次
            tasks = []
            for raw_data in batch:
                task = self.ingest_single_asset(
                    raw_data=raw_data,
                    data_source=data_source,
                    asset_type=asset_type,
                    source_id=source_id,
                    platform_id=platform_id,
                    project_id=project_id
                )
                tasks.append(task)
            
            # 等待批次完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for result in results:
                if isinstance(result, Exception):
                    stats["failed"] += 1
                    logger.error(f"批量处理异常: {result}")
                    continue
                
                asset, success = result
                assets.append(asset)
                
                if success:
                    if asset.metadata.processing_status == ProcessingStatus.INVALID:
                        stats["invalid"] += 1
                    else:
                        stats["success"] += 1
                else:
                    stats["failed"] += 1
        
        logger.info(f"批量摄取完成: {stats}")
        return assets, stats
    
    def _detect_asset_type(self, mapped_data: Dict[str, Any]) -> AssetType:
        """自动检测资产类型"""
        value = mapped_data.get("value", "").lower()
        
        # IP地址检测
        if self._is_ip_address(value):
            return AssetType.IP
        
        # 域名检测
        if self._is_domain(value):
            if value.count('.') > 1:
                return AssetType.SUBDOMAIN
            else:
                return AssetType.DOMAIN
        
        # URL检测
        if value.startswith(('http://', 'https://', 'ftp://')):
            return AssetType.URL
        
        # 邮箱检测
        if '@' in value and '.' in value:
            return AssetType.EMAIL
        
        # 端口检测
        if value.isdigit() and 1 <= int(value) <= 65535:
            return AssetType.PORT
        
        # 默认为其他类型
        return AssetType.OTHER
    
    def _is_ip_address(self, value: str) -> bool:
        """检查是否为IP地址"""
        import ipaddress
        try:
            ipaddress.ip_address(value)
            return True
        except ValueError:
            return False
    
    def _is_domain(self, value: str) -> bool:
        """检查是否为域名"""
        import re
        domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        return bool(re.match(domain_pattern, value))
    
    def _build_asset(
        self,
        mapped_data: Dict[str, Any],
        data_source: DataSource,
        asset_type: AssetType,
        source_id: Optional[str] = None,
        platform_id: Optional[str] = None,
        project_id: Optional[str] = None
    ) -> Asset:
        """构建资产对象"""
        
        # 构建元数据
        metadata = AssetMetadata(
            asset_id="",  # 稍后生成
            asset_type=asset_type,
            data_source=data_source,
            source_id=source_id,
            confidence=mapped_data.get("confidence", 1.0)
        )
        
        # 构建数据
        data = AssetData(
            value=mapped_data.get("value", ""),
            name=mapped_data.get("name"),
            description=mapped_data.get("description"),
            tags=mapped_data.get("tags", []),
            attributes=mapped_data.get("attributes", {})
        )
        
        # 构建关联
        relation = AssetRelation(
            platform_id=platform_id,
            project_id=project_id
        )
        
        # 构建处理信息
        processing = ProcessingInfo(
            field_mappings=mapped_data.get("_field_mappings", {})
        )
        
        return Asset(
            metadata=metadata,
            data=data,
            relation=relation,
            processing=processing
        )
    
    def _create_error_asset(
        self,
        raw_data: Dict[str, Any],
        data_source: DataSource,
        error_message: str
    ) -> Asset:
        """创建错误资产对象"""
        
        metadata = AssetMetadata(
            asset_id="",
            asset_type=AssetType.OTHER,
            data_source=data_source,
            processing_status=ProcessingStatus.FAILED,
            quality_level=QualityLevel.LOW
        )
        
        data = AssetData(
            value=str(raw_data.get("value", "unknown")),
            description=f"处理失败: {error_message}"
        )
        
        relation = AssetRelation()
        
        processing = ProcessingInfo(
            validation_errors=[error_message],
            processing_notes=f"原始数据: {json.dumps(raw_data, ensure_ascii=False)}"
        )
        
        asset = Asset(
            metadata=metadata,
            data=data,
            relation=relation,
            processing=processing
        )
        
        asset.metadata.asset_id = asset.generate_id()
        asset.processing.dedup_hash = asset.generate_dedup_hash()
        
        return asset


class AgentDataAdapter:
    """Agent数据适配器"""
    
    @staticmethod
    def adapt_agent_data(agent_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """适配Agent扫描结果"""
        adapted_data = []
        
        # 处理不同类型的Agent结果
        if "domains" in agent_result:
            for domain in agent_result["domains"]:
                adapted_data.append({
                    "value": domain.get("name", domain),
                    "confidence": domain.get("confidence", 0.8),
                    "attributes": {
                        "source_tool": agent_result.get("tool", "unknown"),
                        "scan_time": agent_result.get("timestamp"),
                        **domain.get("metadata", {})
                    }
                })
        
        if "ips" in agent_result:
            for ip in agent_result["ips"]:
                adapted_data.append({
                    "value": ip.get("address", ip),
                    "confidence": ip.get("confidence", 0.8),
                    "attributes": {
                        "source_tool": agent_result.get("tool", "unknown"),
                        "scan_time": agent_result.get("timestamp"),
                        **ip.get("metadata", {})
                    }
                })
        
        if "urls" in agent_result:
            for url in agent_result["urls"]:
                adapted_data.append({
                    "value": url.get("url", url),
                    "confidence": url.get("confidence", 0.8),
                    "attributes": {
                        "source_tool": agent_result.get("tool", "unknown"),
                        "scan_time": agent_result.get("timestamp"),
                        "status_code": url.get("status_code"),
                        "title": url.get("title"),
                        **url.get("metadata", {})
                    }
                })
        
        return adapted_data


class ManualImportAdapter:
    """手动导入数据适配器"""
    
    @staticmethod
    def adapt_csv_data(csv_rows: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """适配CSV导入数据"""
        adapted_data = []
        
        for row in csv_rows:
            # 基础字段映射
            adapted_row = {
                "value": row.get("value") or row.get("asset") or row.get("target"),
                "name": row.get("name") or row.get("title"),
                "description": row.get("description") or row.get("desc"),
                "tags": row.get("tags", "").split(",") if row.get("tags") else [],
                "confidence": float(row.get("confidence", 1.0)),
                "attributes": {}
            }
            
            # 将其他字段放入attributes
            for key, value in row.items():
                if key not in ["value", "asset", "target", "name", "title", "description", "desc", "tags", "confidence"]:
                    adapted_row["attributes"][key] = value
            
            adapted_data.append(adapted_row)
        
        return adapted_data
    
    @staticmethod
    def adapt_json_data(json_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """适配JSON导入数据"""
        # JSON数据通常已经是结构化的，只需要基本验证
        adapted_data = []
        
        for item in json_data:
            if isinstance(item, dict) and "value" in item:
                adapted_data.append(item)
            elif isinstance(item, str):
                # 简单字符串转换为资产对象
                adapted_data.append({"value": item})
        
        return adapted_data


class APIDataAdapter:
    """API数据适配器"""
    
    @staticmethod
    def adapt_third_party_api(api_response: Dict[str, Any], api_type: str) -> List[Dict[str, Any]]:
        """适配第三方API数据"""
        adapted_data = []
        
        if api_type == "shodan":
            # Shodan API适配
            if "matches" in api_response:
                for match in api_response["matches"]:
                    adapted_data.append({
                        "value": match.get("ip_str"),
                        "attributes": {
                            "port": match.get("port"),
                            "service": match.get("product"),
                            "banner": match.get("data"),
                            "location": match.get("location", {}),
                            "org": match.get("org"),
                            "isp": match.get("isp")
                        }
                    })
        
        elif api_type == "virustotal":
            # VirusTotal API适配
            if "data" in api_response:
                data = api_response["data"]
                adapted_data.append({
                    "value": data.get("id"),
                    "attributes": {
                        "reputation": data.get("attributes", {}).get("reputation"),
                        "last_analysis_stats": data.get("attributes", {}).get("last_analysis_stats"),
                        "categories": data.get("attributes", {}).get("categories")
                    }
                })
        
        return adapted_data
