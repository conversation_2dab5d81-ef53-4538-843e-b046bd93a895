"""
资产索引管理器
负责Elasticsearch索引的创建、管理和优化
"""

from elasticsearch import AsyncElasticsearch
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import logging
import json

# 避免循环导入，直接定义需要的枚举值
# from asset_management_v3 import AssetType, DataSource, ProcessingStatus, QualityLevel

logger = logging.getLogger(__name__)


class AssetIndexManager:
    """资产索引管理器"""
    
    def __init__(self, es_client: AsyncElasticsearch):
        self.es_client = es_client
        
        # 索引配置
        self.index_prefix = "assets-v3"
        self.template_name = "assets-v3-template"
        self.alias_name = "assets-current"
        
        # 分片策略
        self.default_shards = 3
        self.default_replicas = 1
        
    async def initialize(self) -> bool:
        """初始化索引管理器"""
        try:
            # 创建索引模板
            await self._create_index_template()
            
            # 创建当前月份索引
            current_index = await self._create_monthly_index()
            
            # 设置别名
            await self._setup_alias(current_index)
            
            logger.info("✅ 资产索引管理器初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 索引管理器初始化失败: {e}")
            return False
    
    async def _create_index_template(self) -> bool:
        """创建索引模板"""
        try:
            template = {
                "index_patterns": [f"{self.index_prefix}-*"],
                "template": {
                    "settings": {
                        "number_of_shards": self.default_shards,
                        "number_of_replicas": self.default_replicas,
                        "index": {
                            "refresh_interval": "5s",
                            "max_result_window": 50000,
                            "mapping": {
                                "total_fields": {
                                    "limit": 2000
                                }
                            }
                        },
                        "analysis": {
                            "analyzer": {
                                "asset_analyzer": {
                                    "type": "custom",
                                    "tokenizer": "keyword",
                                    "filter": ["lowercase", "trim"]
                                },
                                "text_analyzer": {
                                    "type": "custom",
                                    "tokenizer": "standard",
                                    "filter": ["lowercase", "stop", "snowball"]
                                }
                            }
                        }
                    },
                    "mappings": {
                        "properties": {
                            # 元数据字段
                            "metadata": {
                                "type": "object",
                                "properties": {
                                    "asset_id": {
                                        "type": "keyword",
                                        "index": True
                                    },
                                    "asset_type": {
                                        "type": "keyword",
                                        "index": True
                                    },
                                    "data_source": {
                                        "type": "keyword",
                                        "index": True
                                    },
                                    "source_id": {
                                        "type": "keyword",
                                        "index": True
                                    },
                                    "confidence": {
                                        "type": "float",
                                        "index": True
                                    },
                                    "quality_level": {
                                        "type": "keyword",
                                        "index": True
                                    },
                                    "processing_status": {
                                        "type": "keyword",
                                        "index": True
                                    },
                                    "created_at": {
                                        "type": "date",
                                        "format": "strict_date_optional_time||epoch_millis"
                                    },
                                    "updated_at": {
                                        "type": "date",
                                        "format": "strict_date_optional_time||epoch_millis"
                                    },
                                    "processed_at": {
                                        "type": "date",
                                        "format": "strict_date_optional_time||epoch_millis"
                                    }
                                }
                            },
                            
                            # 数据字段
                            "data": {
                                "type": "object",
                                "properties": {
                                    "value": {
                                        "type": "text",
                                        "analyzer": "asset_analyzer",
                                        "fields": {
                                            "keyword": {
                                                "type": "keyword",
                                                "ignore_above": 256
                                            },
                                            "search": {
                                                "type": "text",
                                                "analyzer": "text_analyzer"
                                            }
                                        }
                                    },
                                    "name": {
                                        "type": "text",
                                        "analyzer": "text_analyzer",
                                        "fields": {
                                            "keyword": {
                                                "type": "keyword",
                                                "ignore_above": 256
                                            }
                                        }
                                    },
                                    "description": {
                                        "type": "text",
                                        "analyzer": "text_analyzer"
                                    },
                                    "tags": {
                                        "type": "keyword",
                                        "index": True
                                    },
                                    "attributes": {
                                        "type": "object",
                                        "dynamic": True
                                    }
                                }
                            },
                            
                            # 关联字段
                            "relation": {
                                "type": "object",
                                "properties": {
                                    "platform_id": {
                                        "type": "keyword",
                                        "index": True
                                    },
                                    "project_id": {
                                        "type": "keyword",
                                        "index": True
                                    },
                                    "parent_asset_id": {
                                        "type": "keyword",
                                        "index": True
                                    },
                                    "related_assets": {
                                        "type": "keyword",
                                        "index": True
                                    }
                                }
                            },
                            
                            # 处理信息字段
                            "processing": {
                                "type": "object",
                                "properties": {
                                    "dedup_hash": {
                                        "type": "keyword",
                                        "index": True
                                    },
                                    "field_mappings": {
                                        "type": "object",
                                        "enabled": False  # 仅存储，不索引
                                    },
                                    "validation_errors": {
                                        "type": "keyword",
                                        "index": True
                                    },
                                    "processing_notes": {
                                        "type": "text",
                                        "analyzer": "text_analyzer",
                                        "index": False  # 仅存储，不搜索
                                    }
                                }
                            },
                            
                            # 全文搜索字段
                            "full_text": {
                                "type": "text",
                                "analyzer": "text_analyzer"
                            },
                            
                            # 地理位置字段（可选）
                            "location": {
                                "type": "geo_point"
                            },
                            
                            # 时间戳字段
                            "@timestamp": {
                                "type": "date",
                                "format": "strict_date_optional_time||epoch_millis"
                            }
                        }
                    }
                }
            }
            
            await self.es_client.indices.put_index_template(
                name=self.template_name,
                body=template
            )
            
            logger.info(f"✅ 创建索引模板: {self.template_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建索引模板失败: {e}")
            return False
    
    async def _create_monthly_index(self, date: Optional[datetime] = None) -> str:
        """创建月度索引"""
        if date is None:
            date = datetime.utcnow()
        
        index_name = f"{self.index_prefix}-{date.strftime('%Y-%m')}"
        
        try:
            # 检查索引是否已存在
            if await self.es_client.indices.exists(index=index_name):
                logger.info(f"索引 {index_name} 已存在")
                return index_name
            
            # 创建索引
            await self.es_client.indices.create(index=index_name)
            
            logger.info(f"✅ 创建月度索引: {index_name}")
            return index_name
            
        except Exception as e:
            logger.error(f"❌ 创建月度索引失败: {e}")
            raise
    
    async def _setup_alias(self, index_name: str) -> bool:
        """设置索引别名"""
        try:
            # 检查别名是否已存在
            alias_exists = await self.es_client.indices.exists_alias(name=self.alias_name)
            
            if alias_exists:
                # 获取当前别名指向的索引
                current_aliases = await self.es_client.indices.get_alias(name=self.alias_name)
                
                # 移除旧别名，添加新别名
                actions = []
                for old_index in current_aliases.keys():
                    actions.append({
                        "remove": {
                            "index": old_index,
                            "alias": self.alias_name
                        }
                    })
                
                actions.append({
                    "add": {
                        "index": index_name,
                        "alias": self.alias_name
                    }
                })
                
                await self.es_client.indices.update_aliases(body={"actions": actions})
            else:
                # 直接添加别名
                await self.es_client.indices.put_alias(
                    index=index_name,
                    name=self.alias_name
                )
            
            logger.info(f"✅ 设置别名 {self.alias_name} -> {index_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 设置别名失败: {e}")
            return False
    
    async def get_current_index(self) -> str:
        """获取当前活跃索引名"""
        try:
            aliases = await self.es_client.indices.get_alias(name=self.alias_name)
            if aliases:
                return list(aliases.keys())[0]
            else:
                # 如果别名不存在，创建当前月份索引
                return await self._create_monthly_index()
        except Exception:
            # 如果别名不存在，创建当前月份索引
            return await self._create_monthly_index()
    
    async def rotate_index(self) -> str:
        """轮转到新的月度索引"""
        try:
            # 创建新的月度索引
            new_index = await self._create_monthly_index()
            
            # 更新别名
            await self._setup_alias(new_index)
            
            logger.info(f"✅ 索引轮转完成: {new_index}")
            return new_index
            
        except Exception as e:
            logger.error(f"❌ 索引轮转失败: {e}")
            raise
    
    async def cleanup_old_indices(self, retention_days: int = 90) -> Dict[str, Any]:
        """清理旧索引"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
            
            # 获取所有匹配的索引
            indices = await self.es_client.indices.get(index=f"{self.index_prefix}-*")
            
            deleted_indices = []
            kept_indices = []
            
            for index_name in indices.keys():
                try:
                    # 从索引名提取日期
                    date_part = index_name.split('-')[-1]  # 格式: assets-v3-2025-01
                    index_date = datetime.strptime(date_part, '%Y-%m')
                    
                    if index_date < cutoff_date:
                        await self.es_client.indices.delete(index=index_name)
                        deleted_indices.append(index_name)
                        logger.info(f"删除旧索引: {index_name}")
                    else:
                        kept_indices.append(index_name)
                        
                except (ValueError, IndexError):
                    # 索引名格式不匹配，跳过
                    kept_indices.append(index_name)
                    continue
            
            return {
                "message": f"清理完成，删除 {len(deleted_indices)} 个旧索引",
                "deleted_indices": deleted_indices,
                "kept_indices": kept_indices,
                "retention_days": retention_days,
                "cutoff_date": cutoff_date.isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ 清理旧索引失败: {e}")
            raise
    
    async def get_index_stats(self) -> Dict[str, Any]:
        """获取索引统计信息"""
        try:
            stats = await self.es_client.indices.stats(index=f"{self.index_prefix}-*")
            
            total_docs = 0
            total_size = 0
            indices_info = {}
            
            for index_name, index_stats in stats['indices'].items():
                docs_count = index_stats['total']['docs']['count']
                store_size = index_stats['total']['store']['size_in_bytes']
                
                total_docs += docs_count
                total_size += store_size
                
                indices_info[index_name] = {
                    "docs_count": docs_count,
                    "size_bytes": store_size,
                    "size_mb": round(store_size / 1024 / 1024, 2)
                }
            
            return {
                "total_documents": total_docs,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / 1024 / 1024, 2),
                "indices_count": len(indices_info),
                "indices": indices_info
            }
            
        except Exception as e:
            logger.error(f"❌ 获取索引统计失败: {e}")
            raise


async def create_asset_index_manager(es_client: AsyncElasticsearch) -> AssetIndexManager:
    """创建资产索引管理器实例"""
    manager = AssetIndexManager(es_client)
    await manager.initialize()
    return manager
