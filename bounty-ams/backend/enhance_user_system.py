#!/usr/bin/env python3
"""
增强用户管理系统 - 数据库结构升级
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 同步数据库连接
SYNC_DATABASE_URL = 'postgresql://postgres:postgres@localhost:5432/bounty_ams'

def enhance_users_table(db):
    """增强用户表结构"""
    print("\n🔧 增强用户表结构...")
    
    # 添加新字段到users表
    new_columns = [
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS full_name VARCHAR(255)",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS phone VARCHAR(20)",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS avatar_url TEXT",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS department VARCHAR(100)",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS position VARCHAR(100)",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login_at TIMESTAMP WITH TIME ZONE",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login_ip INET",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS login_count INTEGER DEFAULT 0",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS password_changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS must_change_password BOOLEAN DEFAULT FALSE",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS account_locked_until TIMESTAMP WITH TIME ZONE",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS failed_login_attempts INTEGER DEFAULT 0",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS notes TEXT",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS created_by_user_id UUID REFERENCES users(id)",
    ]
    
    for sql in new_columns:
        try:
            db.execute(text(sql))
            print(f"  ✅ 添加字段成功")
        except Exception as e:
            print(f"  ⚠️  添加字段失败: {e}")

def create_roles_table(db):
    """创建角色表"""
    print("\n🔧 创建角色权限表...")
    
    # 创建角色表
    roles_table_sql = """
    CREATE TABLE IF NOT EXISTS roles (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(50) UNIQUE NOT NULL,
        display_name VARCHAR(100) NOT NULL,
        description TEXT,
        permissions JSONB NOT NULL DEFAULT '[]',
        is_system BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        created_by_user_id UUID REFERENCES users(id)
    );
    """
    
    try:
        db.execute(text(roles_table_sql))
        print("  ✅ 角色表创建成功")
    except Exception as e:
        print(f"  ⚠️  角色表创建失败: {e}")
    
    # 创建用户角色关联表
    user_roles_table_sql = """
    CREATE TABLE IF NOT EXISTS user_roles (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
        assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        assigned_by_user_id UUID REFERENCES users(id),
        UNIQUE(user_id, role_id)
    );
    """
    
    try:
        db.execute(text(user_roles_table_sql))
        print("  ✅ 用户角色关联表创建成功")
    except Exception as e:
        print(f"  ⚠️  用户角色关联表创建失败: {e}")

def create_user_activity_log_table(db):
    """创建用户活动日志表"""
    print("\n🔧 创建用户活动日志表...")
    
    activity_log_sql = """
    CREATE TABLE IF NOT EXISTS user_activity_logs (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE SET NULL,
        action VARCHAR(50) NOT NULL,
        resource_type VARCHAR(50),
        resource_id VARCHAR(255),
        details JSONB,
        ip_address INET,
        user_agent TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        session_id VARCHAR(255)
    );
    """
    
    try:
        db.execute(text(activity_log_sql))
        print("  ✅ 用户活动日志表创建成功")
    except Exception as e:
        print(f"  ⚠️  用户活动日志表创建失败: {e}")
    
    # 创建索引
    indexes = [
        "CREATE INDEX IF NOT EXISTS idx_user_activity_logs_user_id ON user_activity_logs(user_id)",
        "CREATE INDEX IF NOT EXISTS idx_user_activity_logs_action ON user_activity_logs(action)",
        "CREATE INDEX IF NOT EXISTS idx_user_activity_logs_created_at ON user_activity_logs(created_at)",
        "CREATE INDEX IF NOT EXISTS idx_user_activity_logs_resource ON user_activity_logs(resource_type, resource_id)",
    ]
    
    for index_sql in indexes:
        try:
            db.execute(text(index_sql))
            print("  ✅ 索引创建成功")
        except Exception as e:
            print(f"  ⚠️  索引创建失败: {e}")

def create_default_roles(db):
    """创建默认角色"""
    print("\n🔧 创建默认角色...")
    
    # 获取admin用户ID
    result = db.execute(text("SELECT id FROM users WHERE username = 'admin' LIMIT 1"))
    admin_user = result.fetchone()
    admin_user_id = admin_user[0] if admin_user else None
    
    default_roles = [
        {
            'name': 'super_admin',
            'display_name': '超级管理员',
            'description': '系统超级管理员，拥有所有权限',
            'permissions': [
                'user.create', 'user.read', 'user.update', 'user.delete',
                'role.create', 'role.read', 'role.update', 'role.delete',
                'platform.create', 'platform.read', 'platform.update', 'platform.delete',
                'project.create', 'project.read', 'project.update', 'project.delete',
                'asset.create', 'asset.read', 'asset.update', 'asset.delete',
                'agent.create', 'agent.read', 'agent.update', 'agent.delete',
                'task.create', 'task.read', 'task.update', 'task.delete',
                'workflow.create', 'workflow.read', 'workflow.update', 'workflow.delete',
                'system.admin'
            ],
            'is_system': True
        },
        {
            'name': 'admin',
            'display_name': '管理员',
            'description': '系统管理员，拥有大部分管理权限',
            'permissions': [
                'user.read', 'user.update',
                'platform.create', 'platform.read', 'platform.update', 'platform.delete',
                'project.create', 'project.read', 'project.update', 'project.delete',
                'asset.create', 'asset.read', 'asset.update', 'asset.delete',
                'agent.read', 'agent.update',
                'task.create', 'task.read', 'task.update', 'task.delete',
                'workflow.create', 'workflow.read', 'workflow.update', 'workflow.delete'
            ],
            'is_system': True
        },
        {
            'name': 'operator',
            'display_name': '操作员',
            'description': '系统操作员，可以管理资产和任务',
            'permissions': [
                'platform.read',
                'project.read', 'project.update',
                'asset.create', 'asset.read', 'asset.update',
                'agent.read',
                'task.create', 'task.read', 'task.update',
                'workflow.read', 'workflow.update'
            ],
            'is_system': True
        },
        {
            'name': 'viewer',
            'display_name': '查看者',
            'description': '只读用户，只能查看数据',
            'permissions': [
                'platform.read',
                'project.read',
                'asset.read',
                'agent.read',
                'task.read',
                'workflow.read'
            ],
            'is_system': True
        }
    ]
    
    for role_data in default_roles:
        # 检查角色是否已存在
        result = db.execute(text(f"SELECT id FROM roles WHERE name = '{role_data['name']}'"))
        existing_role = result.fetchone()
        
        if not existing_role:
            permissions_json = str(role_data['permissions']).replace("'", '"')
            
            insert_sql = f"""
            INSERT INTO roles (name, display_name, description, permissions, is_system, created_by_user_id)
            VALUES (
                '{role_data['name']}',
                '{role_data['display_name']}',
                '{role_data['description']}',
                '{permissions_json}'::jsonb,
                {role_data['is_system']},
                {'NULL' if not admin_user_id else f"'{admin_user_id}'"}
            )
            """
            
            try:
                db.execute(text(insert_sql))
                print(f"  ✅ 角色 {role_data['display_name']} 创建成功")
            except Exception as e:
                print(f"  ⚠️  角色 {role_data['display_name']} 创建失败: {e}")
        else:
            print(f"  ℹ️  角色 {role_data['display_name']} 已存在")

def assign_admin_role(db):
    """为admin用户分配超级管理员角色"""
    print("\n🔧 为admin用户分配角色...")
    
    # 获取admin用户和super_admin角色
    result = db.execute(text("""
        SELECT u.id as user_id, r.id as role_id
        FROM users u, roles r
        WHERE u.username = 'admin' AND r.name = 'super_admin'
    """))
    
    user_role = result.fetchone()
    
    if user_role:
        user_id, role_id = user_role
        
        # 检查是否已分配
        result = db.execute(text(f"""
            SELECT id FROM user_roles 
            WHERE user_id = '{user_id}' AND role_id = '{role_id}'
        """))
        
        if not result.fetchone():
            try:
                db.execute(text(f"""
                    INSERT INTO user_roles (user_id, role_id, assigned_by_user_id)
                    VALUES ('{user_id}', '{role_id}', '{user_id}')
                """))
                print("  ✅ admin用户角色分配成功")
            except Exception as e:
                print(f"  ⚠️  admin用户角色分配失败: {e}")
        else:
            print("  ℹ️  admin用户已有超级管理员角色")
    else:
        print("  ⚠️  未找到admin用户或super_admin角色")

def create_indexes(db):
    """创建性能优化索引"""
    print("\n🔧 创建性能优化索引...")
    
    indexes = [
        "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
        "CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active)",
        "CREATE INDEX IF NOT EXISTS idx_users_last_login_at ON users(last_login_at)",
        "CREATE INDEX IF NOT EXISTS idx_users_department ON users(department)",
        "CREATE INDEX IF NOT EXISTS idx_roles_name ON roles(name)",
        "CREATE INDEX IF NOT EXISTS idx_roles_is_active ON roles(is_active)",
        "CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON user_roles(user_id)",
        "CREATE INDEX IF NOT EXISTS idx_user_roles_role_id ON user_roles(role_id)",
    ]
    
    for index_sql in indexes:
        try:
            db.execute(text(index_sql))
            print("  ✅ 索引创建成功")
        except Exception as e:
            print(f"  ⚠️  索引创建失败: {e}")

def main():
    """主函数"""
    try:
        engine = create_engine(SYNC_DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        with SessionLocal() as db:
            print("🔧 增强用户管理系统 - 数据库结构升级")
            
            # 1. 增强用户表
            enhance_users_table(db)
            
            # 2. 创建角色权限表
            create_roles_table(db)
            
            # 3. 创建用户活动日志表
            create_user_activity_log_table(db)
            
            # 4. 创建默认角色
            create_default_roles(db)
            
            # 5. 为admin用户分配角色
            assign_admin_role(db)
            
            # 6. 创建索引
            create_indexes(db)
            
            # 提交所有更改
            db.commit()
            
            print(f"\n✅ 用户管理系统数据库结构升级完成")
            print("\n📋 升级总结:")
            print("1. ✅ 用户表字段增强（全名、手机、头像、部门等）")
            print("2. ✅ 角色权限系统（roles、user_roles表）")
            print("3. ✅ 用户活动日志系统")
            print("4. ✅ 默认角色创建（超级管理员、管理员、操作员、查看者）")
            print("5. ✅ 性能优化索引")
            print("6. ✅ admin用户角色分配")
            
    except Exception as e:
        print(f"❌ 升级过程中出错: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
