"""
基于动态模型的任务管理API路由
"""

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from typing import List, Optional, Dict, Any
from uuid import UUID, uuid4
from datetime import datetime
import json

from database import get_db, AsyncSessionLocal
from models_dynamic import ModelType, DynamicEntity, User
from auth import get_current_user

router = APIRouter(prefix="/api/task-management", tags=["task-management"])

# ==================== 任务模板管理 ====================

@router.get("/templates")
async def get_task_templates(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    search: Optional[str] = Query(None),
    category: Optional[str] = Query(None),
    tags: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务模板列表"""
    try:
        # 获取任务模板模型类型
        model_query = select(ModelType).where(ModelType.name == "task_template")
        model_result = await db.execute(model_query)
        template_model = model_result.scalar_one_or_none()

        if not template_model:
            raise HTTPException(status_code=404, detail="任务模板模型未找到")

        # 构建查询
        query = select(DynamicEntity).where(DynamicEntity.model_type_id == template_model.id)
        count_query = select(func.count(DynamicEntity.id)).where(DynamicEntity.model_type_id == template_model.id)

        # 搜索条件
        conditions = []

        if search:
            search_conditions = [
                DynamicEntity.entity_data.op('->>')('name').ilike(f'%{search}%'),
                DynamicEntity.entity_data.op('->>')('description').ilike(f'%{search}%'),
                DynamicEntity.entity_data.op('->>')('template_id').ilike(f'%{search}%')
            ]
            conditions.append(or_(*search_conditions))

        if category:
            conditions.append(DynamicEntity.entity_data.op('->>')('category') == category)

        if is_active is not None:
            conditions.append(DynamicEntity.entity_data.op('->>')('is_active') == str(is_active).lower())

        if tags:
            tag_list = [tag.strip() for tag in tags.split(',')]
            for tag in tag_list:
                conditions.append(DynamicEntity.entity_data.op('@>')({'tags': [tag]}))

        if conditions:
            query = query.where(and_(*conditions))
            count_query = count_query.where(and_(*conditions))

        # 获取总数
        count_result = await db.execute(count_query)
        total = count_result.scalar()

        # 获取数据
        query = query.order_by(DynamicEntity.created_at.desc()).offset(skip).limit(limit)
        result = await db.execute(query)
        templates = result.scalars().all()

        return {
            "templates": [
                {
                    "id": str(template.id),
                    **template.entity_data,
                    "created_at": template.created_at.isoformat(),
                    "updated_at": template.updated_at.isoformat() if template.updated_at else None
                }
                for template in templates
            ],
            "total": total,
            "skip": skip,
            "limit": limit
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务模板失败: {str(e)}")

@router.post("/templates")
async def create_task_template(
    template_data: Dict[str, Any],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建任务模板"""
    try:
        # 获取任务模板模型类型
        model_query = select(ModelType).where(ModelType.name == "task_template")
        model_result = await db.execute(model_query)
        template_model = model_result.scalar_one_or_none()

        if not template_model:
            raise HTTPException(status_code=404, detail="任务模板模型未找到")

        # 验证必需字段
        required_fields = ['template_id', 'name', 'category', 'tools', 'parameters']
        for field in required_fields:
            if field not in template_data:
                raise HTTPException(status_code=400, detail=f"缺少必需字段: {field}")

        # 检查模板ID是否已存在
        existing_query = select(DynamicEntity).where(
            DynamicEntity.model_type_id == template_model.id,
            DynamicEntity.entity_data.op('->>')('template_id') == template_data['template_id']
        )
        existing_result = await db.execute(existing_query)
        existing = existing_result.scalar_one_or_none()

        if existing:
            raise HTTPException(status_code=400, detail="模板ID已存在")

        # 设置默认值
        template_data.setdefault('version', '1.0.0')
        template_data.setdefault('is_active', True)
        template_data.setdefault('tags', [])
        template_data.setdefault('execution', {})
        template_data.setdefault('output_schema', {})

        # 创建实体
        entity = DynamicEntity(
            model_type_id=template_model.id,
            entity_data=template_data,
            created_by_user_id=current_user.id
        )

        db.add(entity)
        await db.commit()
        await db.refresh(entity)

        return {
            "id": str(entity.id),
            **entity.entity_data,
            "created_at": entity.created_at.isoformat(),
            "updated_at": entity.updated_at.isoformat() if entity.updated_at else None
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"创建任务模板失败: {str(e)}")

@router.get("/templates/{template_id}")
async def get_task_template(
    template_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取指定任务模板"""
    try:
        # 获取任务模板模型类型
        model_query = select(ModelType).where(ModelType.name == "task_template")
        model_result = await db.execute(model_query)
        template_model = model_result.scalar_one_or_none()

        if not template_model:
            raise HTTPException(status_code=404, detail="任务模板模型未找到")

        # 查找模板
        template_query = select(DynamicEntity).where(
            DynamicEntity.model_type_id == template_model.id,
            DynamicEntity.entity_data.op('->>')('template_id') == template_id
        )
        result = await db.execute(template_query)
        template = result.scalar_one_or_none()

        if not template:
            raise HTTPException(status_code=404, detail="任务模板未找到")

        return {
            "id": str(template.id),
            **template.entity_data,
            "created_at": template.created_at.isoformat(),
            "updated_at": template.updated_at.isoformat() if template.updated_at else None
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务模板失败: {str(e)}")

@router.put("/templates/{template_id}")
async def update_task_template(
    template_id: str,
    template_data: Dict[str, Any],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新任务模板"""
    try:
        # 获取任务模板模型类型
        model_query = select(ModelType).where(ModelType.name == "task_template")
        model_result = await db.execute(model_query)
        template_model = model_result.scalar_one_or_none()

        if not template_model:
            raise HTTPException(status_code=404, detail="任务模板模型未找到")

        # 查找模板
        template_query = select(DynamicEntity).where(
            DynamicEntity.model_type_id == template_model.id,
            DynamicEntity.entity_data.op('->>')('template_id') == template_id
        )
        result = await db.execute(template_query)
        template = result.scalar_one_or_none()

        if not template:
            raise HTTPException(status_code=404, detail="任务模板未找到")

        # 更新数据
        updated_data = template.entity_data.copy()
        updated_data.update(template_data)
        updated_data['template_id'] = template_id  # 确保ID不变

        template.entity_data = updated_data
        template.updated_at = datetime.utcnow()

        await db.commit()
        await db.refresh(template)

        return {
            "id": str(template.id),
            **template.entity_data,
            "created_at": template.created_at.isoformat(),
            "updated_at": template.updated_at.isoformat() if template.updated_at else None
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"更新任务模板失败: {str(e)}")

@router.delete("/templates/{template_id}")
async def delete_task_template(
    template_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除任务模板"""
    try:
        # 获取任务模板模型类型
        model_query = select(ModelType).where(ModelType.name == "task_template")
        model_result = await db.execute(model_query)
        template_model = model_result.scalar_one_or_none()

        if not template_model:
            raise HTTPException(status_code=404, detail="任务模板模型未找到")

        # 查找模板
        template_query = select(DynamicEntity).where(
            DynamicEntity.model_type_id == template_model.id,
            DynamicEntity.entity_data.op('->>')('template_id') == template_id
        )
        result = await db.execute(template_query)
        template = result.scalar_one_or_none()

        if not template:
            raise HTTPException(status_code=404, detail="任务模板未找到")

        # 软删除 - 设置为不活跃
        updated_data = template.entity_data.copy()
        updated_data['is_active'] = False
        template.entity_data = updated_data
        template.updated_at = datetime.utcnow()

        await db.commit()

        return {"message": "任务模板删除成功"}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"删除任务模板失败: {str(e)}")

# ==================== 工作流管理 ====================

@router.get("/workflows")
async def get_workflows(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    search: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取工作流列表"""
    try:
        # 获取工作流模型类型
        model_query = select(ModelType).where(ModelType.name == "workflow")
        model_result = await db.execute(model_query)
        workflow_model = model_result.scalar_one_or_none()

        if not workflow_model:
            raise HTTPException(status_code=404, detail="工作流模型未找到")

        # 构建查询
        query = select(DynamicEntity).where(DynamicEntity.model_type_id == workflow_model.id)
        count_query = select(func.count(DynamicEntity.id)).where(DynamicEntity.model_type_id == workflow_model.id)

        # 搜索条件
        conditions = []

        if search:
            search_conditions = [
                DynamicEntity.entity_data.op('->>')('name').ilike(f'%{search}%'),
                DynamicEntity.entity_data.op('->>')('description').ilike(f'%{search}%'),
                DynamicEntity.entity_data.op('->>')('workflow_id').ilike(f'%{search}%')
            ]
            conditions.append(or_(*search_conditions))

        if is_active is not None:
            conditions.append(DynamicEntity.entity_data.op('->>')('is_active') == str(is_active).lower())

        if conditions:
            query = query.where(and_(*conditions))
            count_query = count_query.where(and_(*conditions))

        # 获取总数
        count_result = await db.execute(count_query)
        total = count_result.scalar()

        # 获取数据
        query = query.order_by(DynamicEntity.created_at.desc()).offset(skip).limit(limit)
        result = await db.execute(query)
        workflows = result.scalars().all()

        return {
            "workflows": [
                {
                    "id": str(workflow.id),
                    **workflow.entity_data,
                    "created_at": workflow.created_at.isoformat(),
                    "updated_at": workflow.updated_at.isoformat() if workflow.updated_at else None
                }
                for workflow in workflows
            ],
            "total": total,
            "skip": skip,
            "limit": limit
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取工作流失败: {str(e)}")

@router.post("/workflows")
async def create_workflow(
    workflow_data: Dict[str, Any],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建工作流"""
    try:
        # 获取工作流模型类型
        model_query = select(ModelType).where(ModelType.name == "workflow")
        model_result = await db.execute(model_query)
        workflow_model = model_result.scalar_one_or_none()

        if not workflow_model:
            raise HTTPException(status_code=404, detail="工作流模型未找到")

        # 验证必需字段
        required_fields = ['workflow_id', 'name', 'stages', 'parameters']
        for field in required_fields:
            if field not in workflow_data:
                raise HTTPException(status_code=400, detail=f"缺少必需字段: {field}")

        # 检查工作流ID是否已存在
        existing_query = select(DynamicEntity).where(
            DynamicEntity.model_type_id == workflow_model.id,
            DynamicEntity.entity_data.op('->>')('workflow_id') == workflow_data['workflow_id']
        )
        existing_result = await db.execute(existing_query)
        existing = existing_result.scalar_one_or_none()

        if existing:
            raise HTTPException(status_code=400, detail="工作流ID已存在")

        # 设置默认值
        workflow_data.setdefault('version', '1.0.0')
        workflow_data.setdefault('is_active', True)
        workflow_data.setdefault('error_handling', {})
        workflow_data.setdefault('output_aggregation', {})

        # 创建实体
        entity = DynamicEntity(
            model_type_id=workflow_model.id,
            entity_data=workflow_data,
            created_by_user_id=current_user.id
        )

        db.add(entity)
        await db.commit()
        await db.refresh(entity)

        return {
            "id": str(entity.id),
            **entity.entity_data,
            "created_at": entity.created_at.isoformat(),
            "updated_at": entity.updated_at.isoformat() if entity.updated_at else None
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"创建工作流失败: {str(e)}")

# ==================== 任务执行管理 ====================

@router.post("/execute")
async def execute_task(
    execution_data: Dict[str, Any],
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """执行任务"""
    try:
        # 获取任务执行模型类型
        model_query = select(ModelType).where(ModelType.name == "task_execution")
        model_result = await db.execute(model_query)
        execution_model = model_result.scalar_one_or_none()

        if not execution_model:
            raise HTTPException(status_code=404, detail="任务执行模型未找到")

        # 验证必需字段
        required_fields = ['template_id', 'parameters']
        for field in required_fields:
            if field not in execution_data:
                raise HTTPException(status_code=400, detail=f"缺少必需字段: {field}")

        # 验证模板是否存在
        template_model_query = select(ModelType).where(ModelType.name == "task_template")
        template_model_result = await db.execute(template_model_query)
        template_model = template_model_result.scalar_one_or_none()

        if template_model:
            template_query = select(DynamicEntity).where(
                DynamicEntity.model_type_id == template_model.id,
                DynamicEntity.entity_data.op('->>')('template_id') == execution_data['template_id']
            )
            template_result = await db.execute(template_query)
            template = template_result.scalar_one_or_none()

            if not template:
                raise HTTPException(status_code=404, detail="任务模板未找到")

        # 生成执行ID
        execution_id = f"task_{datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')}"

        # 准备执行数据
        task_execution_data = {
            "execution_id": execution_id,
            "template_id": execution_data['template_id'],
            "template_name": template.entity_data.get('name', '') if template_model and template else '',
            "status": "pending",
            "parameters": execution_data['parameters'],
            "priority": execution_data.get('priority', 2),
            "progress": 0,
            "logs": []
        }

        # 创建执行记录
        entity = DynamicEntity(
            model_type_id=execution_model.id,
            entity_data=task_execution_data,
            created_by_user_id=current_user.id
        )

        db.add(entity)
        await db.commit()
        await db.refresh(entity)

        # 提交到后台任务队列
        background_tasks.add_task(
            process_task_execution,
            str(entity.id),
            execution_data['template_id'],
            execution_data['parameters']
        )

        return {
            "id": str(entity.id),
            **entity.entity_data,
            "created_at": entity.created_at.isoformat(),
            "updated_at": entity.updated_at.isoformat() if entity.updated_at else None
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"提交任务失败: {str(e)}")

@router.get("/executions")
async def get_task_executions(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    status: Optional[str] = Query(None),
    template_id: Optional[str] = Query(None),
    agent_id: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务执行列表"""
    try:
        # 获取任务执行模型类型
        model_query = select(ModelType).where(ModelType.name == "task_execution")
        model_result = await db.execute(model_query)
        execution_model = model_result.scalar_one_or_none()

        if not execution_model:
            raise HTTPException(status_code=404, detail="任务执行模型未找到")

        # 构建查询
        query = select(DynamicEntity).where(DynamicEntity.model_type_id == execution_model.id)
        count_query = select(func.count(DynamicEntity.id)).where(DynamicEntity.model_type_id == execution_model.id)

        # 过滤条件
        conditions = []

        if status:
            conditions.append(DynamicEntity.entity_data.op('->>')('status') == status)

        if template_id:
            conditions.append(DynamicEntity.entity_data.op('->>')('template_id') == template_id)

        if agent_id:
            conditions.append(DynamicEntity.entity_data.op('->>')('agent_id') == agent_id)

        if conditions:
            query = query.where(and_(*conditions))
            count_query = count_query.where(and_(*conditions))

        # 获取总数
        count_result = await db.execute(count_query)
        total = count_result.scalar()

        # 获取数据
        query = query.order_by(DynamicEntity.created_at.desc()).offset(skip).limit(limit)
        result = await db.execute(query)
        executions = result.scalars().all()

        return {
            "executions": [
                {
                    "id": str(execution.id),
                    **execution.entity_data,
                    "created_at": execution.created_at.isoformat(),
                    "updated_at": execution.updated_at.isoformat() if execution.updated_at else None
                }
                for execution in executions
            ],
            "total": total,
            "skip": skip,
            "limit": limit
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务执行列表失败: {str(e)}")

# ==================== 辅助函数 ====================

async def process_task_execution(entity_id: str, template_id: str, parameters: dict):
    """处理任务执行 (后台任务)"""
    # 这里应该实现实际的任务执行逻辑
    # 目前只是模拟
    import asyncio

    async with AsyncSessionLocal() as db:
        try:
            # 获取执行记录
            entity = await db.get(DynamicEntity, entity_id)
            if not entity:
                return

            # 更新状态为运行中
            updated_data = entity.entity_data.copy()
            updated_data['status'] = 'running'
            updated_data['start_time'] = datetime.utcnow().isoformat()
            updated_data['logs'] = ['任务开始执行']
            entity.entity_data = updated_data
            await db.commit()

            # 模拟任务执行
            await asyncio.sleep(5)

            # 更新状态为成功
            updated_data['status'] = 'success'
            updated_data['end_time'] = datetime.utcnow().isoformat()
            updated_data['progress'] = 100
            updated_data['output'] = {'result': f'模拟执行结果 for {template_id}'}
            updated_data['logs'].append('任务执行完成')
            entity.entity_data = updated_data
            await db.commit()

        except Exception as e:
            # 更新状态为失败
            if entity:
                updated_data = entity.entity_data.copy()
                updated_data['status'] = 'failed'
                updated_data['end_time'] = datetime.utcnow().isoformat()
                updated_data['error'] = str(e)
                updated_data['logs'].append(f'任务执行失败: {e}')
                entity.entity_data = updated_data
                await db.commit()