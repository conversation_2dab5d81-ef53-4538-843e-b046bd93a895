"""
任务管理相关的API路由
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
import json

from ..database import get_db
from ..models.task_models import (
    TaskTemplate, Workflow, Agent, TaskExecution, WorkflowExecution,
    TaskQueue, AgentHeartbeat, TaskLog, TaskStatus, WorkflowStatus, AgentStatus
)
from ..task_template_engine import template_engine
from ..workflow_engine import workflow_engine
from ..intelligent_task_scheduler import task_scheduler, agent_manager
from ..schemas.task_schemas import (
    TaskTemplateCreate, TaskTemplateResponse,
    WorkflowCreate, WorkflowResponse,
    AgentCreate, AgentResponse,
    TaskExecutionCreate, TaskExecutionResponse,
    WorkflowExecutionCreate, WorkflowExecutionResponse
)

router = APIRouter(prefix="/api/tasks", tags=["tasks"])

# ==================== 任务模板相关 ====================

@router.get("/templates", response_model=List[TaskTemplateResponse])
async def list_task_templates(
    category: Optional[str] = None,
    tags: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取任务模板列表"""
    try:
        query = db.query(TaskTemplate).filter(TaskTemplate.is_active == True)
        
        if category:
            query = query.filter(TaskTemplate.category == category)
        
        if tags:
            tag_list = tags.split(',')
            # 这里需要根据数据库类型调整JSON查询语法
            for tag in tag_list:
                query = query.filter(TaskTemplate.tags.contains([tag]))
        
        templates = query.all()
        
        return [
            TaskTemplateResponse(
                template_id=t.template_id,
                name=t.name,
                version=t.version,
                description=t.description,
                category=t.category,
                tags=t.tags or [],
                parameters=t.parameters or {},
                tools=t.tools or [],
                execution=t.execution or {},
                output_schema=t.output_schema or {},
                created_at=t.created_at,
                updated_at=t.updated_at
            ) for t in templates
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模板列表失败: {str(e)}")

@router.post("/templates", response_model=TaskTemplateResponse)
async def create_task_template(
    template: TaskTemplateCreate,
    db: Session = Depends(get_db)
):
    """创建任务模板"""
    try:
        # 检查模板ID是否已存在
        existing = db.query(TaskTemplate).filter(
            TaskTemplate.template_id == template.template_id
        ).first()
        if existing:
            raise HTTPException(status_code=400, detail="模板ID已存在")
        
        # 验证模板数据
        template_data = template.dict()
        if not template_engine.validate_template_data(template_data):
            raise HTTPException(status_code=400, detail="模板数据验证失败")
        
        # 创建数据库记录
        db_template = TaskTemplate(
            template_id=template.template_id,
            name=template.name,
            version=template.version,
            description=template.description,
            category=template.category,
            tags=template.tags,
            parameters=template.parameters,
            tools=template.tools,
            execution=template.execution,
            output_schema=template.output_schema,
            created_by=template.created_by
        )
        
        db.add(db_template)
        db.commit()
        db.refresh(db_template)
        
        # 注册到模板引擎
        template_engine.register_template_from_db(db_template)
        
        return TaskTemplateResponse.from_orm(db_template)
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建模板失败: {str(e)}")

@router.get("/templates/{template_id}", response_model=TaskTemplateResponse)
async def get_task_template(template_id: str, db: Session = Depends(get_db)):
    """获取指定任务模板"""
    template = db.query(TaskTemplate).filter(
        TaskTemplate.template_id == template_id,
        TaskTemplate.is_active == True
    ).first()
    
    if not template:
        raise HTTPException(status_code=404, detail="模板不存在")
    
    return TaskTemplateResponse.from_orm(template)

@router.put("/templates/{template_id}", response_model=TaskTemplateResponse)
async def update_task_template(
    template_id: str,
    template: TaskTemplateCreate,
    db: Session = Depends(get_db)
):
    """更新任务模板"""
    try:
        db_template = db.query(TaskTemplate).filter(
            TaskTemplate.template_id == template_id,
            TaskTemplate.is_active == True
        ).first()
        
        if not db_template:
            raise HTTPException(status_code=404, detail="模板不存在")
        
        # 更新字段
        for field, value in template.dict(exclude_unset=True).items():
            setattr(db_template, field, value)
        
        db_template.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(db_template)
        
        # 更新模板引擎
        template_engine.register_template_from_db(db_template)
        
        return TaskTemplateResponse.from_orm(db_template)
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新模板失败: {str(e)}")

@router.delete("/templates/{template_id}")
async def delete_task_template(template_id: str, db: Session = Depends(get_db)):
    """删除任务模板"""
    try:
        db_template = db.query(TaskTemplate).filter(
            TaskTemplate.template_id == template_id
        ).first()
        
        if not db_template:
            raise HTTPException(status_code=404, detail="模板不存在")
        
        # 软删除
        db_template.is_active = False
        db_template.updated_at = datetime.utcnow()
        db.commit()
        
        # 从模板引擎中移除
        template_engine.unregister_template(template_id)
        
        return {"message": "模板删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除模板失败: {str(e)}")

# ==================== 工作流相关 ====================

@router.get("/workflows", response_model=List[WorkflowResponse])
async def list_workflows(db: Session = Depends(get_db)):
    """获取工作流列表"""
    try:
        workflows = db.query(Workflow).filter(Workflow.is_active == True).all()
        return [WorkflowResponse.from_orm(w) for w in workflows]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取工作流列表失败: {str(e)}")

@router.post("/workflows", response_model=WorkflowResponse)
async def create_workflow(
    workflow: WorkflowCreate,
    db: Session = Depends(get_db)
):
    """创建工作流"""
    try:
        # 检查工作流ID是否已存在
        existing = db.query(Workflow).filter(
            Workflow.workflow_id == workflow.workflow_id
        ).first()
        if existing:
            raise HTTPException(status_code=400, detail="工作流ID已存在")
        
        # 创建数据库记录
        db_workflow = Workflow(
            workflow_id=workflow.workflow_id,
            name=workflow.name,
            version=workflow.version,
            description=workflow.description,
            parameters=workflow.parameters,
            stages=workflow.stages,
            error_handling=workflow.error_handling,
            output_aggregation=workflow.output_aggregation,
            created_by=workflow.created_by
        )
        
        db.add(db_workflow)
        db.commit()
        db.refresh(db_workflow)
        
        # 注册到工作流引擎
        workflow_engine.register_workflow_from_db(db_workflow)
        
        return WorkflowResponse.from_orm(db_workflow)
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建工作流失败: {str(e)}")

# ==================== Agent相关 ====================

@router.get("/agents", response_model=List[AgentResponse])
async def list_agents(db: Session = Depends(get_db)):
    """获取Agent列表"""
    try:
        agents = db.query(Agent).filter(Agent.is_active == True).all()
        return [AgentResponse.from_orm(a) for a in agents]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取Agent列表失败: {str(e)}")

@router.post("/agents", response_model=AgentResponse)
async def register_agent(
    agent: AgentCreate,
    db: Session = Depends(get_db)
):
    """注册Agent"""
    try:
        # 检查Agent ID是否已存在
        existing = db.query(Agent).filter(Agent.agent_id == agent.agent_id).first()
        if existing:
            raise HTTPException(status_code=400, detail="Agent ID已存在")
        
        # 生成API密钥
        import secrets
        api_key = secrets.token_urlsafe(32)
        
        # 创建数据库记录
        db_agent = Agent(
            agent_id=agent.agent_id,
            name=agent.name,
            capabilities=agent.capabilities,
            resource_limits=agent.resource_limits,
            max_capacity=agent.max_capacity,
            location=agent.location,
            tags=agent.tags,
            api_key=api_key
        )
        
        db.add(db_agent)
        db.commit()
        db.refresh(db_agent)
        
        # 注册到Agent管理器
        agent_manager.register_agent_from_db(db_agent)
        
        return AgentResponse.from_orm(db_agent)
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"注册Agent失败: {str(e)}")

@router.post("/agents/{agent_id}/heartbeat")
async def agent_heartbeat(
    agent_id: str,
    heartbeat_data: dict,
    db: Session = Depends(get_db)
):
    """Agent心跳"""
    try:
        # 更新Agent状态
        agent = db.query(Agent).filter(Agent.agent_id == agent_id).first()
        if not agent:
            raise HTTPException(status_code=404, detail="Agent不存在")
        
        agent.last_heartbeat = datetime.utcnow()
        agent.status = AgentStatus.ONLINE
        agent.current_load = heartbeat_data.get('current_load', 0)
        
        # 记录心跳
        heartbeat = AgentHeartbeat(
            agent_id=agent_id,
            status=agent.status,
            current_load=agent.current_load,
            running_tasks=heartbeat_data.get('running_tasks', []),
            system_info=heartbeat_data.get('system_info', {})
        )
        
        db.add(heartbeat)
        db.commit()
        
        # 更新Agent管理器
        agent_manager.update_agent_heartbeat(agent_id)
        
        return {"message": "心跳更新成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"心跳更新失败: {str(e)}")

# ==================== 任务执行相关 ====================

@router.post("/execute", response_model=TaskExecutionResponse)
async def execute_task(
    task: TaskExecutionCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """执行任务"""
    try:
        # 创建任务执行记录
        execution_id = f"task_{datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')}"
        
        db_execution = TaskExecution(
            execution_id=execution_id,
            template_id=task.template_id,
            parameters=task.parameters,
            priority=task.priority,
            timeout=task.timeout,
            created_by=task.created_by
        )
        
        db.add(db_execution)
        db.commit()
        db.refresh(db_execution)
        
        # 提交到任务调度器
        background_tasks.add_task(
            submit_task_to_scheduler,
            db_execution.execution_id,
            task.template_id,
            task.parameters,
            task.priority
        )
        
        return TaskExecutionResponse.from_orm(db_execution)
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"提交任务失败: {str(e)}")

@router.post("/workflows/execute", response_model=WorkflowExecutionResponse)
async def execute_workflow(
    workflow_exec: WorkflowExecutionCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """执行工作流"""
    try:
        # 创建工作流执行记录
        execution_id = f"workflow_{datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')}"
        
        db_execution = WorkflowExecution(
            execution_id=execution_id,
            workflow_id=workflow_exec.workflow_id,
            parameters=workflow_exec.parameters,
            created_by=workflow_exec.created_by
        )
        
        db.add(db_execution)
        db.commit()
        db.refresh(db_execution)
        
        # 提交到工作流引擎
        background_tasks.add_task(
            submit_workflow_to_engine,
            db_execution.execution_id,
            workflow_exec.workflow_id,
            workflow_exec.parameters
        )
        
        return WorkflowExecutionResponse.from_orm(db_execution)
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"提交工作流失败: {str(e)}")

@router.get("/executions", response_model=List[TaskExecutionResponse])
async def list_task_executions(
    status: Optional[str] = None,
    agent_id: Optional[str] = None,
    limit: int = 100,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """获取任务执行列表"""
    try:
        query = db.query(TaskExecution)
        
        if status:
            query = query.filter(TaskExecution.status == TaskStatus(status))
        if agent_id:
            query = query.filter(TaskExecution.agent_id == agent_id)
        
        executions = query.order_by(TaskExecution.created_at.desc()).offset(offset).limit(limit).all()
        return [TaskExecutionResponse.from_orm(e) for e in executions]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取执行列表失败: {str(e)}")

@router.get("/executions/{execution_id}", response_model=TaskExecutionResponse)
async def get_task_execution(execution_id: str, db: Session = Depends(get_db)):
    """获取任务执行详情"""
    execution = db.query(TaskExecution).filter(
        TaskExecution.execution_id == execution_id
    ).first()
    
    if not execution:
        raise HTTPException(status_code=404, detail="任务执行不存在")
    
    return TaskExecutionResponse.from_orm(execution)

@router.post("/executions/{execution_id}/cancel")
async def cancel_task_execution(execution_id: str, db: Session = Depends(get_db)):
    """取消任务执行"""
    try:
        execution = db.query(TaskExecution).filter(
            TaskExecution.execution_id == execution_id
        ).first()
        
        if not execution:
            raise HTTPException(status_code=404, detail="任务执行不存在")
        
        if execution.status not in [TaskStatus.PENDING, TaskStatus.RUNNING]:
            raise HTTPException(status_code=400, detail="任务无法取消")
        
        # 更新状态
        execution.status = TaskStatus.CANCELLED
        execution.end_time = datetime.utcnow()
        db.commit()
        
        # 通知调度器取消任务
        await task_scheduler.cancel_task(execution_id)
        
        return {"message": "任务取消成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")

# ==================== 辅助函数 ====================

async def submit_task_to_scheduler(execution_id: str, template_id: str, parameters: dict, priority: int):
    """提交任务到调度器"""
    try:
        # 渲染任务模板
        task_execution = template_engine.render_template(template_id, parameters)
        task_execution.execution_id = execution_id
        
        # 提交到调度器
        await task_scheduler.submit_task(task_execution, priority)
        
    except Exception as e:
        print(f"提交任务到调度器失败: {e}")

async def submit_workflow_to_engine(execution_id: str, workflow_id: str, parameters: dict):
    """提交工作流到引擎"""
    try:
        # 执行工作流
        result = await workflow_engine.execute_workflow(workflow_id, parameters)
        result.execution_id = execution_id
        
    except Exception as e:
        print(f"提交工作流到引擎失败: {e}")
