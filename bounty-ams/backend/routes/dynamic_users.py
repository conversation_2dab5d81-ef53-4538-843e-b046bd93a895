from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc, text
from sqlalchemy.orm import selectinload
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import uuid
import json
from passlib.context import CryptContext

from database import get_db
from models_dynamic import User, ModelType, DynamicEntity
from auth import get_current_user, require_admin
# from utils.error_handler import showErrorMessage

router = APIRouter(prefix="/dynamic-users", tags=["dynamic-users"])

# 密码加密
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

async def get_user_model_type(db: AsyncSession) -> ModelType:
    """获取用户动态模型类型"""
    result = await db.execute(
        select(ModelType).where(ModelType.name == "user")
    )
    model_type = result.scalar_one_or_none()
    if not model_type:
        raise HTTPException(status_code=500, detail="用户模型类型不存在")
    return model_type

async def get_role_model_type(db: AsyncSession) -> ModelType:
    """获取角色动态模型类型"""
    result = await db.execute(
        select(ModelType).where(ModelType.name == "user_role")
    )
    model_type = result.scalar_one_or_none()
    if not model_type:
        raise HTTPException(status_code=500, detail="角色模型类型不存在")
    return model_type

@router.get("/users")
async def get_dynamic_users(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    department: Optional[str] = None,
    is_active: Optional[bool] = None,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """获取动态用户列表"""
    user_model_type = await get_user_model_type(db)
    
    # 构建查询
    query = select(DynamicEntity).where(DynamicEntity.model_type_id == user_model_type.id)
    
    # 添加搜索条件
    if search:
        search_condition = or_(
            DynamicEntity.entity_data['username'].astext.ilike(f"%{search}%"),
            DynamicEntity.entity_data['email'].astext.ilike(f"%{search}%"),
            DynamicEntity.entity_data['full_name'].astext.ilike(f"%{search}%")
        )
        query = query.where(search_condition)

    if department:
        query = query.where(DynamicEntity.entity_data['department'].astext == department)

    if is_active is not None:
        query = query.where(DynamicEntity.entity_data['is_active'].astext == str(is_active).lower())
    
    query = query.offset(skip).limit(limit).order_by(desc(DynamicEntity.created_at))
    
    result = await db.execute(query)
    entities = result.scalars().all()
    
    # 转换为用户格式
    users = []
    for entity in entities:
        user_data = entity.entity_data.copy()
        user_data['id'] = str(entity.id)
        user_data['created_at'] = entity.created_at.isoformat() if entity.created_at else None
        user_data['updated_at'] = entity.updated_at.isoformat() if entity.updated_at else None
        user_data['created_by_user_id'] = str(entity.created_by_user_id) if entity.created_by_user_id else None
        
        # 获取用户角色（这里简化处理，实际应该通过关联查询）
        user_data['roles'] = []
        
        users.append(user_data)
    
    return users

@router.get("/users/{user_id}")
async def get_dynamic_user(
    user_id: str,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """获取动态用户详情"""
    user_model_type = await get_user_model_type(db)
    
    try:
        user_uuid = uuid.UUID(user_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="无效的用户ID格式")
    
    result = await db.execute(
        select(DynamicEntity).where(
            and_(
                DynamicEntity.model_type_id == user_model_type.id,
                DynamicEntity.id == user_uuid
            )
        )
    )
    entity = result.scalar_one_or_none()
    
    if not entity:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    user_data = entity.entity_data.copy()
    user_data['id'] = str(entity.id)
    user_data['created_at'] = entity.created_at.isoformat() if entity.created_at else None
    user_data['updated_at'] = entity.updated_at.isoformat() if entity.updated_at else None
    user_data['created_by_user_id'] = str(entity.created_by_user_id) if entity.created_by_user_id else None
    user_data['roles'] = []
    
    return user_data

@router.post("/users")
async def create_dynamic_user(
    user_data: Dict[str, Any],
    request: Request,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """创建动态用户"""
    user_model_type = await get_user_model_type(db)
    
    # 验证必需字段
    required_fields = ['username', 'email', 'password']
    for field in required_fields:
        if field not in user_data or not user_data[field]:
            raise HTTPException(status_code=400, detail=f"缺少必需字段: {field}")
    
    # 检查用户名和邮箱唯一性
    existing_query = select(DynamicEntity).where(
        and_(
            DynamicEntity.model_type_id == user_model_type.id,
            or_(
                DynamicEntity.entity_data['username'].astext == user_data["username"],
                DynamicEntity.entity_data['email'].astext == user_data["email"]
            )
        )
    )
    result = await db.execute(existing_query)
    existing_user = result.scalar_one_or_none()
    
    if existing_user:
        existing_data = existing_user.entity_data
        if existing_data.get("username") == user_data["username"]:
            raise HTTPException(status_code=400, detail="用户名已存在")
        else:
            raise HTTPException(status_code=400, detail="邮箱已存在")
    
    # 处理密码
    password = user_data.pop('password')
    hashed_password = get_password_hash(password)
    
    # 构建实体数据
    entity_data = {
        'username': user_data['username'],
        'email': user_data['email'],
        'hashed_password': hashed_password,
        'full_name': user_data.get('full_name'),
        'phone': user_data.get('phone'),
        'department': user_data.get('department'),
        'position': user_data.get('position'),
        'avatar_url': user_data.get('avatar_url'),
        'is_admin': user_data.get('is_admin', False),
        'is_active': user_data.get('is_active', True),
        'notes': user_data.get('notes'),
        'must_change_password': user_data.get('must_change_password', False),
        'login_count': 0,
        'failed_login_attempts': 0,
        'password_changed_at': datetime.utcnow().isoformat()
    }
    
    # 移除None值
    entity_data = {k: v for k, v in entity_data.items() if v is not None}
    
    # 创建动态实体
    new_entity = DynamicEntity(
        model_type_id=user_model_type.id,
        entity_data=entity_data,
        created_by_user_id=current_user.id
    )
    
    db.add(new_entity)
    await db.commit()
    await db.refresh(new_entity)
    
    # 返回用户数据
    result_data = new_entity.entity_data.copy()
    result_data['id'] = str(new_entity.id)
    result_data['created_at'] = new_entity.created_at.isoformat()
    result_data['created_by_user_id'] = str(new_entity.created_by_user_id)
    result_data.pop('hashed_password', None)  # 不返回密码哈希
    
    return result_data

@router.put("/users/{user_id}")
async def update_dynamic_user(
    user_id: str,
    user_data: Dict[str, Any],
    request: Request,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """更新动态用户"""
    user_model_type = await get_user_model_type(db)
    
    try:
        user_uuid = uuid.UUID(user_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="无效的用户ID格式")
    
    # 获取现有用户
    result = await db.execute(
        select(DynamicEntity).where(
            and_(
                DynamicEntity.model_type_id == user_model_type.id,
                DynamicEntity.id == user_uuid
            )
        )
    )
    entity = result.scalar_one_or_none()
    
    if not entity:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 检查用户名和邮箱唯一性
    if 'username' in user_data or 'email' in user_data:
        conditions = []
        if 'username' in user_data and user_data['username'] != entity.entity_data.get('username'):
            conditions.append(DynamicEntity.entity_data['username'].astext == user_data["username"])
        if 'email' in user_data and user_data['email'] != entity.entity_data.get('email'):
            conditions.append(DynamicEntity.entity_data['email'].astext == user_data["email"])
        
        if conditions:
            existing_query = select(DynamicEntity).where(
                and_(
                    DynamicEntity.model_type_id == user_model_type.id,
                    DynamicEntity.id != user_uuid,
                    or_(*conditions)
                )
            )
            result = await db.execute(existing_query)
            existing_user = result.scalar_one_or_none()
            
            if existing_user:
                existing_data = existing_user.entity_data
                if 'username' in user_data and existing_data.get("username") == user_data["username"]:
                    raise HTTPException(status_code=400, detail="用户名已存在")
                if 'email' in user_data and existing_data.get("email") == user_data["email"]:
                    raise HTTPException(status_code=400, detail="邮箱已存在")
    
    # 更新实体数据
    updated_data = entity.entity_data.copy()
    
    # 更新允许的字段
    allowed_fields = ['username', 'email', 'full_name', 'phone', 'department', 'position', 
                     'avatar_url', 'is_admin', 'is_active', 'notes', 'must_change_password']
    
    for field in allowed_fields:
        if field in user_data:
            updated_data[field] = user_data[field]
    
    entity.entity_data = updated_data
    entity.updated_at = datetime.utcnow()
    
    await db.commit()
    await db.refresh(entity)
    
    # 返回更新后的数据
    result_data = entity.entity_data.copy()
    result_data['id'] = str(entity.id)
    result_data['created_at'] = entity.created_at.isoformat() if entity.created_at else None
    result_data['updated_at'] = entity.updated_at.isoformat() if entity.updated_at else None
    result_data['created_by_user_id'] = str(entity.created_by_user_id) if entity.created_by_user_id else None
    result_data.pop('hashed_password', None)  # 不返回密码哈希
    
    return result_data

@router.delete("/users/{user_id}")
async def delete_dynamic_user(
    user_id: str,
    request: Request,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """删除动态用户（真删除）"""
    user_model_type = await get_user_model_type(db)
    
    try:
        user_uuid = uuid.UUID(user_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="无效的用户ID格式")
    
    if str(user_uuid) == str(current_user.id):
        raise HTTPException(status_code=400, detail="不能删除自己的账户")
    
    # 获取用户
    result = await db.execute(
        select(DynamicEntity).where(
            and_(
                DynamicEntity.model_type_id == user_model_type.id,
                DynamicEntity.id == user_uuid
            )
        )
    )
    entity = result.scalar_one_or_none()
    
    if not entity:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 真删除
    await db.delete(entity)
    await db.commit()
    
    return {"message": "用户已删除"}

@router.get("/roles")
async def get_dynamic_roles(
    skip: int = 0,
    limit: int = 100,
    is_active: Optional[bool] = None,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """获取动态角色列表"""
    role_model_type = await get_role_model_type(db)
    
    query = select(DynamicEntity).where(DynamicEntity.model_type_id == role_model_type.id)
    
    if is_active is not None:
        query = query.where(DynamicEntity.entity_data['is_active'].astext == str(is_active).lower())

    query = query.offset(skip).limit(limit).order_by(DynamicEntity.entity_data['name'].astext)
    
    result = await db.execute(query)
    entities = result.scalars().all()
    
    # 转换为角色格式
    roles = []
    for entity in entities:
        role_data = entity.entity_data.copy()
        role_data['id'] = str(entity.id)
        role_data['created_at'] = entity.created_at.isoformat() if entity.created_at else None
        role_data['updated_at'] = entity.updated_at.isoformat() if entity.updated_at else None
        role_data['created_by_user_id'] = str(entity.created_by_user_id) if entity.created_by_user_id else None
        roles.append(role_data)
    
    return roles
