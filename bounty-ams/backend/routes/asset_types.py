from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from sqlalchemy.orm import selectinload
from typing import List
from uuid import UUID

from database import get_db
from models_dynamic import AssetType, AssetT<PERSON><PERSON><PERSON>, User
from schemas_dynamic import AssetTypeCreate, AssetTypeUpdate, AssetType as AssetTypeSchema, AssetTypeField as AssetTypeFieldSchema
from auth import get_current_user, get_current_admin_user

router = APIRouter(prefix="/asset-types", tags=["asset-types"])


@router.get("/", response_model=List[AssetTypeSchema])
async def get_asset_types(
    skip: int = 0,
    limit: int = 100,
    include_inactive: bool = False,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get all asset types with their fields"""
    query = select(AssetType).options(selectinload(AssetType.fields))
    
    if not include_inactive:
        query = query.where(AssetType.is_active == True)
    
    query = query.offset(skip).limit(limit)
    result = await db.execute(query)
    asset_types = result.scalars().all()
    
    return asset_types


@router.get("/{asset_type_id}", response_model=AssetTypeSchema)
async def get_asset_type(
    asset_type_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific asset type by ID"""
    query = select(AssetType).options(selectinload(AssetType.fields)).where(AssetType.id == asset_type_id)
    result = await db.execute(query)
    asset_type = result.scalar_one_or_none()
    
    if not asset_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset type not found"
        )
    
    return asset_type


@router.post("/", response_model=AssetTypeSchema, status_code=status.HTTP_201_CREATED)
async def create_asset_type(
    asset_type_data: AssetTypeCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """Create a new asset type"""
    # Check if asset type name already exists
    existing_query = select(AssetType).where(AssetType.name == asset_type_data.name)
    existing_result = await db.execute(existing_query)
    if existing_result.scalar_one_or_none():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Asset type with this name already exists"
        )
    
    # Create asset type
    asset_type = AssetType(
        name=asset_type_data.name,
        display_name=asset_type_data.display_name,
        description=asset_type_data.description,
        icon=asset_type_data.icon,
        color=asset_type_data.color,
        is_active=asset_type_data.is_active,
        created_by_user_id=current_user.id
    )
    
    db.add(asset_type)
    await db.flush()  # Get the ID
    
    # Create fields
    for field_data in asset_type_data.fields:
        field = AssetTypeField(
            asset_type_id=asset_type.id,
            field_name=field_data.field_name,
            field_type=field_data.field_type,
            display_name=field_data.display_name,
            description=field_data.description,
            is_required=field_data.is_required,
            is_searchable=field_data.is_searchable,
            is_filterable=field_data.is_filterable,
            default_value=field_data.default_value,
            validation_rules=field_data.validation_rules,
            field_options=field_data.field_options,
            sort_order=field_data.sort_order
        )
        db.add(field)
    
    await db.commit()
    await db.refresh(asset_type)
    
    # Load fields for response
    query = select(AssetType).options(selectinload(AssetType.fields)).where(AssetType.id == asset_type.id)
    result = await db.execute(query)
    return result.scalar_one()


@router.put("/{asset_type_id}", response_model=AssetTypeSchema)
async def update_asset_type(
    asset_type_id: UUID,
    asset_type_data: AssetTypeUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """Update an asset type"""
    # Get existing asset type
    query = select(AssetType).options(selectinload(AssetType.fields)).where(AssetType.id == asset_type_id)
    result = await db.execute(query)
    asset_type = result.scalar_one_or_none()
    
    if not asset_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset type not found"
        )
    
    # Check if it's a system type
    if asset_type.is_system:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot modify system asset types"
        )
    
    # Update asset type fields
    update_data = asset_type_data.dict(exclude_unset=True)
    fields_data = update_data.pop("fields", None)
    
    for field, value in update_data.items():
        setattr(asset_type, field, value)
    
    # Update fields if provided
    if fields_data is not None:
        # Delete existing fields
        await db.execute(delete(AssetTypeField).where(AssetTypeField.asset_type_id == asset_type_id))
        
        # Create new fields
        for field_data in fields_data:
            field = AssetTypeField(
                asset_type_id=asset_type.id,
                field_name=field_data.field_name,
                field_type=field_data.field_type,
                display_name=field_data.display_name,
                description=field_data.description,
                is_required=field_data.is_required,
                is_searchable=field_data.is_searchable,
                is_filterable=field_data.is_filterable,
                default_value=field_data.default_value,
                validation_rules=field_data.validation_rules,
                field_options=field_data.field_options,
                sort_order=field_data.sort_order
            )
            db.add(field)
    
    await db.commit()
    await db.refresh(asset_type)
    
    # Reload with fields
    query = select(AssetType).options(selectinload(AssetType.fields)).where(AssetType.id == asset_type_id)
    result = await db.execute(query)
    return result.scalar_one()


@router.delete("/{asset_type_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_asset_type(
    asset_type_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """Delete an asset type"""
    # Get existing asset type
    query = select(AssetType).where(AssetType.id == asset_type_id)
    result = await db.execute(query)
    asset_type = result.scalar_one_or_none()
    
    if not asset_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset type not found"
        )
    
    # Check if it's a system type
    if asset_type.is_system:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete system asset types"
        )
    
    # 检查是否有现有资产使用此类型
    from elasticsearch_client import get_es_client
    try:
        es_client = await get_es_client()
        # 在Elasticsearch中搜索使用此资产类型的资产
        search_body = {
            "query": {
                "term": {"asset_type_id.keyword": str(asset_type_id)}
            },
            "size": 0
        }
        
        response = await es_client.search(index="assets-*", body=search_body)
        asset_count = response.get("hits", {}).get("total", {}).get("value", 0)
        
        if asset_count > 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot delete asset type as it is used by {asset_count} asset(s). Please reassign or delete these assets first."
            )
    except Exception as e:
        # 如果ES查询失败，允许删除但记录警告
        print(f"Warning: Could not check asset usage in Elasticsearch: {e}")
    
    # 软删除资产类型
    asset_type.is_active = False
    await db.commit()


@router.get("/field-types/", response_model=List[dict])
async def get_supported_field_types(
    current_user: User = Depends(get_current_user)
):
    """Get supported field types for asset type creation"""
    field_types = [
        {
            "type": "text",
            "display_name": "Text",
            "description": "Single line text input",
            "validation_options": ["minLength", "maxLength", "pattern"]
        },
        {
            "type": "textarea",
            "display_name": "Textarea",
            "description": "Multi-line text input",
            "validation_options": ["minLength", "maxLength"]
        },
        {
            "type": "number",
            "display_name": "Number",
            "description": "Numeric input",
            "validation_options": ["min", "max", "step"]
        },
        {
            "type": "ip",
            "display_name": "IP Address",
            "description": "IPv4 or IPv6 address",
            "validation_options": ["ipv4", "ipv6"]
        },
        {
            "type": "url",
            "display_name": "URL",
            "description": "Web URL",
            "validation_options": ["protocol"]
        },
        {
            "type": "email",
            "display_name": "Email",
            "description": "Email address",
            "validation_options": []
        },
        {
            "type": "date",
            "display_name": "Date",
            "description": "Date picker",
            "validation_options": ["min", "max"]
        },
        {
            "type": "datetime",
            "display_name": "Date & Time",
            "description": "Date and time picker",
            "validation_options": ["min", "max"]
        },
        {
            "type": "select",
            "display_name": "Select",
            "description": "Single selection from predefined options",
            "validation_options": [],
            "requires_options": True
        },
        {
            "type": "multi_select",
            "display_name": "Multi Select",
            "description": "Multiple selection from predefined options",
            "validation_options": ["minItems", "maxItems"],
            "requires_options": True
        },
        {
            "type": "boolean",
            "display_name": "Boolean",
            "description": "Yes/No or True/False",
            "validation_options": []
        },
        {
            "type": "json",
            "display_name": "JSON",
            "description": "JSON object",
            "validation_options": ["schema"]
        }
    ]
    
    return field_types