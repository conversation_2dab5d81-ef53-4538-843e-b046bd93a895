from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc, text
from sqlalchemy.orm import selectinload
from typing import List, Optional
from datetime import datetime, timedelta
import uuid
import json

from database import get_db
from models_dynamic import User, Role, UserRole, UserActivityLog
from schemas_dynamic import (
    User as UserSchema, UserCreate, UserUpdate, UserPasswordUpdate, UserPasswordReset,
    Role as RoleSchema, RoleCreate, RoleUpdate, RoleSimple,
    UserRoleAssign, UserRoleResponse,
    UserActivityLog as UserActivityLogSchema, UserActivityLogCreate
)
from auth import get_current_user, require_admin, get_password_hash, verify_password

router = APIRouter()

# 用户管理API
@router.get("/users", response_model=List[UserSchema])
async def get_users(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    department: Optional[str] = None,
    is_active: Optional[bool] = None,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """获取用户列表"""
    # 使用原生SQL查询以确保数据正确返回
    sql_query = """
    SELECT
        u.id, u.username, u.email, u.hashed_password, u.is_admin, u.is_active,
        u.full_name, u.phone, u.avatar_url, u.department, u.position,
        u.last_login_at, u.last_login_ip, u.login_count, u.password_changed_at,
        u.must_change_password, u.account_locked_until, u.failed_login_attempts,
        u.notes, u.created_by_user_id, u.created_at, u.updated_at,
        COALESCE(
            json_agg(
                json_build_object(
                    'id', r.id,
                    'name', r.name,
                    'display_name', r.display_name
                )
            ) FILTER (WHERE r.id IS NOT NULL),
            '[]'::json
        ) as roles
    FROM users u
    LEFT JOIN user_roles ur ON u.id = ur.user_id
    LEFT JOIN roles r ON ur.role_id = r.id AND r.is_active = true
    WHERE 1=1
    """

    params = {}

    # 添加搜索条件
    if search:
        sql_query += " AND (u.username ILIKE :search OR u.email ILIKE :search OR u.full_name ILIKE :search)"
        params['search'] = f"%{search}%"

    if department:
        sql_query += " AND u.department = :department"
        params['department'] = department

    if is_active is not None:
        sql_query += " AND u.is_active = :is_active"
        params['is_active'] = is_active

    sql_query += """
    GROUP BY u.id, u.username, u.email, u.hashed_password, u.is_admin, u.is_active,
             u.full_name, u.phone, u.avatar_url, u.department, u.position,
             u.last_login_at, u.last_login_ip, u.login_count, u.password_changed_at,
             u.must_change_password, u.account_locked_until, u.failed_login_attempts,
             u.notes, u.created_by_user_id, u.created_at, u.updated_at
    ORDER BY u.created_at DESC
    LIMIT :limit OFFSET :skip
    """

    params['limit'] = limit
    params['skip'] = skip

    result = await db.execute(text(sql_query), params)
    rows = result.fetchall()

    # 转换为用户对象
    users = []
    for row in rows:
        user_data = {
            'id': str(row[0]),
            'username': row[1],
            'email': row[2],
            'is_admin': row[4],
            'is_active': row[5],
            'full_name': row[6],
            'phone': row[7],
            'avatar_url': row[8],
            'department': row[9],
            'position': row[10],
            'last_login_at': row[11],
            'last_login_ip': row[12],
            'login_count': row[13] or 0,
            'password_changed_at': row[14],
            'must_change_password': row[15] or False,
            'account_locked_until': row[16],
            'failed_login_attempts': row[17] or 0,
            'notes': row[18],
            'created_at': row[20],
            'updated_at': row[21],
            'created_by_user_id': str(row[19]) if row[19] else None,
            'roles': row[22] if row[22] else []
        }
        users.append(user_data)

    return users

@router.get("/users/{user_id}", response_model=UserSchema)
async def get_user(
    user_id: uuid.UUID,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """获取用户详情"""
    query = select(User).options(
        selectinload(User.user_roles).selectinload(UserRole.role)
    ).where(User.id == user_id)
    
    result = await db.execute(query)
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 添加角色信息
    user.roles = [RoleSimple(id=ur.role.id, name=ur.role.name, display_name=ur.role.display_name) 
                 for ur in user.user_roles if ur.role.is_active]
    
    return user

@router.post("/users", response_model=UserSchema)
async def create_user(
    user_data: UserCreate,
    request: Request,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """创建新用户"""
    # 检查用户名和邮箱是否已存在
    result = await db.execute(
        select(User).where(
            or_(User.username == user_data.username, User.email == user_data.email)
        )
    )
    existing_user = result.scalar_one_or_none()
    
    if existing_user:
        if existing_user.username == user_data.username:
            raise HTTPException(status_code=400, detail="用户名已存在")
        else:
            raise HTTPException(status_code=400, detail="邮箱已存在")
    
    # 创建新用户
    hashed_password = get_password_hash(user_data.password)
    new_user = User(
        username=user_data.username,
        email=user_data.email,
        full_name=user_data.full_name,
        phone=user_data.phone,
        department=user_data.department,
        position=user_data.position,
        avatar_url=user_data.avatar_url,
        hashed_password=hashed_password,
        is_admin=user_data.is_admin,
        notes=user_data.notes,
        must_change_password=user_data.must_change_password,
        created_by_user_id=current_user.id
    )
    
    db.add(new_user)
    await db.commit()
    await db.refresh(new_user)
    
    # 记录活动日志
    await log_user_activity(
        db, current_user.id, "user.create", "user", str(new_user.id),
        {"username": user_data.username, "email": user_data.email},
        request
    )
    
    return new_user

@router.put("/users/{user_id}", response_model=UserSchema)
async def update_user(
    user_id: uuid.UUID,
    user_data: UserUpdate,
    request: Request,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """更新用户信息"""
    # 获取用户
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 检查用户名和邮箱唯一性
    if user_data.username and user_data.username != user.username:
        result = await db.execute(
            select(User).where(and_(User.username == user_data.username, User.id != user_id))
        )
        if result.scalar_one_or_none():
            raise HTTPException(status_code=400, detail="用户名已存在")
    
    if user_data.email and user_data.email != user.email:
        result = await db.execute(
            select(User).where(and_(User.email == user_data.email, User.id != user_id))
        )
        if result.scalar_one_or_none():
            raise HTTPException(status_code=400, detail="邮箱已存在")
    
    # 更新用户信息
    update_data = user_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(user, field, value)
    
    user.updated_at = datetime.utcnow()
    
    await db.commit()
    await db.refresh(user)
    
    # 记录活动日志
    await log_user_activity(
        db, current_user.id, "user.update", "user", str(user_id),
        {"updated_fields": list(update_data.keys())},
        request
    )
    
    return user

@router.delete("/users/{user_id}")
async def delete_user(
    user_id: uuid.UUID,
    request: Request,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """删除用户（软删除）"""
    if user_id == current_user.id:
        raise HTTPException(status_code=400, detail="不能删除自己的账户")
    
    # 获取用户
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 软删除（设置为非活跃状态）
    user.is_active = False
    user.updated_at = datetime.utcnow()
    
    await db.commit()
    
    # 记录活动日志
    await log_user_activity(
        db, current_user.id, "user.delete", "user", str(user_id),
        {"username": user.username},
        request
    )
    
    return {"message": "用户已删除"}

@router.post("/users/{user_id}/password-reset")
async def reset_user_password(
    user_id: uuid.UUID,
    password_data: UserPasswordReset,
    request: Request,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """重置用户密码"""
    # 获取用户
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 重置密码
    user.hashed_password = get_password_hash(password_data.new_password)
    user.password_changed_at = datetime.utcnow()
    user.must_change_password = password_data.must_change_password
    user.failed_login_attempts = 0
    user.account_locked_until = None
    user.updated_at = datetime.utcnow()
    
    await db.commit()
    
    # 记录活动日志
    await log_user_activity(
        db, current_user.id, "user.password_reset", "user", str(user_id),
        {"username": user.username, "must_change_password": password_data.must_change_password},
        request
    )
    
    return {"message": "密码重置成功"}

# 辅助函数
async def log_user_activity(
    db: AsyncSession,
    user_id: uuid.UUID,
    action: str,
    resource_type: Optional[str] = None,
    resource_id: Optional[str] = None,
    details: Optional[dict] = None,
    request: Optional[Request] = None
):
    """记录用户活动日志"""
    ip_address = None
    user_agent = None
    
    if request:
        ip_address = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")
    
    log_entry = UserActivityLog(
        user_id=user_id,
        action=action,
        resource_type=resource_type,
        resource_id=resource_id,
        details=details,
        ip_address=ip_address,
        user_agent=user_agent
    )
    
    db.add(log_entry)
    await db.commit()

# 角色管理API
@router.get("/roles", response_model=List[RoleSchema])
async def get_roles(
    skip: int = 0,
    limit: int = 100,
    is_active: Optional[bool] = None,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """获取角色列表"""
    query = select(Role)

    if is_active is not None:
        query = query.where(Role.is_active == is_active)

    query = query.offset(skip).limit(limit).order_by(Role.name)

    result = await db.execute(query)
    return result.scalars().all()

@router.get("/roles/{role_id}", response_model=RoleSchema)
async def get_role(
    role_id: uuid.UUID,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """获取角色详情"""
    result = await db.execute(select(Role).where(Role.id == role_id))
    role = result.scalar_one_or_none()

    if not role:
        raise HTTPException(status_code=404, detail="角色不存在")

    return role

@router.post("/roles", response_model=RoleSchema)
async def create_role(
    role_data: RoleCreate,
    request: Request,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """创建新角色"""
    # 检查角色名是否已存在
    result = await db.execute(select(Role).where(Role.name == role_data.name))
    existing_role = result.scalar_one_or_none()

    if existing_role:
        raise HTTPException(status_code=400, detail="角色名已存在")

    # 创建新角色
    new_role = Role(
        name=role_data.name,
        display_name=role_data.display_name,
        description=role_data.description,
        permissions=role_data.permissions,
        is_active=role_data.is_active,
        created_by_user_id=current_user.id
    )

    db.add(new_role)
    await db.commit()
    await db.refresh(new_role)

    # 记录活动日志
    await log_user_activity(
        db, current_user.id, "role.create", "role", str(new_role.id),
        {"name": role_data.name, "display_name": role_data.display_name},
        request
    )

    return new_role

@router.put("/roles/{role_id}", response_model=RoleSchema)
async def update_role(
    role_id: uuid.UUID,
    role_data: RoleUpdate,
    request: Request,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """更新角色信息"""
    # 获取角色
    result = await db.execute(select(Role).where(Role.id == role_id))
    role = result.scalar_one_or_none()

    if not role:
        raise HTTPException(status_code=404, detail="角色不存在")

    if role.is_system:
        raise HTTPException(status_code=400, detail="系统角色不能修改")

    # 更新角色信息
    update_data = role_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(role, field, value)

    role.updated_at = datetime.utcnow()

    await db.commit()
    await db.refresh(role)

    # 记录活动日志
    await log_user_activity(
        db, current_user.id, "role.update", "role", str(role_id),
        {"updated_fields": list(update_data.keys())},
        request
    )

    return role

@router.delete("/roles/{role_id}")
async def delete_role(
    role_id: uuid.UUID,
    request: Request,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """删除角色"""
    # 获取角色
    result = await db.execute(select(Role).where(Role.id == role_id))
    role = result.scalar_one_or_none()

    if not role:
        raise HTTPException(status_code=404, detail="角色不存在")

    if role.is_system:
        raise HTTPException(status_code=400, detail="系统角色不能删除")

    # 检查是否有用户使用此角色
    result = await db.execute(select(UserRole).where(UserRole.role_id == role_id))
    user_roles = result.scalars().all()

    if user_roles:
        raise HTTPException(status_code=400, detail="角色正在被使用，不能删除")

    # 删除角色
    await db.delete(role)
    await db.commit()

    # 记录活动日志
    await log_user_activity(
        db, current_user.id, "role.delete", "role", str(role_id),
        {"name": role.name},
        request
    )

    return {"message": "角色已删除"}

# 用户角色分配API
@router.post("/users/{user_id}/roles")
async def assign_user_roles(
    user_id: uuid.UUID,
    role_assignment: UserRoleAssign,
    request: Request,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """分配用户角色"""
    # 检查用户是否存在
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()

    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")

    # 删除现有角色分配
    await db.execute(
        select(UserRole).where(UserRole.user_id == user_id)
    )
    existing_roles = await db.execute(
        select(UserRole).where(UserRole.user_id == user_id)
    )
    for user_role in existing_roles.scalars().all():
        await db.delete(user_role)

    # 分配新角色
    for role_id in role_assignment.role_ids:
        # 检查角色是否存在
        result = await db.execute(select(Role).where(Role.id == role_id))
        role = result.scalar_one_or_none()

        if not role:
            raise HTTPException(status_code=400, detail=f"角色 {role_id} 不存在")

        if not role.is_active:
            raise HTTPException(status_code=400, detail=f"角色 {role.display_name} 已禁用")

        # 创建用户角色关联
        user_role = UserRole(
            user_id=user_id,
            role_id=role_id,
            assigned_by_user_id=current_user.id
        )
        db.add(user_role)

    await db.commit()

    # 记录活动日志
    await log_user_activity(
        db, current_user.id, "user.role_assign", "user", str(user_id),
        {"role_ids": [str(rid) for rid in role_assignment.role_ids]},
        request
    )

    return {"message": "角色分配成功"}

@router.get("/users/{user_id}/roles", response_model=List[UserRoleResponse])
async def get_user_roles(
    user_id: uuid.UUID,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """获取用户角色"""
    query = select(UserRole).options(
        selectinload(UserRole.role)
    ).where(UserRole.user_id == user_id)

    result = await db.execute(query)
    return result.scalars().all()

# 用户活动日志API
@router.get("/users/{user_id}/activity-logs", response_model=List[UserActivityLogSchema])
async def get_user_activity_logs(
    user_id: uuid.UUID,
    skip: int = 0,
    limit: int = 100,
    action: Optional[str] = None,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """获取用户活动日志"""
    query = select(UserActivityLog).where(UserActivityLog.user_id == user_id)

    if action:
        query = query.where(UserActivityLog.action == action)

    query = query.offset(skip).limit(limit).order_by(desc(UserActivityLog.created_at))

    result = await db.execute(query)
    return result.scalars().all()

@router.get("/activity-logs", response_model=List[UserActivityLogSchema])
async def get_all_activity_logs(
    skip: int = 0,
    limit: int = 100,
    action: Optional[str] = None,
    user_id: Optional[uuid.UUID] = None,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """获取所有用户活动日志"""
    query = select(UserActivityLog)

    if action:
        query = query.where(UserActivityLog.action == action)

    if user_id:
        query = query.where(UserActivityLog.user_id == user_id)

    query = query.offset(skip).limit(limit).order_by(desc(UserActivityLog.created_at))

    result = await db.execute(query)
    return result.scalars().all()
