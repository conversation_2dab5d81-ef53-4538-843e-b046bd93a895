from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, or_, func
from sqlalchemy.orm import selectinload
from typing import List, Optional
from uuid import UUID

from database import get_db
from models_dynamic import User, VulnerabilityKB
from schemas_dynamic import VulnerabilityKBCreate, VulnerabilityKBUpdate, VulnerabilityKB as VulnerabilityKBSchema
from auth import get_current_user, get_current_admin_user

router = APIRouter(prefix="/vulnerabilities", tags=["vulnerabilities"])


@router.get("/", response_model=List[VulnerabilityKBSchema])
async def get_vulnerabilities(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = Query(None),
    severity: Optional[str] = Query(None),
    vuln_id: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get vulnerabilities with optional filtering"""
    query = select(VulnerabilityKB)
    
    # Apply filters
    if search:
        query = query.where(
            or_(
                VulnerabilityKB.name.ilike(f"%{search}%"),
                VulnerabilityKB.description.ilike(f"%{search}%"),
                VulnerabilityKB.vuln_id.ilike(f"%{search}%")
            )
        )
    
    if severity:
        query = query.where(VulnerabilityKB.severity == severity)
    
    if vuln_id:
        query = query.where(VulnerabilityKB.vuln_id.ilike(f"%{vuln_id}%"))
    
    # Order by created_at desc
    query = query.order_by(VulnerabilityKB.created_at.desc())
    query = query.offset(skip).limit(limit)
    
    result = await db.execute(query)
    vulnerabilities = result.scalars().all()
    
    return vulnerabilities


@router.get("/{vulnerability_id}", response_model=VulnerabilityKBSchema)
async def get_vulnerability(
    vulnerability_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific vulnerability by ID"""
    query = select(VulnerabilityKB).where(VulnerabilityKB.id == vulnerability_id)
    result = await db.execute(query)
    vulnerability = result.scalar_one_or_none()
    
    if not vulnerability:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Vulnerability not found"
        )
    
    return vulnerability


@router.post("/", response_model=VulnerabilityKBSchema, status_code=status.HTTP_201_CREATED)
async def create_vulnerability(
    vulnerability_data: VulnerabilityKBCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """Create a new vulnerability"""
    # Check if vuln_id already exists (if provided)
    if vulnerability_data.vuln_id:
        existing_query = select(VulnerabilityKB).where(VulnerabilityKB.vuln_id == vulnerability_data.vuln_id)
        existing_result = await db.execute(existing_query)
        if existing_result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Vulnerability ID already exists"
            )
    
    # Create vulnerability
    vulnerability = VulnerabilityKB(
        name=vulnerability_data.name,
        vuln_id=vulnerability_data.vuln_id,
        description=vulnerability_data.description,
        severity=vulnerability_data.severity,
        affected_products=vulnerability_data.affected_products,
        poc_identifier=vulnerability_data.poc_identifier,
        created_by_user_id=current_user.id
    )
    
    db.add(vulnerability)
    await db.commit()
    await db.refresh(vulnerability)
    
    return vulnerability


@router.put("/{vulnerability_id}", response_model=VulnerabilityKBSchema)
async def update_vulnerability(
    vulnerability_id: UUID,
    vulnerability_data: VulnerabilityKBUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """Update a vulnerability"""
    # Get existing vulnerability
    query = select(VulnerabilityKB).where(VulnerabilityKB.id == vulnerability_id)
    result = await db.execute(query)
    vulnerability = result.scalar_one_or_none()
    
    if not vulnerability:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Vulnerability not found"
        )
    
    # Check if vuln_id already exists (if being updated)
    if vulnerability_data.vuln_id and vulnerability_data.vuln_id != vulnerability.vuln_id:
        existing_query = select(VulnerabilityKB).where(VulnerabilityKB.vuln_id == vulnerability_data.vuln_id)
        existing_result = await db.execute(existing_query)
        if existing_result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Vulnerability ID already exists"
            )
    
    # Update fields
    update_data = vulnerability_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(vulnerability, field, value)
    
    await db.commit()
    await db.refresh(vulnerability)
    
    return vulnerability


@router.delete("/{vulnerability_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_vulnerability(
    vulnerability_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """Delete a vulnerability"""
    # Get existing vulnerability
    query = select(VulnerabilityKB).where(VulnerabilityKB.id == vulnerability_id)
    result = await db.execute(query)
    vulnerability = result.scalar_one_or_none()
    
    if not vulnerability:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Vulnerability not found"
        )
    
    # 检查漏洞是否被任务引用
    from models_dynamic import Task
    task_query = select(Task).where(
        Task.parameters.op('->>')('vulnerability_id') == str(vulnerability_id)
    )
    task_result = await db.execute(task_query)
    referenced_tasks = task_result.scalars().all()
    
    if referenced_tasks:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot delete vulnerability as it is referenced by {len(referenced_tasks)} task(s)"
        )
    
    # 删除漏洞
    await db.delete(vulnerability)
    await db.commit()


@router.get("/stats/severity", response_model=List[dict])
async def get_severity_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get vulnerability count by severity"""
    query = select(
        VulnerabilityKB.severity,
        func.count(VulnerabilityKB.id).label("count")
    ).group_by(VulnerabilityKB.severity)
    
    result = await db.execute(query)
    stats = result.all()
    
    return [
        {
            "severity": stat.severity or "Unknown",
            "count": stat.count
        }
        for stat in stats
    ]


@router.get("/severities/", response_model=List[str])
async def get_available_severities(
    current_user: User = Depends(get_current_user)
):
    """Get available severity levels"""
    return ["Critical", "High", "Medium", "Low", "Info"]