#!/usr/bin/env python3
"""
统一资产服务层 - 基于动态模型的资产管理
替代原有的unified_assets API，全面使用动态模型
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, or_, and_
from typing import List, Optional, Dict, Any
from uuid import UUID
import json
from datetime import datetime

from database import get_db
from models_dynamic import ModelType, DynamicEntity, User
from auth import get_current_user
from routes.dynamic_models import sync_entity_to_elasticsearch

router = APIRouter(prefix="/assets", tags=["assets"])

async def get_asset_model_type(db: AsyncSession) -> ModelType:
    """获取资产模型类型"""
    query = select(ModelType).where(ModelType.name == 'asset')
    result = await db.execute(query)
    asset_model = result.scalar_one_or_none()
    
    if not asset_model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset model type not found. Please create asset dynamic model first."
        )
    
    return asset_model

@router.get("/search")
async def search_assets(
    q: Optional[str] = Query(None, description="搜索查询"),
    asset_type: Optional[str] = Query(None, description="资产类型过滤"),
    confidence: Optional[str] = Query(None, description="置信度过滤"),
    status: Optional[str] = Query(None, description="状态过滤"),
    platform_id: Optional[str] = Query(None, description="平台ID过滤"),
    project_id: Optional[str] = Query(None, description="项目ID过滤"),
    country_cn: Optional[str] = Query(None, description="国家过滤"),
    province_cn: Optional[str] = Query(None, description="省份过滤"),
    city_cn: Optional[str] = Query(None, description="城市过滤"),
    isp: Optional[str] = Query(None, description="运营商过滤"),
    application_name: Optional[str] = Query(None, description="应用名称过滤"),
    service_name: Optional[str] = Query(None, description="服务名称过滤"),
    date_from: Optional[str] = Query(None, description="起始日期 (YYYY-MM-DD)"),
    date_to: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    skip: int = Query(0, description="偏移量"),
    limit: int = Query(20, description="返回结果数量"),
    size: int = Query(20, description="返回结果数量（兼容性）"),
    include_aggregations: bool = Query(False, description="包含聚合统计"),
    filters: Optional[str] = Query(None, description="JSON格式的过滤条件"),
    sort: Optional[str] = Query(None, description="排序字段"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """搜索资产 - 基于动态模型的统一接口"""
    
    # 获取资产模型类型
    asset_model = await get_asset_model_type(db)
    
    # 构建查询
    query = select(DynamicEntity).where(DynamicEntity.model_type_id == asset_model.id)
    
    # 解析JSON过滤条件
    json_filters = {}
    if filters:
        try:
            json_filters = json.loads(filters)
        except json.JSONDecodeError:
            pass
    
    # 构建搜索条件
    search_conditions = []
    
    # 文本搜索
    search_text = q or json_filters.get('q')
    if search_text:
        text_conditions = [
            DynamicEntity.entity_data.op('->>')('asset_value').ilike(f"%{search_text}%"),
            DynamicEntity.entity_data.op('->>')('asset_host').ilike(f"%{search_text}%"),
            DynamicEntity.entity_data.op('->>')('ip_address').ilike(f"%{search_text}%"),
            DynamicEntity.entity_data.op('->>')('url').ilike(f"%{search_text}%"),
            DynamicEntity.entity_data.op('->>')('domain').ilike(f"%{search_text}%"),
            DynamicEntity.entity_data.op('->>')('website_host').ilike(f"%{search_text}%"),
            DynamicEntity.entity_data.op('->>')('application_name').ilike(f"%{search_text}%"),
            DynamicEntity.entity_data.op('->>')('service_name').ilike(f"%{search_text}%"),
            DynamicEntity.entity_data.op('->>')('webpage_title').ilike(f"%{search_text}%"),
        ]
        search_conditions.append(or_(*text_conditions))
    
    # 字段过滤
    filter_mappings = {
        'asset_type': asset_type or json_filters.get('asset_type'),
        'confidence': confidence or json_filters.get('confidence'),
        'status': status or json_filters.get('status'),
        'platform_id': platform_id or json_filters.get('platform_id'),
        'project_id': project_id or json_filters.get('project_id'),
        'country_cn': country_cn or json_filters.get('country_cn'),
        'province_cn': province_cn or json_filters.get('province_cn'),
        'city_cn': city_cn or json_filters.get('city_cn'),
        'isp': isp or json_filters.get('isp'),
        'application_name': application_name or json_filters.get('application_name'),
        'service_name': service_name or json_filters.get('service_name'),
    }
    
    for field, value in filter_mappings.items():
        if value:
            search_conditions.append(
                DynamicEntity.entity_data.op('->>')((field)) == value
            )
    
    # 日期过滤
    date_from_val = date_from or json_filters.get('date_from')
    date_to_val = date_to or json_filters.get('date_to')
    
    if date_from_val:
        search_conditions.append(
            DynamicEntity.entity_data.op('->>')('discovered_at') >= date_from_val
        )
    
    if date_to_val:
        search_conditions.append(
            DynamicEntity.entity_data.op('->>')('discovered_at') <= date_to_val
        )
    
    # 应用搜索条件
    if search_conditions:
        query = query.where(and_(*search_conditions))
    
    # 排序
    if sort:
        if sort.startswith('-'):
            field = sort[1:]
            query = query.order_by(DynamicEntity.entity_data.op('->>')((field)).desc())
        else:
            query = query.order_by(DynamicEntity.entity_data.op('->>')((sort)).asc())
    else:
        query = query.order_by(DynamicEntity.created_at.desc())
    
    # 获取总数
    count_query = select(func.count(DynamicEntity.id)).where(DynamicEntity.model_type_id == asset_model.id)
    if search_conditions:
        count_query = count_query.where(and_(*search_conditions))
    
    count_result = await db.execute(count_query)
    total = count_result.scalar()
    
    # 分页
    result_limit = limit if limit > 0 else size
    query = query.offset(skip).limit(result_limit)
    
    # 执行查询
    result = await db.execute(query)
    assets = result.scalars().all()
    
    # 格式化响应
    response_data = {
        "assets": [
            {
                "id": str(asset.id),
                "asset_id": asset.entity_data.get('asset_id'),
                "asset_type": asset.entity_data.get('asset_type'),
                "asset_value": asset.entity_data.get('asset_value'),
                "asset_host": asset.entity_data.get('asset_host'),
                "ip_address": asset.entity_data.get('ip_address'),
                "confidence": asset.entity_data.get('confidence'),
                "status": asset.entity_data.get('status'),
                "platform_id": asset.entity_data.get('platform_id'),
                "project_id": asset.entity_data.get('project_id'),
                "discovered_at": asset.entity_data.get('discovered_at'),
                "created_at": asset.created_at.isoformat() if asset.created_at else None,
                "updated_at": asset.updated_at.isoformat() if asset.updated_at else None,
                **asset.entity_data  # 包含所有45+字段
            }
            for asset in assets
        ],
        "total": total,
        "skip": skip,
        "limit": result_limit
    }
    
    # 添加聚合统计
    if include_aggregations:
        response_data["aggregations"] = await get_asset_aggregations(db, asset_model, search_conditions)
    
    return response_data

@router.get("/statistics")
async def get_asset_statistics(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取资产统计信息"""
    
    asset_model = await get_asset_model_type(db)
    
    # 基础统计
    total_query = select(func.count(DynamicEntity.id)).where(DynamicEntity.model_type_id == asset_model.id)
    total_result = await db.execute(total_query)
    total_assets = total_result.scalar()
    
    # 按类型统计
    type_stats = {}
    asset_types = ['domain', 'ip', 'port', 'url', 'service', 'application', 'certificate', 'subdomain', 'website']
    
    for asset_type in asset_types:
        type_query = select(func.count(DynamicEntity.id)).where(
            DynamicEntity.model_type_id == asset_model.id,
            DynamicEntity.entity_data.op('->>')('asset_type') == asset_type
        )
        type_result = await db.execute(type_query)
        type_stats[asset_type] = type_result.scalar()
    
    # 按状态统计
    status_stats = {}
    statuses = ['discovered', 'verified', 'confirmed', 'archived', 'ignored']
    
    for status_val in statuses:
        status_query = select(func.count(DynamicEntity.id)).where(
            DynamicEntity.model_type_id == asset_model.id,
            DynamicEntity.entity_data.op('->>')('status') == status_val
        )
        status_result = await db.execute(status_query)
        status_stats[status_val] = status_result.scalar()
    
    return {
        "total_assets": total_assets,
        "by_type": type_stats,
        "by_status": status_stats,
        "model_type_id": str(asset_model.id)
    }

@router.post("/ingest")
async def ingest_assets(
    assets_data: List[Dict[str, Any]],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """批量导入资产"""
    
    asset_model = await get_asset_model_type(db)
    
    created_assets = []
    errors = []
    
    for asset_data in assets_data:
        try:
            # 验证必填字段
            if not asset_data.get('asset_value'):
                errors.append(f"Missing required field: asset_value")
                continue
            
            # 生成asset_id（如果没有）
            if not asset_data.get('asset_id'):
                import uuid
                asset_data['asset_id'] = str(uuid.uuid4())
            
            # 生成指纹哈希
            if not asset_data.get('fingerprint_hash'):
                import hashlib
                fingerprint_data = f"{asset_data.get('asset_type', '')}{asset_data.get('asset_value', '')}{asset_data.get('asset_host', '')}{asset_data.get('asset_port', '')}"
                asset_data['fingerprint_hash'] = hashlib.md5(fingerprint_data.encode()).hexdigest()
            
            # 设置默认值
            asset_data.setdefault('asset_type', 'unknown')
            asset_data.setdefault('confidence', 0.5)
            asset_data.setdefault('status', 'discovered')
            asset_data.setdefault('discovered_at', datetime.utcnow().isoformat())
            
            # 检查是否已存在
            existing_query = select(DynamicEntity).where(
                DynamicEntity.model_type_id == asset_model.id,
                DynamicEntity.entity_data.op('->>')('fingerprint_hash') == asset_data['fingerprint_hash']
            )
            existing_result = await db.execute(existing_query)
            existing_entity = existing_result.scalar_one_or_none()
            
            if existing_entity:
                # 更新现有资产
                existing_entity.entity_data = asset_data
                existing_entity.updated_at = datetime.utcnow()
                await sync_entity_to_elasticsearch(existing_entity, asset_model)
                created_assets.append(str(existing_entity.id))
            else:
                # 创建新资产
                new_entity = DynamicEntity(
                    model_type_id=asset_model.id,
                    entity_data=asset_data,
                    created_by_user_id=current_user.id
                )
                db.add(new_entity)
                await db.flush()
                await sync_entity_to_elasticsearch(new_entity, asset_model)
                created_assets.append(str(new_entity.id))
            
        except Exception as e:
            errors.append(f"Error processing asset {asset_data.get('asset_id', 'unknown')}: {str(e)}")
    
    await db.commit()
    
    return {
        "created_assets": created_assets,
        "created_count": len(created_assets),
        "errors": errors,
        "error_count": len(errors)
    }

@router.get("/{asset_id}")
async def get_asset(
    asset_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取单个资产详情"""
    
    asset_model = await get_asset_model_type(db)
    
    # 尝试按UUID查找
    try:
        uuid_id = UUID(asset_id)
        query = select(DynamicEntity).where(
            DynamicEntity.model_type_id == asset_model.id,
            DynamicEntity.id == uuid_id
        )
    except ValueError:
        # 按asset_id查找
        query = select(DynamicEntity).where(
            DynamicEntity.model_type_id == asset_model.id,
            DynamicEntity.entity_data.op('->>')('asset_id') == asset_id
        )
    
    result = await db.execute(query)
    asset = result.scalar_one_or_none()
    
    if not asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )
    
    return {
        "id": str(asset.id),
        "asset_id": asset.entity_data.get('asset_id'),
        "model_type_id": str(asset.model_type_id),
        "created_at": asset.created_at.isoformat() if asset.created_at else None,
        "updated_at": asset.updated_at.isoformat() if asset.updated_at else None,
        "created_by": str(asset.created_by_user_id),
        **asset.entity_data  # 包含所有45+字段
    }

@router.put("/{asset_id}")
async def update_asset(
    asset_id: str,
    asset_data: Dict[str, Any],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新资产"""
    
    asset_model = await get_asset_model_type(db)
    
    # 查找资产
    try:
        uuid_id = UUID(asset_id)
        query = select(DynamicEntity).where(
            DynamicEntity.model_type_id == asset_model.id,
            DynamicEntity.id == uuid_id
        )
    except ValueError:
        query = select(DynamicEntity).where(
            DynamicEntity.model_type_id == asset_model.id,
            DynamicEntity.entity_data.op('->>')('asset_id') == asset_id
        )
    
    result = await db.execute(query)
    asset = result.scalar_one_or_none()
    
    if not asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )
    
    # 更新数据
    asset.entity_data = {**asset.entity_data, **asset_data}
    asset.updated_at = datetime.utcnow()
    
    await db.commit()
    await sync_entity_to_elasticsearch(asset, asset_model)
    
    return {
        "id": str(asset.id),
        "asset_id": asset.entity_data.get('asset_id'),
        "updated_at": asset.updated_at.isoformat(),
        **asset.entity_data
    }

@router.delete("/{asset_id}")
async def delete_asset(
    asset_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除资产"""
    
    asset_model = await get_asset_model_type(db)
    
    # 查找资产
    try:
        uuid_id = UUID(asset_id)
        query = select(DynamicEntity).where(
            DynamicEntity.model_type_id == asset_model.id,
            DynamicEntity.id == uuid_id
        )
    except ValueError:
        query = select(DynamicEntity).where(
            DynamicEntity.model_type_id == asset_model.id,
            DynamicEntity.entity_data.op('->>')('asset_id') == asset_id
        )
    
    result = await db.execute(query)
    asset = result.scalar_one_or_none()
    
    if not asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )
    
    # 从ES删除
    if asset.es_doc_id:
        try:
            from elasticsearch_client import get_es_client
            es = await get_es_client()
            await es.delete(index=asset.es_index, id=asset.es_doc_id)
        except Exception:
            pass
    
    # 从数据库删除
    await db.delete(asset)
    await db.commit()
    
    return {"message": "Asset deleted successfully"}

async def get_asset_aggregations(db: AsyncSession, asset_model: ModelType, search_conditions: List = None):
    """获取资产聚合统计"""
    
    base_query = select(DynamicEntity).where(DynamicEntity.model_type_id == asset_model.id)
    if search_conditions:
        base_query = base_query.where(and_(*search_conditions))
    
    # 按类型聚合
    type_agg = {}
    for asset_type in ['domain', 'ip', 'port', 'url', 'service', 'application']:
        type_query = select(func.count(DynamicEntity.id)).where(
            DynamicEntity.model_type_id == asset_model.id,
            DynamicEntity.entity_data.op('->>')('asset_type') == asset_type
        )
        if search_conditions:
            type_query = type_query.where(and_(*search_conditions))
        
        type_result = await db.execute(type_query)
        type_agg[asset_type] = type_result.scalar()
    
    # 按状态聚合
    status_agg = {}
    for status_val in ['discovered', 'verified', 'confirmed']:
        status_query = select(func.count(DynamicEntity.id)).where(
            DynamicEntity.model_type_id == asset_model.id,
            DynamicEntity.entity_data.op('->>')('status') == status_val
        )
        if search_conditions:
            status_query = status_query.where(and_(*search_conditions))
        
        status_result = await db.execute(status_query)
        status_agg[status_val] = status_result.scalar()
    
    return {
        "by_type": type_agg,
        "by_status": status_agg
    }