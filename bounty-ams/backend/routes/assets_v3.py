"""
资产管理 V3.0 API路由
基于Elasticsearch的统一资产管理接口
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Form, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any, Optional
import json
import csv
import io
from datetime import datetime

from database import get_db
from auth import get_current_user, get_current_admin_user
from models_dynamic import User
from asset_management_v3 import (
    AssetManagerV3, create_asset_manager_v3, DataSource, AssetType,
    Asset, AssetMetadata, AssetData, AssetRelation, ProcessingInfo
)
from elasticsearch_client import get_es_client
from pydantic import BaseModel

router = APIRouter(prefix="/assets-v3", tags=["assets-v3"])


# Pydantic模型
class AssetSearchRequest(BaseModel):
    query: Optional[str] = None
    filters: Optional[Dict[str, Any]] = None
    sort: Optional[List[Dict[str, str]]] = None
    page: int = 1
    size: int = 20


class AssetIngestRequest(BaseModel):
    data: List[Dict[str, Any]]
    data_source: DataSource
    asset_type: Optional[AssetType] = None
    source_id: Optional[str] = None
    platform_id: Optional[str] = None
    project_id: Optional[str] = None


class AssetUpdateRequest(BaseModel):
    updates: Dict[str, Any]


# 依赖注入
async def get_asset_manager(
    db: AsyncSession = Depends(get_db),
    es_client = Depends(get_es_client)
) -> AssetManagerV3:
    """获取资产管理器实例"""
    return await create_asset_manager_v3(es_client, db)


@router.post("/ingest", response_model=Dict[str, Any])
async def ingest_assets(
    request: AssetIngestRequest,
    asset_manager: AssetManagerV3 = Depends(get_asset_manager),
    current_user: User = Depends(get_current_user)
):
    """摄取资产数据"""
    try:
        result = await asset_manager.ingest_assets(
            raw_data_list=request.data,
            data_source=request.data_source,
            asset_type=request.asset_type,
            source_id=request.source_id,
            platform_id=request.platform_id,
            project_id=request.project_id
        )
        
        return {
            "success": True,
            "message": "资产摄取完成",
            "data": result
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"资产摄取失败: {str(e)}"
        )


@router.post("/ingest/csv")
async def ingest_csv_file(
    file: UploadFile = File(...),
    data_source: DataSource = Form(DataSource.MANUAL_IMPORT),
    asset_type: Optional[AssetType] = Form(None),
    platform_id: Optional[str] = Form(None),
    project_id: Optional[str] = Form(None),
    asset_manager: AssetManagerV3 = Depends(get_asset_manager),
    current_user: User = Depends(get_current_user)
):
    """上传CSV文件摄取资产"""
    try:
        # 验证文件类型
        if not file.filename.endswith('.csv'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只支持CSV文件格式"
            )
        
        # 读取CSV内容
        content = await file.read()
        csv_content = content.decode('utf-8')
        
        # 解析CSV
        csv_reader = csv.DictReader(io.StringIO(csv_content))
        raw_data_list = list(csv_reader)
        
        if not raw_data_list:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="CSV文件为空或格式不正确"
            )
        
        # 适配CSV数据
        from asset_ingestion_pipeline import ManualImportAdapter
        adapted_data = ManualImportAdapter.adapt_csv_data(raw_data_list)
        
        # 摄取数据
        result = await asset_manager.ingest_assets(
            raw_data_list=adapted_data,
            data_source=data_source,
            asset_type=asset_type,
            source_id=f"csv_upload_{datetime.utcnow().isoformat()}",
            platform_id=platform_id,
            project_id=project_id
        )
        
        return {
            "success": True,
            "message": f"CSV文件摄取完成，处理了 {len(raw_data_list)} 条记录",
            "filename": file.filename,
            "data": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"CSV文件处理失败: {str(e)}"
        )


@router.post("/search", response_model=Dict[str, Any])
async def search_assets(
    request: AssetSearchRequest,
    asset_manager: AssetManagerV3 = Depends(get_asset_manager),
    current_user: User = Depends(get_current_user)
):
    """搜索资产"""
    try:
        result = await asset_manager.search_assets(
            query=request.query,
            filters=request.filters,
            sort=request.sort,
            page=request.page,
            size=request.size
        )
        
        return {
            "success": True,
            "data": result
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索失败: {str(e)}"
        )


@router.get("/search", response_model=Dict[str, Any])
async def search_assets_get(
    q: Optional[str] = Query(None, description="搜索关键词"),
    asset_type: Optional[str] = Query(None, description="资产类型"),
    data_source: Optional[str] = Query(None, description="数据源"),
    platform_id: Optional[str] = Query(None, description="平台ID"),
    project_id: Optional[str] = Query(None, description="项目ID"),
    quality_level: Optional[str] = Query(None, description="质量等级"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    asset_manager: AssetManagerV3 = Depends(get_asset_manager),
    current_user: User = Depends(get_current_user)
):
    """GET方式搜索资产"""
    try:
        # 构建过滤条件
        filters = {}
        if asset_type:
            filters["metadata.asset_type"] = asset_type
        if data_source:
            filters["metadata.data_source"] = data_source
        if platform_id:
            filters["relation.platform_id"] = platform_id
        if project_id:
            filters["relation.project_id"] = project_id
        if quality_level:
            filters["metadata.quality_level"] = quality_level
        
        result = await asset_manager.search_assets(
            query=q,
            filters=filters if filters else None,
            page=page,
            size=size
        )
        
        return {
            "success": True,
            "data": result
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索失败: {str(e)}"
        )


@router.get("/statistics", response_model=Dict[str, Any])
async def get_asset_statistics(
    asset_manager: AssetManagerV3 = Depends(get_asset_manager),
    current_user: User = Depends(get_current_user)
):
    """获取资产统计信息"""
    try:
        stats = await asset_manager.get_statistics()
        
        return {
            "success": True,
            "data": stats
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计信息失败: {str(e)}"
        )


@router.get("/{asset_id}", response_model=Dict[str, Any])
async def get_asset(
    asset_id: str,
    asset_manager: AssetManagerV3 = Depends(get_asset_manager),
    current_user: User = Depends(get_current_user)
):
    """获取单个资产详情"""
    try:
        asset = await asset_manager.get_asset(asset_id)
        
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="资产不存在"
            )
        
        return {
            "success": True,
            "data": asset.to_dict()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取资产失败: {str(e)}"
        )


@router.put("/{asset_id}", response_model=Dict[str, Any])
async def update_asset(
    asset_id: str,
    request: AssetUpdateRequest,
    asset_manager: AssetManagerV3 = Depends(get_asset_manager),
    current_user: User = Depends(get_current_user)
):
    """更新资产"""
    try:
        success = await asset_manager.update_asset(asset_id, request.updates)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="资产不存在或更新失败"
            )
        
        return {
            "success": True,
            "message": "资产更新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新资产失败: {str(e)}"
        )


@router.delete("/{asset_id}", response_model=Dict[str, Any])
async def delete_asset(
    asset_id: str,
    asset_manager: AssetManagerV3 = Depends(get_asset_manager),
    current_user: User = Depends(get_current_admin_user)  # 需要管理员权限
):
    """删除资产"""
    try:
        success = await asset_manager.delete_asset(asset_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="资产不存在或删除失败"
            )
        
        return {
            "success": True,
            "message": "资产删除成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除资产失败: {str(e)}"
        )
