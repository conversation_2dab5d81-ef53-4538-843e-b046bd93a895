<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资产管理API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .api-endpoint { background: #e7f3ff; padding: 5px; margin: 5px 0; }
    </style>
</head>
<body>
    <h1>🔍 资产管理页面API使用验证</h1>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <p>此页面用于验证资产管理功能是否真正使用了新的PostgreSQL统一资产API。</p>
        <p><strong>期望结果：</strong> 所有API调用都指向 <code>/api/unified-assets/*</code> 端点</p>
    </div>

    <div class="test-section">
        <h2>API端点测试</h2>
        <button onclick="testLogin()">1. 登录测试</button>
        <button onclick="testAssetSearch()">2. 资产搜索</button>
        <button onclick="testAssetTypes()">3. 资产类型</button>
        <button onclick="testAssetSources()">4. 资产来源</button>
        <button onclick="testAssetStats()">5. 统计信息</button>
        <button onclick="testAssetExport()">6. 导出功能</button>
        <button onclick="runAllTests()">🚀 运行全部测试</button>
        
        <div id="results"></div>
    </div>

    <div class="test-section">
        <h2>前端页面检查</h2>
        <p>请在浏览器开发者工具中查看网络请求，确认是否使用了正确的API端点：</p>
        <div class="api-endpoint">✅ 正确: <code>GET /api/unified-assets/search</code></div>
        <div class="api-endpoint">✅ 正确: <code>GET /api/unified-assets/types</code></div>
        <div class="api-endpoint">✅ 正确: <code>GET /api/unified-assets/sources</code></div>
        <div class="api-endpoint">✅ 正确: <code>POST /api/unified-assets/export</code></div>
        <div class="api-endpoint">❌ 错误: <code>GET /api/discovered-assets/*</code></div>
        <div class="api-endpoint">❌ 错误: <code>POST /api/enhanced-search/*</code></div>
    </div>

    <script>
        let authToken = null;

        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        async function testLogin() {
            try {
                log('🔐 测试登录API...');
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'admin', password: 'password' })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access_token;
                    log('✅ 登录成功，获取到认证令牌', 'success');
                } else {
                    log(`❌ 登录失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ 登录错误: ${error.message}`, 'error');
            }
        }

        async function makeAuthenticatedRequest(url, options = {}) {
            if (!authToken) {
                await testLogin();
            }
            
            return fetch(url, {
                ...options,
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json',
                    ...options.headers
                }
            });
        }

        async function testAssetSearch() {
            try {
                log('📋 测试资产搜索API (PostgreSQL)...');
                const response = await makeAuthenticatedRequest('/api/unified-assets/search?limit=5');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 资产搜索成功: 总数 ${data.total}, 返回 ${data.assets.length} 条`, 'success');
                    if (data.assets.length > 0) {
                        const asset = data.assets[0];
                        log(`📝 示例资产: ${asset.asset_type} - ${asset.asset_value} (ID: ${asset.asset_id})`, 'info');
                    }
                } else {
                    log(`❌ 资产搜索失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ 资产搜索错误: ${error.message}`, 'error');
            }
        }

        async function testAssetTypes() {
            try {
                log('📊 测试资产类型统计API...');
                const response = await makeAuthenticatedRequest('/api/unified-assets/types');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 资产类型统计成功: ${data.types.length} 种类型`, 'success');
                    data.types.slice(0, 3).forEach(type => {
                        log(`  - ${type.type}: ${type.count} 个`, 'info');
                    });
                } else {
                    log(`❌ 资产类型统计失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ 资产类型统计错误: ${error.message}`, 'error');
            }
        }

        async function testAssetSources() {
            try {
                log('🔍 测试资产来源统计API...');
                const response = await makeAuthenticatedRequest('/api/unified-assets/sources');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 资产来源统计成功: ${data.sources.length} 种来源`, 'success');
                    data.sources.slice(0, 3).forEach(source => {
                        log(`  - ${source.source}: ${source.count} 个`, 'info');
                    });
                } else {
                    log(`❌ 资产来源统计失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ 资产来源统计错误: ${error.message}`, 'error');
            }
        }

        async function testAssetStats() {
            try {
                log('📈 测试统计信息API...');
                const response = await makeAuthenticatedRequest('/api/unified-assets/stats');
                
                if (response.ok) {
                    const data = await response.json();
                    const stats = data.basic_stats;
                    log(`✅ 统计信息获取成功`, 'success');
                    log(`  - 总资产数: ${stats.total_assets}`, 'info');
                    log(`  - 唯一主机: ${stats.unique_hosts}`, 'info');
                    log(`  - 平均置信度: ${stats.avg_confidence.toFixed(3)}`, 'info');
                } else {
                    log(`❌ 统计信息获取失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ 统计信息错误: ${error.message}`, 'error');
            }
        }

        async function testAssetExport() {
            try {
                log('📤 测试导出功能API...');
                const exportParams = {
                    query: "",
                    platform_id: null,
                    project_id: null,
                    asset_types: [],
                    confidence: null,
                    filters: {}
                };
                
                const response = await makeAuthenticatedRequest('/api/unified-assets/export', {
                    method: 'POST',
                    body: JSON.stringify(exportParams)
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 导出功能正常: 可导出 ${data.data.total_exported} 条资产`, 'success');
                } else {
                    log(`❌ 导出功能失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ 导出功能错误: ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            log('🚀 开始运行全部测试...', 'info');
            await testLogin();
            await testAssetSearch();
            await testAssetTypes();
            await testAssetSources();
            await testAssetStats();
            await testAssetExport();
            log('🎉 所有测试完成！', 'success');
        }

        // 页面加载时运行测试
        window.onload = function() {
            log('📱 页面加载完成，可以开始测试', 'info');
        };
    </script>
</body>
</html>