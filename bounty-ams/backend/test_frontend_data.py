#!/usr/bin/env python3
"""
测试前端数据加载
验证平台、项目、资产数据是否正确返回
"""

import asyncio
import json
from database import AsyncSessionLocal
from models_dynamic import ModelType, DynamicEntity
from routes.assets_dynamic import search_assets, get_asset_model_type
from sqlalchemy import select

async def test_frontend_data():
    """测试前端需要的数据"""
    async with AsyncSessionLocal() as db:
        try:
            # 1. 获取模型类型
            print("=== 模型类型 ===")
            
            # 获取平台模型
            platform_query = select(ModelType).where(ModelType.name == 'platform')
            platform_result = await db.execute(platform_query)
            platform_type = platform_result.scalar_one_or_none()
            
            # 获取项目模型
            project_query = select(ModelType).where(ModelType.name == 'project')
            project_result = await db.execute(project_query)
            project_type = project_result.scalar_one_or_none()
            
            # 获取资产模型
            asset_type = await get_asset_model_type(db)
            
            print(f"平台模型ID: {platform_type.id if platform_type else 'None'}")
            print(f"项目模型ID: {project_type.id if project_type else 'None'}")
            print(f"资产模型ID: {asset_type.id if asset_type else 'None'}")
            
            # 2. 获取平台数据
            print("\n=== 平台数据 ===")
            if platform_type:
                platform_query = select(DynamicEntity).where(DynamicEntity.model_type_id == platform_type.id)
                platform_result = await db.execute(platform_query)
                platforms = platform_result.scalars().all()
                print(f"找到 {len(platforms)} 个平台:")
                for i, platform in enumerate(platforms[:3]):  # 只显示前3个
                    print(f"  平台 {i+1}: {platform.id}")
                    print(f"    名称: {platform.entity_data.get('name')}")
                    print(f"    显示名称: {platform.entity_data.get('display_name')}")
                    print(f"    类型: {platform.entity_data.get('platform_type')}")
                    print()
            
            # 3. 获取项目数据
            print("=== 项目数据 ===")
            if project_type:
                project_query = select(DynamicEntity).where(DynamicEntity.model_type_id == project_type.id)
                project_result = await db.execute(project_query)
                projects = project_result.scalars().all()
                print(f"找到 {len(projects)} 个项目:")
                for i, project in enumerate(projects[:3]):  # 只显示前3个
                    print(f"  项目 {i+1}: {project.id}")
                    print(f"    名称: {project.entity_data.get('name')}")
                    print(f"    平台ID: {project.entity_data.get('platform_id')}")
                    print(f"    公司: {project.entity_data.get('company_name')}")
                    print()
            
            # 4. 获取资产数据
            print("=== 资产数据 ===")
            assets_result = await search_assets(
                db=db,
                skip=0,
                limit=3,
                platform_id=None,
                project_id=None,
                q=None
            )
            
            if assets_result:
                print(f"找到 {assets_result.get('total', 0)} 个资产:")
                assets = assets_result.get('assets', [])
                for i, asset in enumerate(assets):
                    print(f"  资产 {i+1}: {asset.get('asset_id')}")
                    print(f"    值: {asset.get('asset_value')}")
                    print(f"    类型: {asset.get('asset_type')}")
                    print(f"    平台ID: {asset.get('platform_id')}")
                    print(f"    项目ID: {asset.get('project_id')}")
                    print()
            
            # 5. 验证ID匹配
            print("=== ID匹配验证 ===")
            if platforms and projects and assets_result:
                platform_ids = {str(p.id) for p in platforms}
                project_ids = {str(p.id) for p in projects}
                
                for asset in assets_result.get('assets', []):
                    asset_platform_id = asset.get('platform_id')
                    asset_project_id = asset.get('project_id')
                    
                    platform_found = asset_platform_id in platform_ids if asset_platform_id else True
                    project_found = asset_project_id in project_ids if asset_project_id else True
                    
                    print(f"资产 {asset.get('asset_value')}:")
                    print(f"  平台ID {asset_platform_id} 匹配: {'✓' if platform_found else '✗'}")
                    print(f"  项目ID {asset_project_id} 匹配: {'✓' if project_found else '✗'}")
                    
                    if not platform_found:
                        print(f"    可用平台IDs: {list(platform_ids)[:3]}...")
                    if not project_found:
                        print(f"    可用项目IDs: {list(project_ids)[:3]}...")
                    print()
            
        except Exception as e:
            print(f"错误: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_frontend_data())