#!/usr/bin/env python3
"""
创建资产类型的动态模型定义
将unified_assets的45+字段定义为动态模型
"""

import asyncio
import logging
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from database import AsyncSessionLocal
from models_dynamic import ModelType, ModelField, User

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def create_asset_dynamic_model():
    """创建资产类型的动态模型"""
    async with AsyncSessionLocal() as db:
        # 获取admin用户
        admin_query = select(User).where(User.username == 'admin')
        admin_result = await db.execute(admin_query)
        admin_user = admin_result.scalar_one_or_none()
        
        if not admin_user:
            logger.error("Admin user not found")
            return
        
        # 检查是否已存在asset模型类型
        existing_query = select(ModelType).where(ModelType.name == 'asset')
        existing_result = await db.execute(existing_query)
        existing_model = existing_result.scalar_one_or_none()
        
        if existing_model:
            logger.info("Asset model type already exists, updating...")
            # 删除现有字段
            from sqlalchemy import delete
            await db.execute(delete(ModelField).where(ModelField.model_type_id == existing_model.id))
            model_type = existing_model
        else:
            logger.info("Creating new asset model type...")
            # 创建新的模型类型
            model_type = ModelType(
                name='asset',
                display_name='资产',
                description='统一资产管理，支持45+字段的扩展版本',
                icon='🎯',
                color='#1890ff',
                is_active=True,
                is_system=True,
                created_by_user_id=admin_user.id
            )
            db.add(model_type)
            await db.flush()
        
        # 定义资产字段
        asset_fields = [
            # 基础标识字段
            {
                'field_name': 'asset_id',
                'field_type': 'text',
                'display_name': '资产ID',
                'description': '资产唯一标识符',
                'is_required': True,
                'is_unique': True,
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 1
            },
            {
                'field_name': 'fingerprint_hash',
                'field_type': 'text',
                'display_name': '指纹哈希',
                'description': '资产指纹的哈希值',
                'is_required': True,
                'is_unique': True,
                'is_searchable': True,
                'sort_order': 2
            },
            {
                'field_name': 'asset_type',
                'field_type': 'select',
                'display_name': '资产类型',
                'description': '资产的类型分类',
                'is_required': True,
                'is_searchable': True,
                'is_filterable': True,
                'field_options': {
                    'options': [
                        {'label': '域名', 'value': 'domain'},
                        {'label': 'IP地址', 'value': 'ip'},
                        {'label': '端口', 'value': 'port'},
                        {'label': 'URL', 'value': 'url'},
                        {'label': '服务', 'value': 'service'},
                        {'label': '应用', 'value': 'application'},
                        {'label': '证书', 'value': 'certificate'},
                        {'label': '子域名', 'value': 'subdomain'},
                        {'label': '网站', 'value': 'website'}
                    ]
                },
                'sort_order': 3
            },
            {
                'field_name': 'asset_value',
                'field_type': 'text',
                'display_name': '资产值',
                'description': '资产的具体值',
                'is_required': True,
                'is_searchable': True,
                'sort_order': 4
            },
            
            # 基础网络信息
            {
                'field_name': 'asset_host',
                'field_type': 'text',
                'display_name': '主机',
                'description': '资产主机名',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 5
            },
            {
                'field_name': 'asset_port',
                'field_type': 'number',
                'display_name': '端口',
                'description': '资产端口号',
                'is_filterable': True,
                'validation_rules': {'min': 1, 'max': 65535},
                'sort_order': 6
            },
            {
                'field_name': 'asset_service',
                'field_type': 'text',
                'display_name': '服务',
                'description': '资产服务类型',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 7
            },
            {
                'field_name': 'ip_address',
                'field_type': 'text',
                'display_name': 'IP地址',
                'description': '资产IP地址',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 8
            },
            {
                'field_name': 'url',
                'field_type': 'text',
                'display_name': 'URL',
                'description': '资产URL地址',
                'is_searchable': True,
                'sort_order': 9
            },
            {
                'field_name': 'transport_protocol',
                'field_type': 'select',
                'display_name': '传输协议',
                'description': '传输协议类型',
                'is_filterable': True,
                'field_options': {
                    'options': [
                        {'label': 'TCP', 'value': 'tcp'},
                        {'label': 'UDP', 'value': 'udp'},
                        {'label': 'HTTP', 'value': 'http'},
                        {'label': 'HTTPS', 'value': 'https'},
                        {'label': 'FTP', 'value': 'ftp'},
                        {'label': 'SSH', 'value': 'ssh'},
                        {'label': 'SMTP', 'value': 'smtp'}
                    ]
                },
                'sort_order': 10
            },
            {
                'field_name': 'scan_time',
                'field_type': 'datetime',
                'display_name': '扫描时间',
                'description': '资产扫描时间',
                'is_filterable': True,
                'sort_order': 11
            },
            
            # 网络路径和域名信息
            {
                'field_name': 'website_path',
                'field_type': 'text',
                'display_name': '网站路径',
                'description': '网站路径信息',
                'is_searchable': True,
                'sort_order': 12
            },
            {
                'field_name': 'asn_number',
                'field_type': 'text',
                'display_name': 'ASN号码',
                'description': '自治系统号码',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 13
            },
            {
                'field_name': 'asn_organization',
                'field_type': 'text',
                'display_name': 'ASN组织',
                'description': '自治系统组织',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 14
            },
            {
                'field_name': 'international_domain',
                'field_type': 'text',
                'display_name': '国际域名',
                'description': '国际域名信息',
                'is_searchable': True,
                'sort_order': 15
            },
            {
                'field_name': 'website_host',
                'field_type': 'text',
                'display_name': '网站主机',
                'description': '网站主机信息',
                'is_searchable': True,
                'sort_order': 16
            },
            
            # ICP备案信息
            {
                'field_name': 'icp_unit',
                'field_type': 'text',
                'display_name': 'ICP备案单位',
                'description': 'ICP备案单位信息',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 17
            },
            {
                'field_name': 'icp_number',
                'field_type': 'text',
                'display_name': 'ICP备案号',
                'description': 'ICP备案号码',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 18
            },
            {
                'field_name': 'icp_update_time',
                'field_type': 'datetime',
                'display_name': 'ICP更新时间',
                'description': 'ICP备案更新时间',
                'is_filterable': True,
                'sort_order': 19
            },
            
            # 网站信息
            {
                'field_name': 'website_keywords',
                'field_type': 'text',
                'display_name': '网站关键词',
                'description': '网站关键词信息',
                'is_searchable': True,
                'sort_order': 20
            },
            {
                'field_name': 'website_status_code',
                'field_type': 'number',
                'display_name': '网站状态码',
                'description': 'HTTP状态码',
                'is_filterable': True,
                'validation_rules': {'min': 100, 'max': 599},
                'sort_order': 21
            },
            {
                'field_name': 'website_type',
                'field_type': 'text',
                'display_name': '网站类型',
                'description': '网站类型分类',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 22
            },
            {
                'field_name': 'webpage_title',
                'field_type': 'text',
                'display_name': '网页标题',
                'description': '网页标题信息',
                'is_searchable': True,
                'sort_order': 23
            },
            
            # 应用信息
            {
                'field_name': 'application_name',
                'field_type': 'text',
                'display_name': '应用名称',
                'description': '应用程序名称',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 24
            },
            {
                'field_name': 'application_level',
                'field_type': 'select',
                'display_name': '应用级别',
                'description': '应用重要级别',
                'is_filterable': True,
                'field_options': {
                    'options': [
                        {'label': '高', 'value': 'high'},
                        {'label': '中', 'value': 'medium'},
                        {'label': '低', 'value': 'low'}
                    ]
                },
                'sort_order': 25
            },
            {
                'field_name': 'application_vendor',
                'field_type': 'text',
                'display_name': '应用厂商',
                'description': '应用程序厂商',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 26
            },
            {
                'field_name': 'application_category',
                'field_type': 'text',
                'display_name': '应用分类',
                'description': '应用程序分类',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 27
            },
            {
                'field_name': 'application_type',
                'field_type': 'text',
                'display_name': '应用类型',
                'description': '应用程序类型',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 28
            },
            
            # 服务信息
            {
                'field_name': 'service_product',
                'field_type': 'text',
                'display_name': '服务产品',
                'description': '服务产品信息',
                'is_searchable': True,
                'sort_order': 29
            },
            {
                'field_name': 'service_raw_response',
                'field_type': 'textarea',
                'display_name': '服务原始响应',
                'description': '服务原始响应数据',
                'sort_order': 30
            },
            {
                'field_name': 'service_name',
                'field_type': 'text',
                'display_name': '服务名称',
                'description': '服务名称',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 31
            },
            {
                'field_name': 'service_version',
                'field_type': 'text',
                'display_name': '服务版本',
                'description': '服务版本信息',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 32
            },
            {
                'field_name': 'service_certificate',
                'field_type': 'textarea',
                'display_name': '服务证书',
                'description': '服务证书信息',
                'sort_order': 33
            },
            
            # 地理位置信息（中文）
            {
                'field_name': 'country_cn',
                'field_type': 'text',
                'display_name': '国家(中文)',
                'description': '国家名称（中文）',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 34
            },
            {
                'field_name': 'province_cn',
                'field_type': 'text',
                'display_name': '省份(中文)',
                'description': '省份名称（中文）',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 35
            },
            {
                'field_name': 'city_cn',
                'field_type': 'text',
                'display_name': '城市(中文)',
                'description': '城市名称（中文）',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 36
            },
            {
                'field_name': 'district_cn',
                'field_type': 'text',
                'display_name': '区域(中文)',
                'description': '区域名称（中文）',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 37
            },
            
            # 地理位置信息（英文）
            {
                'field_name': 'country_en',
                'field_type': 'text',
                'display_name': '国家(英文)',
                'description': '国家名称（英文）',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 38
            },
            {
                'field_name': 'province_en',
                'field_type': 'text',
                'display_name': '省份(英文)',
                'description': '省份名称（英文）',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 39
            },
            {
                'field_name': 'city_en',
                'field_type': 'text',
                'display_name': '城市(英文)',
                'description': '城市名称（英文）',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 40
            },
            {
                'field_name': 'district_en',
                'field_type': 'text',
                'display_name': '区域(英文)',
                'description': '区域名称（英文）',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 41
            },
            
            # 运营商和证书信息
            {
                'field_name': 'isp',
                'field_type': 'text',
                'display_name': '运营商',
                'description': '互联网服务提供商',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 42
            },
            {
                'field_name': 'certificate_subject',
                'field_type': 'text',
                'display_name': '证书主题',
                'description': '证书主题信息',
                'is_searchable': True,
                'sort_order': 43
            },
            {
                'field_name': 'certificate_subject_country',
                'field_type': 'text',
                'display_name': '证书主题国家',
                'description': '证书主题国家信息',
                'is_searchable': True,
                'sort_order': 44
            },
            {
                'field_name': 'certificate_subject_organization',
                'field_type': 'text',
                'display_name': '证书主题组织',
                'description': '证书主题组织信息',
                'is_searchable': True,
                'sort_order': 45
            },
            {
                'field_name': 'certificate_subject_common_name',
                'field_type': 'text',
                'display_name': '证书通用名称',
                'description': '证书通用名称',
                'is_searchable': True,
                'sort_order': 46
            },
            {
                'field_name': 'certificate_issuer',
                'field_type': 'text',
                'display_name': '证书颁发者',
                'description': '证书颁发机构',
                'is_searchable': True,
                'sort_order': 47
            },
            {
                'field_name': 'certificate_serial_number',
                'field_type': 'text',
                'display_name': '证书序列号',
                'description': '证书序列号',
                'is_searchable': True,
                'sort_order': 48
            },
            
            # 系统字段
            {
                'field_name': 'confidence',
                'field_type': 'number',
                'display_name': '置信度',
                'description': '资产置信度',
                'is_filterable': True,
                'validation_rules': {'min': 0, 'max': 1},
                'default_value': '0.5',
                'sort_order': 49
            },
            {
                'field_name': 'status',
                'field_type': 'select',
                'display_name': '状态',
                'description': '资产状态',
                'is_filterable': True,
                'field_options': {
                    'options': [
                        {'label': '已发现', 'value': 'discovered'},
                        {'label': '已验证', 'value': 'verified'},
                        {'label': '已确认', 'value': 'confirmed'},
                        {'label': '已归档', 'value': 'archived'},
                        {'label': '已忽略', 'value': 'ignored'}
                    ]
                },
                'default_value': 'discovered',
                'sort_order': 50
            },
            {
                'field_name': 'source',
                'field_type': 'text',
                'display_name': '来源',
                'description': '资产来源',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 51
            },
            {
                'field_name': 'source_task_id',
                'field_type': 'text',
                'display_name': '来源任务ID',
                'description': '来源任务标识',
                'is_searchable': True,
                'sort_order': 52
            },
            {
                'field_name': 'source_task_type',
                'field_type': 'text',
                'display_name': '来源任务类型',
                'description': '来源任务类型',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 53
            },
            {
                'field_name': 'platform_id',
                'field_type': 'text',
                'display_name': '平台ID',
                'description': '关联平台ID',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 54
            },
            {
                'field_name': 'project_id',
                'field_type': 'text',
                'display_name': '项目ID',
                'description': '关联项目ID',
                'is_searchable': True,
                'is_filterable': True,
                'sort_order': 55
            },
            {
                'field_name': 'discovered_at',
                'field_type': 'datetime',
                'display_name': '发现时间',
                'description': '资产发现时间',
                'is_filterable': True,
                'sort_order': 56
            },
            {
                'field_name': 'verified_at',
                'field_type': 'datetime',
                'display_name': '验证时间',
                'description': '资产验证时间',
                'is_filterable': True,
                'sort_order': 57
            },
            {
                'field_name': 'updated_at',
                'field_type': 'datetime',
                'display_name': '更新时间',
                'description': '资产更新时间',
                'is_filterable': True,
                'sort_order': 58
            },
            {
                'field_name': 'tags',
                'field_type': 'multi_select',
                'display_name': '标签',
                'description': '资产标签',
                'is_searchable': True,
                'is_filterable': True,
                'field_options': {
                    'options': [
                        {'label': '重要', 'value': 'important'},
                        {'label': '测试', 'value': 'test'},
                        {'label': '生产', 'value': 'production'},
                        {'label': '内网', 'value': 'internal'},
                        {'label': '外网', 'value': 'external'},
                        {'label': '高风险', 'value': 'high_risk'},
                        {'label': '低风险', 'value': 'low_risk'}
                    ]
                },
                'sort_order': 59
            },
            {
                'field_name': 'parent_asset_id',
                'field_type': 'text',
                'display_name': '父资产ID',
                'description': '父资产标识',
                'is_searchable': True,
                'sort_order': 60
            }
        ]
        
        # 创建字段
        for field_data in asset_fields:
            field = ModelField(
                model_type_id=model_type.id,
                field_name=field_data['field_name'],
                field_type=field_data['field_type'],
                display_name=field_data['display_name'],
                description=field_data.get('description', ''),
                is_required=field_data.get('is_required', False),
                is_searchable=field_data.get('is_searchable', False),
                is_filterable=field_data.get('is_filterable', False),
                is_unique=field_data.get('is_unique', False),
                default_value=field_data.get('default_value'),
                validation_rules=field_data.get('validation_rules', {}),
                field_options=field_data.get('field_options', {}),
                sort_order=field_data.get('sort_order', 0)
            )
            db.add(field)
        
        await db.commit()
        
        # 创建对应的Elasticsearch索引
        from routes.dynamic_models import create_elasticsearch_index
        await create_elasticsearch_index(model_type)
        
        logger.info(f"✅ 成功创建资产动态模型，包含{len(asset_fields)}个字段")
        logger.info("📊 已自动创建Elasticsearch索引: dynamic_asset")
        
        return model_type

async def main():
    """主函数"""
    logger.info("🚀 开始创建资产动态模型...")
    
    try:
        asset_model = await create_asset_dynamic_model()
        logger.info("🎉 资产动态模型创建完成！")
        logger.info("现在可以通过动态模型API管理所有资产数据")
        
    except Exception as e:
        logger.error(f"❌ 创建资产动态模型失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())