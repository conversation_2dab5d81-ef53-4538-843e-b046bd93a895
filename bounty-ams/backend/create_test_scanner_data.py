"""
创建标准化测试数据
使用新的扫描器模板系统生成测试数据
"""

import asyncio
import json
import random
from datetime import datetime, timedelta
from scanner_templates import scanner_template_manager, ScannerType
from elasticsearch_client import get_es_client

class TestDataGenerator:
    """测试数据生成器"""
    
    def __init__(self):
        self.domains = [
            "example.com", "test.com", "demo.org", "sample.net", 
            "vulnerable.app", "target.site", "scan.me", "testsite.co"
        ]
        self.ips = [
            "***********", "********", "**********", "*******",
            "*******", "*******", "**************", "*************"
        ]
        
    async def generate_subfinder_data(self, count: int = 20) -> list:
        """生成Subfinder扫描结果"""
        data = []
        for i in range(count):
            domain = random.choice(self.domains)
            subdomain = f"{random.choice(['www', 'api', 'admin', 'mail', 'dev', 'test'])}.{domain}"
            
            record = {
                "host": subdomain,
                "subdomain": subdomain,
                "domain": domain,
                "source": random.choice(["crtsh", "dns", "scraping"]),
                "timestamp": (datetime.now() - timedelta(hours=random.randint(1, 24))).isoformat()
            }
            data.append(record)
        return data
    
    async def generate_nmap_data(self, count: int = 15) -> list:
        """生成Nmap扫描结果"""
        data = []
        services = {
            22: "ssh", 80: "http", 443: "https", 53: "dns",
            21: "ftp", 25: "smtp", 110: "pop3", 143: "imap"
        }
        
        for i in range(count):
            ip = random.choice(self.ips)
            port = random.choice(list(services.keys()))
            
            record = {
                "ip": ip,
                "port": port,
                "protocol": "tcp",
                "state": "open",
                "service": services[port],
                "version": f"Apache httpd 2.4.{random.randint(10, 50)}",
                "banner": f"{services[port]}_banner_{random.randint(100, 999)}"
            }
            data.append(record)
        return data
    
    async def generate_httpx_data(self, count: int = 12) -> list:
        """生成Httpx扫描结果"""
        data = []
        for i in range(count):
            domain = random.choice(self.domains)
            url = f"https://{domain}"
            
            record = {
                "url": url,
                "status-code": random.choice([200, 301, 302, 403, 404, 500]),
                "title": f"Title of {domain}",
                "content-length": random.randint(500, 5000),
                "technologies": random.sample(["Apache", "Nginx", "PHP", "MySQL", "WordPress"], 2),
                "webserver": random.choice(["Apache/2.4.41", "Nginx/1.18.0"]),
                "response-time": f"{random.randint(50, 500)}ms"
            }
            data.append(record)
        return data
    
    async def generate_nuclei_data(self, count: int = 8) -> list:
        """生成Nuclei扫描结果"""
        data = []
        vulns = [
            {"id": "cve-2021-44228", "name": "Apache Log4j RCE", "severity": "critical"},
            {"id": "cve-2020-1472", "name": "Zerologon", "severity": "critical"},
            {"id": "exposed-admin-panel", "name": "Exposed Admin Panel", "severity": "medium"},
            {"id": "ssl-tls-weak-cipher", "name": "Weak SSL/TLS Cipher", "severity": "low"}
        ]
        
        for i in range(count):
            domain = random.choice(self.domains)
            vuln = random.choice(vulns)
            
            record = {
                "template-id": vuln["id"],
                "info": {
                    "name": vuln["name"],
                    "severity": vuln["severity"]
                },
                "host": f"https://{domain}",
                "matched-at": f"https://{domain}/{random.choice(['admin', 'login', 'api', 'panel'])}",
                "extracted-results": [f"vulnerable_{random.randint(100, 999)}"],
                "timestamp": datetime.now().isoformat()
            }
            data.append(record)
        return data
    
    async def generate_naabu_data(self, count: int = 10) -> list:
        """生成Naabu扫描结果"""
        data = []
        for i in range(count):
            domain = random.choice(self.domains)
            ip = random.choice(self.ips)
            port = random.choice([80, 443, 22, 53, 21, 25])
            
            record = {
                "host": domain,
                "port": port,
                "ip": ip,
                "protocol": "tcp",
                "timestamp": datetime.now().isoformat()
            }
            data.append(record)
        return data

async def create_test_scanner_data():
    """创建标准化测试数据"""
    print("🚀 开始生成标准化测试数据...")
    
    generator = TestDataGenerator()
    es_client = await get_es_client()
    
    # 生成各种扫描器的测试数据
    test_datasets = {
        ScannerType.SUBFINDER: await generator.generate_subfinder_data(20),
        ScannerType.NMAP: await generator.generate_nmap_data(15),
        ScannerType.HTTPX: await generator.generate_httpx_data(12),
        ScannerType.NUCLEI: await generator.generate_nuclei_data(8),
        ScannerType.NAABU: await generator.generate_naabu_data(10)
    }
    
    total_records = 0
    
    for scanner_type, raw_data in test_datasets.items():
        print(f"\n📊 处理 {scanner_type.value} 数据: {len(raw_data)} 条记录")
        
        # 使用模板转换数据
        transformed_data = []
        for record in raw_data:
            transformed_record = scanner_template_manager.transform_data(scanner_type, record)
            
            # 添加测试标识
            transformed_record.update({
                "platform_id": random.choice(["hackerone", "bugcrowd", "microsoft"]),
                "project_id": random.choice(["project-1", "project-2", "project-3"]),
                "import_source": "test_data_generation",
                "imported_by": "system",
                "imported_at": datetime.utcnow().isoformat()
            })
            
            transformed_data.append(transformed_record)
        
        # 生成索引名称
        index_name = scanner_template_manager.generate_index_name(scanner_type)
        print(f"  📥 存储到索引: {index_name}")
        
        # 批量插入ES
        try:
            from elasticsearch.helpers import async_bulk
            
            actions = []
            for record in transformed_data:
                action = {
                    "_index": index_name,
                    "_source": record
                }
                actions.append(action)
            
            success_count, failed_items = await async_bulk(
                es_client,
                actions,
                index=index_name,
                refresh='wait_for'
            )
            
            print(f"  ✅ 成功插入: {success_count} 条记录")
            if failed_items:
                print(f"  ❌ 失败记录: {len(failed_items)} 条")
            
            total_records += success_count
            
        except Exception as e:
            print(f"  ❌ 插入失败: {e}")
    
    print(f"\n🎉 测试数据生成完成! 总计: {total_records} 条记录")
    return total_records

if __name__ == "__main__":
    asyncio.run(create_test_scanner_data())