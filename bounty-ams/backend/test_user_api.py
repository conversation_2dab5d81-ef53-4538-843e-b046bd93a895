#!/usr/bin/env python3
"""
测试用户管理API
"""

import requests
import json

BASE_URL = "http://localhost:8003"

def test_login():
    """测试登录"""
    print("🔧 测试登录...")
    
    try:
        login_data = {
            "username": "admin",
            "password": "password"
        }

        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        
        if response.status_code == 200:
            token_data = response.json()
            print("  ✅ 登录成功")
            return token_data.get("access_token")
        else:
            print(f"  ❌ 登录失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"  ❌ 登录错误: {e}")
        return None

def test_get_users(token):
    """测试获取用户列表"""
    print("\n🔧 测试获取用户列表...")
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BASE_URL}/api/users", headers=headers)
        
        if response.status_code == 200:
            users = response.json()
            print(f"  ✅ 获取用户列表成功: {len(users)} 个用户")
            for user in users:
                print(f"    - {user['username']} ({user['email']}) - 角色: {len(user.get('roles', []))}个")
            return True
        else:
            print(f"  ❌ 获取用户列表失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"  ❌ 获取用户列表错误: {e}")
        return False

def test_get_roles(token):
    """测试获取角色列表"""
    print("\n🔧 测试获取角色列表...")
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BASE_URL}/api/roles", headers=headers)
        
        if response.status_code == 200:
            roles = response.json()
            print(f"  ✅ 获取角色列表成功: {len(roles)} 个角色")
            for role in roles:
                print(f"    - {role['display_name']} ({role['name']}) - 权限: {len(role.get('permissions', []))}个")
            return True
        else:
            print(f"  ❌ 获取角色列表失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"  ❌ 获取角色列表错误: {e}")
        return False

def test_get_activity_logs(token):
    """测试获取活动日志"""
    print("\n🔧 测试获取活动日志...")
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BASE_URL}/api/activity-logs?limit=5", headers=headers)
        
        if response.status_code == 200:
            logs = response.json()
            print(f"  ✅ 获取活动日志成功: {len(logs)} 条日志")
            for log in logs:
                print(f"    - {log['action']} - {log['created_at']}")
            return True
        else:
            print(f"  ❌ 获取活动日志失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"  ❌ 获取活动日志错误: {e}")
        return False

def main():
    """主函数"""
    print("🎯 用户管理API测试")
    print("=" * 50)
    
    # 测试登录
    token = test_login()
    if not token:
        print("❌ 无法获取访问令牌，停止测试")
        return 1
    
    success_count = 0
    total_tests = 3
    
    # 测试获取用户列表
    if test_get_users(token):
        success_count += 1
    
    # 测试获取角色列表
    if test_get_roles(token):
        success_count += 1
    
    # 测试获取活动日志
    if test_get_activity_logs(token):
        success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 用户管理API测试全部通过！")
        print("✅ 后端服务正常运行，可以访问前端页面")
        print(f"🌐 后端服务地址: {BASE_URL}")
        print("📋 可用的用户管理功能:")
        print("  - 用户列表查看")
        print("  - 角色管理")
        print("  - 活动日志查看")
        print("  - 用户创建/编辑/删除")
        print("  - 密码重置")
        print("  - 角色分配")
    else:
        print("⚠️  部分API测试失败")
    
    return 0 if success_count == total_tests else 1

if __name__ == "__main__":
    exit(main())
