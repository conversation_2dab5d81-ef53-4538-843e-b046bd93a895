#!/usr/bin/env python3
"""
测试统一资产管道的简单脚本
"""
import asyncio
import sys
import os

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database import AsyncSessionLocal
from elasticsearch_client import get_es_client
from asset_pipeline import AssetAggregationPipeline


async def test_pipeline():
    """测试统一资产管道"""
    print("🚀 开始测试统一资产聚合管道...")
    
    try:
        # 连接数据库和ES
        async with AsyncSessionLocal() as db:
            es_client = await get_es_client()
            pipeline = AssetAggregationPipeline(es_client, db)
            
            print("✅ 数据库和ES连接成功")
            
            # 测试索引创建
            print("📊 检查统一索引...")
            await pipeline.ensure_unified_index()
            print("✅ 统一索引准备完成")
            
            # 收集原始数据（限制数量以快速测试）
            print("🔍 收集原始资产数据...")
            raw_assets = await pipeline.collect_raw_assets()
            print(f"✅ 收集到 {len(raw_assets)} 条原始资产")
            
            if len(raw_assets) == 0:
                print("⚠️ 没有找到原始资产数据，退出测试")
                return 0
            
            # 测试标准化（只处理前10条）
            print("🔄 测试资产标准化...")
            test_assets = raw_assets[:10]  # 只测试前10条
            normalized_assets = []
            
            for raw_asset in test_assets:
                normalized = pipeline.normalize_asset(raw_asset)
                if normalized:
                    normalized_assets.append(normalized)
            
            print(f"✅ 成功标准化 {len(normalized_assets)} 条资产")
            
            # 测试去重
            print("🔧 测试资产去重...")
            deduplicated_assets = await pipeline.deduplicate_assets(normalized_assets)
            print(f"✅ 去重后剩余 {len(deduplicated_assets)} 条资产")
            
            # 测试数据库同步
            print("💾 测试数据库同步...")
            await pipeline.sync_to_postgresql(deduplicated_assets)
            print("✅ 数据库同步成功")
            
            print(f"🎉 测试完成！成功处理了 {len(deduplicated_assets)} 条资产")
            return len(deduplicated_assets)
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return -1


async def main():
    """主函数"""
    result = await test_pipeline()
    if result > 0:
        print(f"\n✨ 统一资产管道测试成功！共处理 {result} 条资产")
    elif result == 0:
        print("\n⚠️ 测试完成，但没有处理任何资产（可能没有原始数据）")
    else:
        print("\n💥 测试失败")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())