#!/usr/bin/env python3
"""
资产管理系统 V3.0 测试脚本
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import List, Dict, Any

from database import AsyncSessionLocal
from elasticsearch_client import get_es_client
from asset_management_v3 import (
    create_asset_manager_v3, DataSource, AssetType, Asset,
    AssetMetadata, AssetData, AssetRelation, ProcessingInfo
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AssetManagementV3Tester:
    """资产管理系统V3测试器"""
    
    def __init__(self):
        self.asset_manager = None
        self.test_results = []
        
    async def initialize(self):
        """初始化测试环境"""
        try:
            es_client = await get_es_client()
            async with AsyncSessionLocal() as db_session:
                self.asset_manager = await create_asset_manager_v3(es_client, db_session)
            logger.info("✅ 测试环境初始化完成")
            return True
        except Exception as e:
            logger.error(f"❌ 测试环境初始化失败: {e}")
            return False
    
    def add_test_result(self, test_name: str, success: bool, message: str, data: Any = None):
        """添加测试结果"""
        self.test_results.append({
            "test_name": test_name,
            "success": success,
            "message": message,
            "data": data,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        status = "✅" if success else "❌"
        logger.info(f"{status} {test_name}: {message}")
    
    async def test_data_ingestion(self):
        """测试数据摄取功能"""
        test_name = "数据摄取测试"
        
        try:
            # 准备测试数据
            test_data = [
                {
                    "value": "test-domain.com",
                    "name": "测试域名",
                    "description": "用于测试的域名",
                    "tags": ["test", "domain"],
                    "confidence": 0.9
                },
                {
                    "value": "*************",
                    "name": "测试IP",
                    "description": "用于测试的IP地址",
                    "tags": ["test", "ip"],
                    "confidence": 0.8
                }
            ]
            
            # 执行摄取
            result = await self.asset_manager.ingest_assets(
                raw_data_list=test_data,
                data_source=DataSource.MANUAL_IMPORT,
                source_id="test_ingestion"
            )
            
            # 验证结果
            if result["status"] == "success" and result["storage"]["success"] > 0:
                self.add_test_result(
                    test_name, True,
                    f"成功摄取 {result['storage']['success']} 个资产",
                    result
                )
            else:
                self.add_test_result(
                    test_name, False,
                    f"摄取失败: {result}",
                    result
                )
                
        except Exception as e:
            self.add_test_result(test_name, False, f"摄取异常: {e}")
    
    async def test_search_functionality(self):
        """测试搜索功能"""
        test_name = "搜索功能测试"
        
        try:
            # 测试基础搜索
            search_result = await self.asset_manager.search_assets(
                query="test",
                page=1,
                size=10
            )
            
            if search_result["total"] > 0:
                self.add_test_result(
                    test_name, True,
                    f"搜索成功，找到 {search_result['total']} 个结果",
                    search_result
                )
            else:
                self.add_test_result(
                    test_name, False,
                    "搜索未找到结果",
                    search_result
                )
                
        except Exception as e:
            self.add_test_result(test_name, False, f"搜索异常: {e}")
    
    async def test_filtering(self):
        """测试过滤功能"""
        test_name = "过滤功能测试"
        
        try:
            # 测试按资产类型过滤
            filter_result = await self.asset_manager.search_assets(
                filters={"metadata.data_source": "manual_import"},
                page=1,
                size=10
            )
            
            self.add_test_result(
                test_name, True,
                f"过滤成功，找到 {filter_result['total']} 个手动导入的资产",
                filter_result
            )
                
        except Exception as e:
            self.add_test_result(test_name, False, f"过滤异常: {e}")
    
    async def test_statistics(self):
        """测试统计功能"""
        test_name = "统计功能测试"
        
        try:
            stats = await self.asset_manager.get_statistics()
            
            if "total_assets" in stats:
                self.add_test_result(
                    test_name, True,
                    f"统计成功，总计 {stats['total_assets']} 个资产",
                    stats
                )
            else:
                self.add_test_result(
                    test_name, False,
                    "统计数据格式异常",
                    stats
                )
                
        except Exception as e:
            self.add_test_result(test_name, False, f"统计异常: {e}")
    
    async def test_deduplication(self):
        """测试去重功能"""
        test_name = "去重功能测试"
        
        try:
            # 插入重复数据
            duplicate_data = [
                {
                    "value": "duplicate-test.com",
                    "name": "重复测试域名1",
                    "tags": ["duplicate", "test"]
                },
                {
                    "value": "duplicate-test.com",  # 相同的值
                    "name": "重复测试域名2",
                    "tags": ["duplicate", "test"]
                }
            ]
            
            result = await self.asset_manager.ingest_assets(
                raw_data_list=duplicate_data,
                data_source=DataSource.MANUAL_IMPORT,
                source_id="test_deduplication"
            )
            
            # 检查去重统计
            dedup_stats = result.get("deduplication", {})
            if dedup_stats.get("duplicates_found", 0) > 0:
                self.add_test_result(
                    test_name, True,
                    f"去重成功，发现 {dedup_stats['duplicates_found']} 个重复项",
                    dedup_stats
                )
            else:
                self.add_test_result(
                    test_name, True,
                    "去重功能正常（未发现重复项）",
                    dedup_stats
                )
                
        except Exception as e:
            self.add_test_result(test_name, False, f"去重测试异常: {e}")
    
    async def test_data_quality(self):
        """测试数据质量检查"""
        test_name = "数据质量测试"
        
        try:
            # 插入质量不同的数据
            quality_test_data = [
                {
                    "value": "valid-domain.com",
                    "name": "有效域名",
                    "description": "这是一个有效的域名",
                    "tags": ["valid", "quality"]
                },
                {
                    "value": "invalid..domain",  # 无效域名格式
                    "name": "无效域名",
                    "tags": ["invalid", "quality"]
                }
            ]
            
            result = await self.asset_manager.ingest_assets(
                raw_data_list=quality_test_data,
                data_source=DataSource.MANUAL_IMPORT,
                asset_type=AssetType.DOMAIN,
                source_id="test_quality"
            )
            
            # 检查处理结果
            ingestion_stats = result.get("ingestion", {})
            if ingestion_stats.get("invalid", 0) > 0:
                self.add_test_result(
                    test_name, True,
                    f"质量检查成功，发现 {ingestion_stats['invalid']} 个无效资产",
                    ingestion_stats
                )
            else:
                self.add_test_result(
                    test_name, True,
                    "质量检查正常（所有数据有效）",
                    ingestion_stats
                )
                
        except Exception as e:
            self.add_test_result(test_name, False, f"质量检查异常: {e}")
    
    async def test_performance(self):
        """测试性能"""
        test_name = "性能测试"
        
        try:
            # 批量插入测试
            batch_data = []
            for i in range(100):
                batch_data.append({
                    "value": f"perf-test-{i}.com",
                    "name": f"性能测试域名 {i}",
                    "tags": ["performance", "test"]
                })
            
            start_time = datetime.utcnow()
            result = await self.asset_manager.ingest_assets(
                raw_data_list=batch_data,
                data_source=DataSource.MANUAL_IMPORT,
                source_id="test_performance"
            )
            end_time = datetime.utcnow()
            
            duration = (end_time - start_time).total_seconds()
            throughput = len(batch_data) / duration if duration > 0 else 0
            
            self.add_test_result(
                test_name, True,
                f"批量处理 {len(batch_data)} 个资产，耗时 {duration:.2f}s，吞吐量 {throughput:.1f} 资产/秒",
                {"duration": duration, "throughput": throughput, "count": len(batch_data)}
            )
                
        except Exception as e:
            self.add_test_result(test_name, False, f"性能测试异常: {e}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始运行资产管理系统V3测试套件...")
        
        # 初始化
        if not await self.initialize():
            return
        
        # 运行测试
        await self.test_data_ingestion()
        await self.test_search_functionality()
        await self.test_filtering()
        await self.test_statistics()
        await self.test_deduplication()
        await self.test_data_quality()
        await self.test_performance()
        
        # 生成测试报告
        self.generate_test_report()
    
    def generate_test_report(self):
        """生成测试报告"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "="*60)
        print("📊 资产管理系统V3测试报告")
        print("="*60)
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {failed_tests}")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        print("\n详细结果:")
        
        for result in self.test_results:
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            print(f"  {status} {result['test_name']}: {result['message']}")
        
        print("\n" + "="*60)
        
        # 保存详细报告到文件
        report_file = f"asset_management_v3_test_report_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"📄 详细测试报告已保存到: {report_file}")


async def main():
    """主函数"""
    tester = AssetManagementV3Tester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
