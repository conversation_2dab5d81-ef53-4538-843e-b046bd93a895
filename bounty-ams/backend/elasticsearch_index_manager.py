"""
Elasticsearch索引管理器
优化索引结构，支持大数据量存储和快速检索
"""

from elasticsearch import AsyncElasticsearch
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import logging
import json

logger = logging.getLogger(__name__)

class ElasticsearchIndexManager:
    """Elasticsearch索引管理器"""
    
    def __init__(self, es_client: AsyncElasticsearch):
        self.es_client = es_client
        
        # 索引配置
        self.asset_index_pattern = "unified-assets-{date}"
        self.asset_template_name = "unified-assets-template"
        self.asset_alias = "unified-assets"
        
        # 性能优化配置
        self.shard_config = {
            "small_dataset": {"shards": 1, "replicas": 1},      # < 1GB
            "medium_dataset": {"shards": 3, "replicas": 1},     # 1GB - 10GB  
            "large_dataset": {"shards": 5, "replicas": 2},      # 10GB - 100GB
            "xlarge_dataset": {"shards": 10, "replicas": 2}     # > 100GB
        }
    
    async def create_optimized_index_template(self) -> bool:
        """创建优化的索引模板"""
        try:
            template = {
                "index_patterns": ["unified-assets-*"],
                "template": {
                    "settings": {
                        # 基础设置
                        "number_of_shards": 3,
                        "number_of_replicas": 1,
                        "max_result_window": 50000,

                        # 简化的分析器配置
                        "analysis": {
                            "analyzer": {
                                "asset_analyzer": {
                                    "type": "keyword"
                                },
                                "text_analyzer": {
                                    "type": "standard"
                                }
                            }
                        }
                    },
                    "mappings": {
                        "properties": {
                            # 核心资产字段 - 优化搜索性能
                            "asset_id": {
                                "type": "keyword",
                                "index": True,
                                "doc_values": True
                            },
                            "asset_type": {
                                "type": "keyword",
                                "index": True,
                                "doc_values": True,
                                "eager_global_ordinals": True  # 优化聚合性能
                            },
                            "asset_value": {
                                "type": "text",
                                "fields": {
                                    "keyword": {"type": "keyword"}
                                }
                            },
                            "asset_host": {"type": "keyword"},
                            "asset_port": {"type": "integer"},
                            "asset_service": {"type": "keyword"},
                            
                            # 指纹和去重字段
                            "fingerprint_hash": {
                                "type": "keyword",
                                "index": True,
                                "doc_values": True
                            },
                            "is_duplicate": {
                                "type": "boolean",
                                "index": True,
                                "doc_values": True
                            },
                            "duplicate_of": {
                                "type": "keyword",
                                "index": True,
                                "doc_values": False
                            },
                            "duplicate_score": {
                                "type": "float",
                                "index": False,
                                "doc_values": True
                            },
                            
                            # 元数据字段 - 优化聚合
                            "source": {
                                "type": "keyword",
                                "index": True,
                                "doc_values": True,
                                "eager_global_ordinals": True
                            },
                            "confidence": {
                                "type": "keyword",
                                "index": True,
                                "doc_values": True,
                                "eager_global_ordinals": True
                            },
                            "status": {
                                "type": "keyword",
                                "index": True,
                                "doc_values": True,
                                "eager_global_ordinals": True
                            },
                            "tags": {
                                "type": "keyword",
                                "index": True,
                                "doc_values": True
                            },
                            
                            # 关联信息字段
                            "platform_id": {
                                "type": "keyword",
                                "index": True,
                                "doc_values": True
                            },
                            "project_id": {
                                "type": "keyword",
                                "index": True,
                                "doc_values": True
                            },
                            "platform_name": {
                                "type": "text",
                                "analyzer": "text_analyzer",
                                "fields": {
                                    "keyword": {
                                        "type": "keyword",
                                        "index": True,
                                        "doc_values": True,
                                        "eager_global_ordinals": True
                                    }
                                }
                            },
                            "project_name": {
                                "type": "text",
                                "analyzer": "text_analyzer",
                                "fields": {
                                    "keyword": {
                                        "type": "keyword",
                                        "index": True,
                                        "doc_values": True,
                                        "eager_global_ordinals": True
                                    }
                                }
                            },
                            "workflow_id": {
                                "type": "keyword",
                                "index": True,
                                "doc_values": False
                            },
                            "task_id": {
                                "type": "keyword",
                                "index": True,
                                "doc_values": False
                            },
                            "agent_id": {
                                "type": "keyword",
                                "index": True,
                                "doc_values": False
                            },
                            
                            # 时间字段 - 优化范围查询
                            "discovered_at": {
                                "type": "date",
                                "format": "strict_date_optional_time||epoch_millis",
                                "index": True,
                                "doc_values": True
                            },
                            "verified_at": {
                                "type": "date",
                                "format": "strict_date_optional_time||epoch_millis",
                                "index": True,
                                "doc_values": True
                            },
                            "last_seen": {
                                "type": "date",
                                "format": "strict_date_optional_time||epoch_millis",
                                "index": True,
                                "doc_values": True
                            },
                            "created_at": {
                                "type": "date",
                                "format": "strict_date_optional_time||epoch_millis",
                                "index": True,
                                "doc_values": True
                            },
                            "updated_at": {
                                "type": "date",
                                "format": "strict_date_optional_time||epoch_millis",
                                "index": True,
                                "doc_values": True
                            },
                            
                            # 技术栈信息 - 嵌套对象
                            "technology_stack": {
                                "type": "nested",
                                "properties": {
                                    "name": {"type": "keyword", "index": True},
                                    "version": {"type": "keyword", "index": True},
                                    "category": {"type": "keyword", "index": True}
                                }
                            },
                            
                            # 端口信息 - 嵌套对象
                            "ports": {
                                "type": "nested",
                                "properties": {
                                    "port": {"type": "integer", "index": True},
                                    "protocol": {"type": "keyword", "index": True},
                                    "service": {"type": "keyword", "index": True},
                                    "version": {"type": "keyword", "index": True},
                                    "state": {"type": "keyword", "index": True}
                                }
                            },
                            
                            # 漏洞关联 - 嵌套对象
                            "vulnerabilities": {
                                "type": "nested",
                                "properties": {
                                    "cve_id": {"type": "keyword", "index": True},
                                    "severity": {"type": "keyword", "index": True},
                                    "status": {"type": "keyword", "index": True},
                                    "verified": {"type": "boolean", "index": True},
                                    "verified_at": {"type": "date", "index": True}
                                }
                            },
                            
                            # 地理位置
                            "geo_location": {
                                "type": "geo_point",
                                "index": True,
                                "doc_values": True
                            },
                            "country": {
                                "type": "keyword",
                                "index": True,
                                "doc_values": True
                            },
                            "city": {
                                "type": "keyword",
                                "index": True,
                                "doc_values": True
                            },
                            "organization": {
                                "type": "keyword",
                                "index": True,
                                "doc_values": True
                            },
                            
                            # 原始数据和扩展字段
                            "raw_data": {
                                "type": "object",
                                "enabled": False  # 不索引，仅存储
                            },
                            "metadata": {
                                "type": "object",
                                "dynamic": True,
                                "properties": {
                                    "source_entity_id": {"type": "keyword", "index": True},
                                    "source_model_type": {"type": "keyword", "index": True},
                                    "sync_time": {"type": "date", "index": True}
                                }
                            },
                            "custom_fields": {
                                "type": "object",
                                "dynamic": True
                            },
                            
                            # 搜索优化字段
                            "searchable_text": {
                                "type": "text",
                                "analyzer": "text_analyzer",
                                "search_analyzer": "text_analyzer",
                                "index": True,
                                "norms": False  # 不需要评分标准化
                            },
                            
                            # 数据质量字段
                            "data_quality_score": {
                                "type": "float",
                                "index": True,
                                "doc_values": True
                            },
                            "validation_errors": {
                                "type": "keyword",
                                "index": True,
                                "doc_values": True
                            },
                            "processing_notes": {
                                "type": "text",
                                "analyzer": "text_analyzer",
                                "index": False,  # 仅存储，不搜索
                                "doc_values": False
                            }
                        }
                    }
                }
            }
            
            await self.es_client.indices.put_index_template(
                name=self.asset_template_name,
                body=template
            )
            
            logger.info(f"✅ 创建优化索引模板: {self.asset_template_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建索引模板失败: {e}")
            return False

    async def create_monthly_index(self, date: Optional[datetime] = None) -> str:
        """创建月度索引"""
        if date is None:
            date = datetime.utcnow()

        index_name = self.asset_index_pattern.format(
            date=date.strftime("%Y-%m")
        )

        try:
            # 检查索引是否已存在
            if await self.es_client.indices.exists(index=index_name):
                logger.info(f"索引 {index_name} 已存在")
                return index_name

            # 根据数据量动态调整分片配置
            dataset_size = await self._estimate_dataset_size()
            shard_config = self._get_optimal_shard_config(dataset_size)

            # 创建索引
            index_settings = {
                "settings": {
                    "number_of_shards": shard_config["shards"],
                    "number_of_replicas": shard_config["replicas"]
                }
            }

            await self.es_client.indices.create(
                index=index_name,
                body=index_settings
            )

            # 更新别名
            await self._update_alias(index_name)

            logger.info(f"✅ 创建月度索引: {index_name}")
            return index_name

        except Exception as e:
            logger.error(f"❌ 创建月度索引失败: {e}")
            raise

    async def _estimate_dataset_size(self) -> str:
        """估算数据集大小"""
        try:
            # 获取当前索引统计信息
            stats = await self.es_client.indices.stats(index=self.asset_alias)

            total_size_bytes = 0
            for index_stats in stats.get("indices", {}).values():
                total_size_bytes += index_stats.get("total", {}).get("store", {}).get("size_in_bytes", 0)

            # 转换为GB
            total_size_gb = total_size_bytes / (1024 ** 3)

            if total_size_gb < 1:
                return "small_dataset"
            elif total_size_gb < 10:
                return "medium_dataset"
            elif total_size_gb < 100:
                return "large_dataset"
            else:
                return "xlarge_dataset"

        except Exception as e:
            logger.warning(f"估算数据集大小失败: {e}")
            return "medium_dataset"  # 默认值

    def _get_optimal_shard_config(self, dataset_size: str) -> Dict[str, int]:
        """获取最优分片配置"""
        return self.shard_config.get(dataset_size, self.shard_config["medium_dataset"])

    async def _update_alias(self, new_index: str):
        """更新索引别名"""
        try:
            # 获取当前别名指向的索引
            current_aliases = await self.es_client.indices.get_alias(name=self.asset_alias)

            actions = []

            # 移除旧的别名（如果存在）
            for index_name in current_aliases.keys():
                if index_name != new_index:
                    actions.append({
                        "remove": {
                            "index": index_name,
                            "alias": self.asset_alias
                        }
                    })

            # 添加新的别名
            actions.append({
                "add": {
                    "index": new_index,
                    "alias": self.asset_alias
                }
            })

            if actions:
                await self.es_client.indices.update_aliases(body={"actions": actions})
                logger.info(f"✅ 更新别名 {self.asset_alias} -> {new_index}")

        except Exception as e:
            logger.error(f"❌ 更新别名失败: {e}")
            raise

    async def optimize_indices(self) -> Dict[str, Any]:
        """优化索引性能"""
        try:
            results = {}

            # 获取所有资产索引
            indices = await self.es_client.cat.indices(
                index="unified-assets-*",
                format="json"
            )

            for index_info in indices:
                index_name = index_info["index"]

                # 强制合并段
                merge_result = await self.es_client.indices.forcemerge(
                    index=index_name,
                    max_num_segments=1,
                    wait_for_completion=False
                )

                # 刷新索引
                await self.es_client.indices.refresh(index=index_name)

                results[index_name] = {
                    "merge_task": merge_result.get("task"),
                    "status": "optimizing"
                }

            return {
                "message": "索引优化已启动",
                "indices": results,
                "timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"索引优化失败: {e}")
            raise

    async def get_index_health(self) -> Dict[str, Any]:
        """获取索引健康状态"""
        try:
            # 集群健康状态
            cluster_health = await self.es_client.cluster.health()

            # 索引统计信息
            index_stats = await self.es_client.indices.stats(index=self.asset_alias)

            # 索引设置
            index_settings = await self.es_client.indices.get_settings(index=self.asset_alias)

            # 计算总体指标
            total_docs = 0
            total_size_bytes = 0

            for index_name, stats in index_stats.get("indices", {}).items():
                total_docs += stats.get("total", {}).get("docs", {}).get("count", 0)
                total_size_bytes += stats.get("total", {}).get("store", {}).get("size_in_bytes", 0)

            return {
                "cluster_health": {
                    "status": cluster_health.get("status"),
                    "number_of_nodes": cluster_health.get("number_of_nodes"),
                    "active_shards": cluster_health.get("active_shards"),
                    "relocating_shards": cluster_health.get("relocating_shards"),
                    "unassigned_shards": cluster_health.get("unassigned_shards")
                },
                "index_metrics": {
                    "total_documents": total_docs,
                    "total_size_mb": round(total_size_bytes / (1024 ** 2), 2),
                    "indices_count": len(index_stats.get("indices", {}))
                },
                "performance_metrics": {
                    "search_time_ms": sum(
                        stats.get("total", {}).get("search", {}).get("time_in_millis", 0)
                        for stats in index_stats.get("indices", {}).values()
                    ),
                    "indexing_time_ms": sum(
                        stats.get("total", {}).get("indexing", {}).get("time_in_millis", 0)
                        for stats in index_stats.get("indices", {}).values()
                    ),
                    "search_rate": sum(
                        stats.get("total", {}).get("search", {}).get("query_total", 0)
                        for stats in index_stats.get("indices", {}).values()
                    ),
                    "indexing_rate": sum(
                        stats.get("total", {}).get("indexing", {}).get("index_total", 0)
                        for stats in index_stats.get("indices", {}).values()
                    )
                },
                "timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"获取索引健康状态失败: {e}")
            return {}

    async def cleanup_old_indices(self, retention_days: int = 90) -> Dict[str, Any]:
        """清理旧索引"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=retention_days)

            # 获取所有资产索引
            indices = await self.es_client.cat.indices(
                index="unified-assets-*",
                format="json"
            )

            deleted_indices = []
            kept_indices = []

            for index_info in indices:
                index_name = index_info["index"]

                # 从索引名称中提取日期
                try:
                    date_part = index_name.split("-")[-1]  # 获取最后部分，如 "2025-07"
                    index_date = datetime.strptime(date_part, "%Y-%m")

                    if index_date < cutoff_date:
                        # 删除旧索引
                        await self.es_client.indices.delete(index=index_name)
                        deleted_indices.append({
                            "index": index_name,
                            "date": index_date.isoformat(),
                            "size_mb": round(float(index_info.get("store.size", "0b").replace("b", "")) / (1024**2), 2)
                        })
                    else:
                        kept_indices.append(index_name)

                except (ValueError, IndexError) as e:
                    logger.warning(f"无法解析索引日期: {index_name}, 错误: {e}")
                    kept_indices.append(index_name)

            return {
                "message": f"清理完成，删除 {len(deleted_indices)} 个旧索引",
                "deleted_indices": deleted_indices,
                "kept_indices": kept_indices,
                "retention_days": retention_days,
                "cutoff_date": cutoff_date.isoformat()
            }

        except Exception as e:
            logger.error(f"清理旧索引失败: {e}")
            raise

# 创建索引管理器实例
async def create_index_manager(es_client: AsyncElasticsearch) -> ElasticsearchIndexManager:
    """创建索引管理器实例"""
    manager = ElasticsearchIndexManager(es_client)

    # 初始化索引模板
    await manager.create_optimized_index_template()

    # 创建当前月份的索引
    await manager.create_monthly_index()

    return manager
