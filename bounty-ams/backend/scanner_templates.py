"""
扫描器模板系统
定义不同扫描器的数据结构和字段映射模板
"""

import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

class ScannerType(Enum):
    """扫描器类型枚举"""
    SUBFINDER = "subfinder"
    AMASS = "amass" 
    NMAP = "nmap"
    MASSCAN = "masscan"
    HTTPX = "httpx"
    NUCLEI = "nuclei"
    FFUF = "ffuf"
    DIRSEARCH = "dirsearch"
    WHATWEB = "whatweb"
    NAABU = "naabu"
    FOFA = "fofa"           # FOFA网络空间测绘
    SHODAN = "shodan"       # Shodan搜索引擎
    CENSYS = "censys"       # Censys搜索引擎
    QUAKE = "quake"         # 360 Quake网络空间测绘
    HUNTER = "hunter"       # 鹰图平台
    ZOOMEYE = "zoomeye"     # ZoomEye网络空间搜索引擎
    
class AssetType(Enum):
    """资产类型枚举"""
    DOMAIN = "domain"
    SUBDOMAIN = "subdomain" 
    IP = "ip"
    PORT = "port"
    URL = "url"
    SERVICE = "service"
    VULNERABILITY = "vulnerability"
    DIRECTORY = "directory"
    TECHNOLOGY = "technology"

@dataclass
class FieldMapping:
    """字段映射配置"""
    source_field: str           # 原始字段名
    target_field: str          # 目标标准字段名
    field_type: str            # 字段类型: text, number, boolean, date
    is_required: bool = False  # 是否必需
    default_value: Any = None  # 默认值
    transform_func: str = None # 转换函数名

@dataclass 
class ScannerTemplate:
    """扫描器模板配置"""
    scanner_type: ScannerType
    asset_type: AssetType
    index_pattern: str         # ES索引模式
    confidence_default: float  # 默认置信度
    field_mappings: List[FieldMapping]
    sample_data: Dict[str, Any] = None
    description: str = ""

class ScannerTemplateManager:
    """扫描器模板管理器"""
    
    def __init__(self):
        self.templates = self._init_templates()
    
    def _init_templates(self) -> Dict[ScannerType, ScannerTemplate]:
        """初始化所有扫描器模板"""
        templates = {}
        
        # Subfinder 子域名发现模板
        templates[ScannerType.SUBFINDER] = ScannerTemplate(
            scanner_type=ScannerType.SUBFINDER,
            asset_type=AssetType.SUBDOMAIN,
            index_pattern="subfinder-results-{date}",
            confidence_default=0.8,
            field_mappings=[
                FieldMapping("host", "asset_value", "text", True),
                FieldMapping("subdomain", "asset_value", "text", True),
                FieldMapping("domain", "parent_domain", "text", False),
                FieldMapping("source", "discovery_source", "text", False),
                FieldMapping("timestamp", "discovered_at", "date", False)
            ],
            sample_data={
                "host": "api.example.com",
                "subdomain": "api.example.com", 
                "domain": "example.com",
                "source": "crtsh",
                "timestamp": "2025-07-15T10:30:00Z"
            },
            description="Subfinder子域名发现结果"
        )
        
        # Amass 子域名发现模板
        templates[ScannerType.AMASS] = ScannerTemplate(
            scanner_type=ScannerType.AMASS,
            asset_type=AssetType.SUBDOMAIN,
            index_pattern="amass-results-{date}",
            confidence_default=0.9,
            field_mappings=[
                FieldMapping("name", "asset_value", "text", True),
                FieldMapping("domain", "parent_domain", "text", False),
                FieldMapping("addresses", "ip_addresses", "text", False),
                FieldMapping("tag", "discovery_method", "text", False),
                FieldMapping("sources", "data_sources", "text", False)
            ],
            sample_data={
                "name": "www.example.com",
                "domain": "example.com",
                "addresses": ["*******", "*******"],
                "tag": "dns",
                "sources": ["DNS", "Scraping"]
            },
            description="Amass子域名枚举结果"
        )
        
        # Nmap 端口扫描模板
        templates[ScannerType.NMAP] = ScannerTemplate(
            scanner_type=ScannerType.NMAP,
            asset_type=AssetType.PORT,
            index_pattern="nmap-results-{date}",
            confidence_default=0.95,
            field_mappings=[
                FieldMapping("ip", "asset_host", "text", True),
                FieldMapping("port", "asset_port", "number", True),
                FieldMapping("protocol", "protocol", "text", False),
                FieldMapping("state", "port_state", "text", False),
                FieldMapping("service", "service_name", "text", False),
                FieldMapping("version", "service_version", "text", False),
                FieldMapping("banner", "service_banner", "text", False)
            ],
            sample_data={
                "ip": "***********",
                "port": 80,
                "protocol": "tcp",
                "state": "open", 
                "service": "http",
                "version": "Apache httpd 2.4.41",
                "banner": "Apache/2.4.41 (Ubuntu)"
            },
            description="Nmap端口扫描结果"
        )
        
        # Naabu 端口扫描模板
        templates[ScannerType.NAABU] = ScannerTemplate(
            scanner_type=ScannerType.NAABU,
            asset_type=AssetType.PORT,
            index_pattern="naabu-results-{date}",
            confidence_default=0.85,
            field_mappings=[
                FieldMapping("host", "asset_host", "text", True),
                FieldMapping("port", "asset_port", "number", True),
                FieldMapping("ip", "resolved_ip", "text", False),
                FieldMapping("protocol", "protocol", "text", False, "tcp"),
                FieldMapping("timestamp", "discovered_at", "date", False)
            ],
            sample_data={
                "host": "example.com",
                "port": 443,
                "ip": "*******",
                "protocol": "tcp",
                "timestamp": "2025-07-15T10:30:00Z"
            },
            description="Naabu快速端口扫描结果"
        )
        
        # Httpx Web探测模板 - 扩展字段映射
        templates[ScannerType.HTTPX] = ScannerTemplate(
            scanner_type=ScannerType.HTTPX,
            asset_type=AssetType.URL,
            index_pattern="httpx-results-{date}",
            confidence_default=0.9,
            field_mappings=[
                FieldMapping("url", "asset_value", "text", True),
                FieldMapping("url", "url", "text", True),
                FieldMapping("host", "asset_host", "text", False),
                FieldMapping("status-code", "website_status_code", "number", False),
                FieldMapping("status_code", "website_status_code", "number", False),
                FieldMapping("title", "webpage_title", "text", False),
                FieldMapping("content-length", "content_length", "number", False),
                FieldMapping("content_length", "content_length", "number", False),
                FieldMapping("technologies", "technologies", "text", False),
                FieldMapping("webserver", "web_server", "text", False),
                FieldMapping("web_server", "web_server", "text", False),
                FieldMapping("server", "web_server", "text", False),
                FieldMapping("response-time", "response_time", "number", False),
                FieldMapping("response_time", "response_time", "number", False),
                FieldMapping("path", "website_path", "text", False),
                FieldMapping("scheme", "transport_protocol", "text", False),
                FieldMapping("port", "asset_port", "number", False),
                # 网站相关字段
                FieldMapping("keywords", "website_keywords", "text", False),
                FieldMapping("website_type", "website_type", "text", False),
                # 应用信息字段
                FieldMapping("application", "application_name", "text", False),
                FieldMapping("app_name", "application_name", "text", False),
                FieldMapping("vendor", "application_vendor", "text", False),
                FieldMapping("version", "service_version", "text", False),
                # 证书字段
                FieldMapping("tls_subject", "certificate_subject", "text", False),
                FieldMapping("tls_issuer", "certificate_issuer", "text", False),
                FieldMapping("cert_subject", "certificate_subject", "text", False),
                FieldMapping("cert_issuer", "certificate_issuer", "text", False)
            ],
            sample_data={
                "url": "https://example.com",
                "status-code": 200,
                "title": "Example Domain",
                "content-length": 1256,
                "technologies": ["Apache", "PHP"],
                "webserver": "Apache/2.4.41",
                "response-time": "150ms",
                "host": "example.com",
                "port": 443,
                "path": "/",
                "scheme": "https"
            },
            description="Httpx Web服务探测结果 - 支持全面字段映射"
        )
        
        # Nuclei 漏洞扫描模板
        templates[ScannerType.NUCLEI] = ScannerTemplate(
            scanner_type=ScannerType.NUCLEI,
            asset_type=AssetType.VULNERABILITY,
            index_pattern="nuclei-results-{date}",
            confidence_default=0.7,
            field_mappings=[
                FieldMapping("template-id", "template_id", "text", True),
                FieldMapping("info.name", "vulnerability_name", "text", True),
                FieldMapping("info.severity", "severity", "text", False),
                FieldMapping("host", "target_host", "text", True),
                FieldMapping("matched-at", "matched_url", "text", False),
                FieldMapping("extracted-results", "extracted_data", "text", False),
                FieldMapping("timestamp", "discovered_at", "date", False)
            ],
            sample_data={
                "template-id": "cve-2021-44228",
                "info": {
                    "name": "Apache Log4j RCE",
                    "severity": "critical"
                },
                "host": "https://vulnerable.example.com",
                "matched-at": "https://vulnerable.example.com/login",
                "extracted-results": ["log4j vulnerable"],
                "timestamp": "2025-07-15T10:30:00Z"
            },
            description="Nuclei漏洞扫描结果"
        )
        
        # FFUF 目录暴破模板
        templates[ScannerType.FFUF] = ScannerTemplate(
            scanner_type=ScannerType.FFUF,
            asset_type=AssetType.DIRECTORY,
            index_pattern="ffuf-results-{date}",
            confidence_default=0.6,
            field_mappings=[
                FieldMapping("url", "asset_value", "text", True),
                FieldMapping("status", "http_status", "number", False),
                FieldMapping("length", "content_length", "number", False),
                FieldMapping("words", "word_count", "number", False),
                FieldMapping("lines", "line_count", "number", False),
                FieldMapping("resultfile", "result_file", "text", False)
            ],
            sample_data={
                "url": "https://example.com/admin",
                "status": 200,
                "length": 1024,
                "words": 150,
                "lines": 25,
                "resultfile": "results.txt"
            },
            description="FFUF目录暴破结果"
        )
        
        # FOFA网络空间测绘模板 - 支持全面字段映射
        templates[ScannerType.FOFA] = ScannerTemplate(
            scanner_type=ScannerType.FOFA,
            asset_type=AssetType.SERVICE,
            index_pattern="fofa-results-{date}",
            confidence_default=0.9,
            field_mappings=[
                # 基础网络信息
                FieldMapping("ip", "ip_address", "text", True),
                FieldMapping("host", "asset_host", "text", False),
                FieldMapping("port", "asset_port", "number", False),
                FieldMapping("protocol", "transport_protocol", "text", False),
                FieldMapping("domain", "asset_value", "text", False),
                FieldMapping("url", "url", "text", False),
                
                # 服务信息
                FieldMapping("server", "service_name", "text", False),
                FieldMapping("service", "service_name", "text", False),
                FieldMapping("banner", "service_raw_response", "text", False),
                FieldMapping("product", "service_product", "text", False),
                FieldMapping("version", "service_version", "text", False),
                
                # 网站信息
                FieldMapping("title", "webpage_title", "text", False),
                FieldMapping("status_code", "website_status_code", "number", False),
                FieldMapping("body", "service_raw_response", "text", False),
                
                # 地理位置信息
                FieldMapping("country", "country_en", "text", False),
                FieldMapping("country_name", "country_cn", "text", False),
                FieldMapping("region", "province_en", "text", False),
                FieldMapping("city", "city_en", "text", False),
                
                # ASN信息
                FieldMapping("as_number", "asn_number", "text", False),
                FieldMapping("as_organization", "asn_organization", "text", False),
                FieldMapping("isp", "isp", "text", False),
                
                # 应用信息
                FieldMapping("app", "application_name", "text", False),
                FieldMapping("app_name", "application_name", "text", False),
                FieldMapping("vendor", "application_vendor", "text", False),
                FieldMapping("category", "application_category", "text", False),
                
                # 证书信息
                FieldMapping("cert_subject", "certificate_subject", "text", False),
                FieldMapping("cert_issuer", "certificate_issuer", "text", False),
                FieldMapping("cert_serial", "certificate_serial_number", "text", False),
                
                # ICP信息
                FieldMapping("icp_license", "icp_number", "text", False),
                FieldMapping("icp_company", "icp_unit", "text", False),
                
                # 其他
                FieldMapping("lastupdatetime", "scan_time", "date", False)
            ],
            sample_data={
                "ip": "*******",
                "host": "example.com",
                "port": 443,
                "protocol": "https",
                "title": "Example Site",
                "country": "China",
                "city": "Beijing",
                "as_organization": "China Telecom",
                "isp": "China Telecom",
                "app": "Apache httpd",
                "version": "2.4.41"
            },
            description="FOFA网络空间测绘结果 - 支持地理位置和应用指纹"
        )
        
        # Quake网络空间测绘模板 - 增强版支持真实数据格式
        templates[ScannerType.QUAKE] = ScannerTemplate(
            scanner_type=ScannerType.QUAKE,
            asset_type=AssetType.SERVICE,
            index_pattern="quake-results-{date}",
            confidence_default=0.9,
            field_mappings=[
                # 基础信息
                FieldMapping("ip", "ip_address", "text", True),
                FieldMapping("port", "asset_port", "number", False),
                FieldMapping("url", "url", "text", False),
                FieldMapping("url", "asset_value", "text", False),  # URL作为主要资产值
                FieldMapping("hostname", "asset_host", "text", False),
                FieldMapping("transport", "transport_protocol", "text", False),
                FieldMapping("asn", "asn_number", "text", False),
                FieldMapping("org", "asn_organization", "text", False),
                
                # 服务信息
                FieldMapping("service.name", "service_name", "text", False),
                FieldMapping("service.response", "service_raw_response", "text", False),
                FieldMapping("service.version", "service_version", "text", False),
                FieldMapping("service.cert", "service_certificate", "text", False),
                
                # 地理位置信息
                FieldMapping("location.country_cn", "country_cn", "text", False),
                FieldMapping("location.country_en", "country_en", "text", False),
                FieldMapping("location.province_cn", "province_cn", "text", False),
                FieldMapping("location.province_en", "province_en", "text", False),
                FieldMapping("location.city_cn", "city_cn", "text", False),
                FieldMapping("location.city_en", "city_en", "text", False),
                FieldMapping("location.district_cn", "district_cn", "text", False),
                FieldMapping("location.district_en", "district_en", "text", False),
                FieldMapping("location.isp", "isp", "text", False),
                
                # HTTP信息
                FieldMapping("service.http.title", "webpage_title", "text", False),
                FieldMapping("service.http.status_code", "website_status_code", "number", False),
                FieldMapping("service.http.host", "website_host", "text", False),
                FieldMapping("service.http.path", "website_path", "text", False),
                FieldMapping("service.http.meta_keywords", "website_keywords", "text", False),
                
                # 证书信息 - TLS解析
                FieldMapping("service.tls.handshake_log.server_certificates.certificate.parsed.subject_dn", "certificate_subject", "text", False),
                FieldMapping("service.tls.handshake_log.server_certificates.certificate.parsed.issuer_dn", "certificate_issuer", "text", False),
                FieldMapping("service.tls.handshake_log.server_certificates.certificate.parsed.serial_number", "certificate_serial_number", "text", False),
                FieldMapping("service.tls.handshake_log.server_certificates.certificate.parsed.subject.common_name", "certificate_subject_common_name", "text", False),
                
                # 组件信息
                FieldMapping("components.product_name_cn", "application_name", "text", False),
                FieldMapping("components.product_vendor", "application_vendor", "text", False),
                FieldMapping("components.product_level", "application_level", "text", False),
                FieldMapping("components.product_catalog", "application_category", "text", False),
                FieldMapping("components.product_type", "application_type", "text", False),
                
                # 时间
                FieldMapping("time", "scan_time", "date", False)
            ],
            sample_data={
                "ip": "***********",
                "port": 443,
                "url": "https://media-cdn.duizhang.fun:443",
                "transport": "tcp",
                "asn": 13335,
                "org": "Cloudflare, Inc.",
                "service": {
                    "name": "http/ssl",
                    "version": "1.1",
                    "http": {
                        "title": "Not Found",
                        "status_code": 404,
                        "host": "media-cdn.duizhang.fun",
                        "path": "/"
                    },
                    "tls": {
                        "handshake_log": {
                            "server_certificates": {
                                "certificate": {
                                    "parsed": {
                                        "subject_dn": "CN=media-cdn.duizhang.fun",
                                        "issuer_dn": "CN=WE1, O=Google Trust Services, C=US",
                                        "subject": {
                                            "common_name": ["media-cdn.duizhang.fun"]
                                        },
                                        "serial_number": "274266326884341355700436860479129717264"
                                    }
                                }
                            }
                        }
                    }
                },
                "location": {
                    "country_cn": "美国",
                    "country_en": "United States",
                    "province_cn": "",
                    "province_en": "",
                    "city_cn": "",
                    "city_en": "",
                    "isp": "Cloudflare, Inc."
                },
                "components": [{
                    "product_name_cn": "Cloudflare",
                    "product_vendor": "Cloudflare, Inc.",
                    "product_level": "中间支撑层",
                    "product_catalog": ["网络安全设备", "Web系统与应用"],
                    "product_type": ["Web服务器", "内容分发网络(CDN)"]
                }],
                "time": "2025-06-02T05:54:55.301Z"
            },
            description="360 Quake网络空间测绘结果 - 支持真实数据格式包含证书解析"
        )
        
        return templates
    
    def get_template(self, scanner_type: ScannerType) -> Optional[ScannerTemplate]:
        """获取指定扫描器的模板"""
        return self.templates.get(scanner_type)
    
    def get_all_templates(self) -> Dict[ScannerType, ScannerTemplate]:
        """获取所有模板"""
        return self.templates
    
    def detect_scanner_type(self, data_sample: Dict[str, Any]) -> Optional[ScannerType]:
        """根据数据样本智能检测扫描器类型"""
        field_signatures = {
            ScannerType.SUBFINDER: ["host", "subdomain", "source"],
            ScannerType.AMASS: ["name", "domain", "addresses", "tag"],
            ScannerType.NMAP: ["ip", "port", "protocol", "state", "service"],
            ScannerType.NAABU: ["host", "port", "ip"],
            ScannerType.HTTPX: ["url", "status-code", "title", "content-length"],
            ScannerType.NUCLEI: ["template-id", "info", "host", "matched-at"],
            ScannerType.FFUF: ["url", "status", "length", "words"],
            # 网络空间测绘平台特征
            ScannerType.FOFA: ["ip", "port", "country", "app", "banner"],
            ScannerType.QUAKE: ["ip", "port", "location", "components", "service"],
            ScannerType.SHODAN: ["ip_str", "port", "org", "data", "location"],
            ScannerType.CENSYS: ["ip", "location", "autonomous_system", "services"],
            ScannerType.HUNTER: ["ip", "port", "web_title", "domain", "company"],
            ScannerType.ZOOMEYE: ["ip", "portinfo", "geoinfo", "protocol", "app"]
        }
        
        max_match_score = 0
        best_match = None
        
        data_fields = set(data_sample.keys())
        
        for scanner_type, signature_fields in field_signatures.items():
            # 计算字段匹配度
            signature_set = set(signature_fields)
            match_count = len(data_fields & signature_set)
            match_score = match_count / len(signature_set)
            
            if match_score > max_match_score and match_score > 0.5:
                max_match_score = match_score
                best_match = scanner_type
        
        return best_match
    
    def generate_index_name(self, scanner_type: ScannerType, date: str = None) -> str:
        """生成索引名称"""
        if date is None:
            date = datetime.now().strftime("%Y-%m")
        
        template = self.get_template(scanner_type)
        if not template:
            return f"unknown-results-{date}"
        
        return template.index_pattern.format(date=date)
    
    def transform_data(self, scanner_type: ScannerType, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """根据模板转换数据"""
        template = self.get_template(scanner_type)
        if not template:
            return raw_data
        
        transformed = {
            "scanner_type": scanner_type.value,
            "asset_type": template.asset_type.value,
            "confidence": template.confidence_default,
            "discovered_at": datetime.utcnow().isoformat(),
            "raw_data": raw_data  # 保留原始数据
        }
        
        # 应用字段映射
        for mapping in template.field_mappings:
            source_value = self._get_nested_value(raw_data, mapping.source_field)
            
            if source_value is not None:
                transformed[mapping.target_field] = self._transform_value(
                    source_value, mapping.field_type, mapping.transform_func
                )
            elif mapping.is_required and mapping.default_value is not None:
                transformed[mapping.target_field] = mapping.default_value
        
        return transformed
    
    def _get_nested_value(self, data: Dict[str, Any], field_path: str) -> Any:
        """获取嵌套字段值"""
        keys = field_path.split('.')
        value = data
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return None
        
        return value
    
    def _transform_value(self, value: Any, field_type: str, transform_func: str = None) -> Any:
        """转换字段值类型"""
        if transform_func:
            # 这里可以实现自定义转换函数
            pass
        
        try:
            if field_type == "number":
                return float(value) if value else 0
            elif field_type == "boolean":
                return bool(value) if value else False
            elif field_type == "date":
                if isinstance(value, str):
                    return value
                return str(value)
            else:  # text
                return str(value) if value else ""
        except:
            return value

# 全局模板管理器实例
scanner_template_manager = ScannerTemplateManager()