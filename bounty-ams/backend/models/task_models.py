"""
任务管理相关的数据模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON, ForeignKey, Enum as SQLEnum
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import enum
import uuid

Base = declarative_base()

class TaskStatus(enum.Enum):
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"

class WorkflowStatus(enum.Enum):
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"

class AgentStatus(enum.Enum):
    ONLINE = "online"
    OFFLINE = "offline"
    BUSY = "busy"
    MAINTENANCE = "maintenance"

class TaskTemplate(Base):
    """任务模板表"""
    __tablename__ = "task_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    template_id = Column(String(100), unique=True, index=True, nullable=False)
    name = Column(String(200), nullable=False)
    version = Column(String(20), nullable=False)
    description = Column(Text)
    category = Column(String(50), nullable=False)
    tags = Column(JSON)  # 存储标签数组
    parameters = Column(JSON)  # 存储参数schema
    tools = Column(JSON)  # 存储工具配置
    execution = Column(JSON)  # 存储执行配置
    output_schema = Column(JSON)  # 存储输出schema
    created_by = Column(String(100))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    
    # 关系
    task_executions = relationship("TaskExecution", back_populates="template")

class Workflow(Base):
    """工作流表"""
    __tablename__ = "workflows"
    
    id = Column(Integer, primary_key=True, index=True)
    workflow_id = Column(String(100), unique=True, index=True, nullable=False)
    name = Column(String(200), nullable=False)
    version = Column(String(20), nullable=False)
    description = Column(Text)
    parameters = Column(JSON)  # 存储参数schema
    stages = Column(JSON)  # 存储阶段配置
    error_handling = Column(JSON)  # 存储错误处理配置
    output_aggregation = Column(JSON)  # 存储输出聚合配置
    created_by = Column(String(100))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    
    # 关系
    workflow_executions = relationship("WorkflowExecution", back_populates="workflow")

class Agent(Base):
    """Agent表"""
    __tablename__ = "agents"
    
    id = Column(Integer, primary_key=True, index=True)
    agent_id = Column(String(100), unique=True, index=True, nullable=False)
    name = Column(String(200), nullable=False)
    status = Column(SQLEnum(AgentStatus), default=AgentStatus.OFFLINE)
    capabilities = Column(JSON)  # 存储能力配置
    resource_limits = Column(JSON)  # 存储资源限制
    current_load = Column(Integer, default=0)
    max_capacity = Column(Integer, default=10)
    last_heartbeat = Column(DateTime)
    location = Column(String(100))
    tags = Column(JSON)
    performance_history = Column(JSON)  # 存储性能历史数据
    api_key = Column(String(255))  # Agent API密钥
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    
    # 关系
    task_executions = relationship("TaskExecution", back_populates="agent")

class TaskExecution(Base):
    """任务执行表"""
    __tablename__ = "task_executions"
    
    id = Column(Integer, primary_key=True, index=True)
    execution_id = Column(String(100), unique=True, index=True, nullable=False)
    template_id = Column(String(100), ForeignKey("task_templates.template_id"), nullable=False)
    agent_id = Column(String(100), ForeignKey("agents.agent_id"))
    workflow_execution_id = Column(String(100), ForeignKey("workflow_executions.execution_id"))
    
    status = Column(SQLEnum(TaskStatus), default=TaskStatus.PENDING)
    parameters = Column(JSON)  # 存储执行参数
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    progress = Column(Integer, default=0)  # 进度百分比
    output = Column(JSON)  # 存储执行结果
    error = Column(Text)  # 存储错误信息
    logs = Column(JSON)  # 存储执行日志
    metadata = Column(JSON)  # 存储元数据
    
    priority = Column(Integer, default=2)  # 优先级 1=高 2=中 3=低
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
    timeout = Column(Integer)  # 超时时间(秒)
    
    created_by = Column(String(100))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    template = relationship("TaskTemplate", back_populates="task_executions")
    agent = relationship("Agent", back_populates="task_executions")
    workflow_execution = relationship("WorkflowExecution", back_populates="task_executions")

class WorkflowExecution(Base):
    """工作流执行表"""
    __tablename__ = "workflow_executions"
    
    id = Column(Integer, primary_key=True, index=True)
    execution_id = Column(String(100), unique=True, index=True, nullable=False)
    workflow_id = Column(String(100), ForeignKey("workflows.workflow_id"), nullable=False)
    
    status = Column(SQLEnum(WorkflowStatus), default=WorkflowStatus.PENDING)
    parameters = Column(JSON)  # 存储执行参数
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    current_stage = Column(String(100))  # 当前执行阶段
    progress = Column(Integer, default=0)  # 总体进度百分比
    
    context = Column(JSON)  # 存储执行上下文
    stage_status = Column(JSON)  # 存储各阶段状态
    output = Column(JSON)  # 存储最终输出
    error = Column(Text)  # 存储错误信息
    logs = Column(JSON)  # 存储执行日志
    
    created_by = Column(String(100))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    workflow = relationship("Workflow", back_populates="workflow_executions")
    task_executions = relationship("TaskExecution", back_populates="workflow_execution")

class TaskQueue(Base):
    """任务队列表"""
    __tablename__ = "task_queue"
    
    id = Column(Integer, primary_key=True, index=True)
    task_execution_id = Column(String(100), ForeignKey("task_executions.execution_id"), nullable=False)
    priority = Column(Integer, default=2)
    required_capabilities = Column(JSON)  # 所需能力
    estimated_duration = Column(Integer)  # 预估执行时间
    queued_at = Column(DateTime, default=datetime.utcnow)
    assigned_at = Column(DateTime)
    assigned_agent_id = Column(String(100), ForeignKey("agents.agent_id"))
    
    # 关系
    task_execution = relationship("TaskExecution")
    assigned_agent = relationship("Agent")

class AgentHeartbeat(Base):
    """Agent心跳记录表"""
    __tablename__ = "agent_heartbeats"
    
    id = Column(Integer, primary_key=True, index=True)
    agent_id = Column(String(100), ForeignKey("agents.agent_id"), nullable=False)
    timestamp = Column(DateTime, default=datetime.utcnow)
    status = Column(SQLEnum(AgentStatus))
    current_load = Column(Integer)
    running_tasks = Column(JSON)  # 当前运行的任务列表
    system_info = Column(JSON)  # 系统信息(CPU、内存等)
    
    # 关系
    agent = relationship("Agent")

class TaskLog(Base):
    """任务日志表"""
    __tablename__ = "task_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    task_execution_id = Column(String(100), ForeignKey("task_executions.execution_id"), nullable=False)
    level = Column(String(20))  # INFO, WARNING, ERROR, DEBUG
    message = Column(Text)
    timestamp = Column(DateTime, default=datetime.utcnow)
    source = Column(String(100))  # 日志来源(scheduler, agent, etc.)
    
    # 关系
    task_execution = relationship("TaskExecution")

# 创建所有表的函数
def create_tables(engine):
    """创建所有表"""
    Base.metadata.create_all(bind=engine)

# 生成UUID的辅助函数
def generate_uuid():
    """生成UUID"""
    return str(uuid.uuid4())

# 模型验证函数
def validate_task_template(template_data: dict) -> bool:
    """验证任务模板数据"""
    required_fields = ['template_id', 'name', 'version', 'category', 'tools']
    return all(field in template_data for field in required_fields)

def validate_workflow(workflow_data: dict) -> bool:
    """验证工作流数据"""
    required_fields = ['workflow_id', 'name', 'version', 'stages']
    return all(field in workflow_data for field in required_fields)

def validate_agent_registration(agent_data: dict) -> bool:
    """验证Agent注册数据"""
    required_fields = ['agent_id', 'name', 'capabilities']
    return all(field in agent_data for field in required_fields)
