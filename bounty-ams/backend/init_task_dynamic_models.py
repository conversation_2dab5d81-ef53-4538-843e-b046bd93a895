#!/usr/bin/env python3
"""
使用动态模型初始化任务管理系统
"""

import asyncio
import sys
import os
from datetime import datetime
from uuid import uuid4

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import AsyncSessionLocal
from models_dynamic import ModelType, ModelField, DynamicEntity
from sqlalchemy import select

async def create_task_template_model():
    """创建任务模板动态模型"""
    async with AsyncSessionLocal() as db:
        try:
            # 检查是否已存在
            existing_query = select(ModelType).where(ModelType.name == "task_template")
            result = await db.execute(existing_query)
            existing = result.scalar_one_or_none()
            
            if existing:
                print("✅ 任务模板模型已存在")
                return existing
            
            # 创建任务模板模型类型
            task_template_model = ModelType(
                name="task_template",
                display_name="任务模板",
                description="用于定义可重复执行的任务模板",
                icon="thunderbolt",
                color="#1890ff",
                is_active=True,
                is_system=True
            )
            
            db.add(task_template_model)
            await db.flush()  # 获取ID
            
            # 定义字段
            fields = [
                {
                    "field_name": "template_id",
                    "field_type": "text",
                    "display_name": "模板ID",
                    "description": "模板的唯一标识符",
                    "is_required": True,
                    "is_unique": True,
                    "is_searchable": True,
                    "sort_order": 1
                },
                {
                    "field_name": "name",
                    "field_type": "text",
                    "display_name": "模板名称",
                    "description": "模板的显示名称",
                    "is_required": True,
                    "is_searchable": True,
                    "sort_order": 2
                },
                {
                    "field_name": "version",
                    "field_type": "text",
                    "display_name": "版本",
                    "description": "模板版本号",
                    "is_required": True,
                    "default_value": "1.0.0",
                    "sort_order": 3
                },
                {
                    "field_name": "description",
                    "field_type": "textarea",
                    "display_name": "描述",
                    "description": "模板的详细描述",
                    "is_required": False,
                    "is_searchable": True,
                    "sort_order": 4
                },
                {
                    "field_name": "category",
                    "field_type": "select",
                    "display_name": "分类",
                    "description": "模板分类",
                    "is_required": True,
                    "is_filterable": True,
                    "field_options": {
                        "options": [
                            {"value": "reconnaissance", "label": "侦察"},
                            {"value": "scanning", "label": "扫描"},
                            {"value": "identification", "label": "识别"},
                            {"value": "vulnerability", "label": "漏洞"},
                            {"value": "exploitation", "label": "利用"},
                            {"value": "reporting", "label": "报告"}
                        ]
                    },
                    "sort_order": 5
                },
                {
                    "field_name": "tags",
                    "field_type": "json",
                    "display_name": "标签",
                    "description": "模板标签列表",
                    "is_required": False,
                    "is_filterable": True,
                    "sort_order": 6
                },
                {
                    "field_name": "parameters",
                    "field_type": "json",
                    "display_name": "参数配置",
                    "description": "模板参数的JSON Schema定义",
                    "is_required": True,
                    "sort_order": 7
                },
                {
                    "field_name": "tools",
                    "field_type": "json",
                    "display_name": "工具配置",
                    "description": "使用的工具及其配置",
                    "is_required": True,
                    "sort_order": 8
                },
                {
                    "field_name": "execution",
                    "field_type": "json",
                    "display_name": "执行配置",
                    "description": "执行策略和配置",
                    "is_required": True,
                    "sort_order": 9
                },
                {
                    "field_name": "output_schema",
                    "field_type": "json",
                    "display_name": "输出格式",
                    "description": "输出数据的JSON Schema",
                    "is_required": False,
                    "sort_order": 10
                },
                {
                    "field_name": "is_active",
                    "field_type": "boolean",
                    "display_name": "是否启用",
                    "description": "模板是否可用",
                    "is_required": True,
                    "default_value": "true",
                    "is_filterable": True,
                    "sort_order": 11
                }
            ]
            
            # 创建字段
            for i, field_data in enumerate(fields):
                field = ModelField(
                    model_type_id=task_template_model.id,
                    **field_data
                )
                db.add(field)
            
            await db.commit()
            print("✅ 任务模板动态模型创建成功")
            return task_template_model
            
        except Exception as e:
            await db.rollback()
            print(f"❌ 创建任务模板模型失败: {e}")
            raise

async def create_workflow_model():
    """创建工作流动态模型"""
    async with AsyncSessionLocal() as db:
        try:
            # 检查是否已存在
            existing_query = select(ModelType).where(ModelType.name == "workflow")
            result = await db.execute(existing_query)
            existing = result.scalar_one_or_none()
            
            if existing:
                print("✅ 工作流模型已存在")
                return existing
            
            # 创建工作流模型类型
            workflow_model = ModelType(
                name="workflow",
                display_name="工作流",
                description="用于定义复杂的多阶段任务流程",
                icon="branches",
                color="#52c41a",
                is_active=True,
                is_system=True
            )
            
            db.add(workflow_model)
            await db.flush()
            
            # 定义字段
            fields = [
                {
                    "field_name": "workflow_id",
                    "field_type": "text",
                    "display_name": "工作流ID",
                    "description": "工作流的唯一标识符",
                    "is_required": True,
                    "is_unique": True,
                    "is_searchable": True,
                    "sort_order": 1
                },
                {
                    "field_name": "name",
                    "field_type": "text",
                    "display_name": "工作流名称",
                    "description": "工作流的显示名称",
                    "is_required": True,
                    "is_searchable": True,
                    "sort_order": 2
                },
                {
                    "field_name": "version",
                    "field_type": "text",
                    "display_name": "版本",
                    "description": "工作流版本号",
                    "is_required": True,
                    "default_value": "1.0.0",
                    "sort_order": 3
                },
                {
                    "field_name": "description",
                    "field_type": "textarea",
                    "display_name": "描述",
                    "description": "工作流的详细描述",
                    "is_required": False,
                    "is_searchable": True,
                    "sort_order": 4
                },
                {
                    "field_name": "parameters",
                    "field_type": "json",
                    "display_name": "参数配置",
                    "description": "工作流参数的JSON Schema定义",
                    "is_required": True,
                    "sort_order": 5
                },
                {
                    "field_name": "stages",
                    "field_type": "json",
                    "display_name": "阶段配置",
                    "description": "工作流各阶段的配置",
                    "is_required": True,
                    "sort_order": 6
                },
                {
                    "field_name": "error_handling",
                    "field_type": "json",
                    "display_name": "错误处理",
                    "description": "错误处理策略配置",
                    "is_required": False,
                    "sort_order": 7
                },
                {
                    "field_name": "output_aggregation",
                    "field_type": "json",
                    "display_name": "输出聚合",
                    "description": "输出聚合策略配置",
                    "is_required": False,
                    "sort_order": 8
                },
                {
                    "field_name": "is_active",
                    "field_type": "boolean",
                    "display_name": "是否启用",
                    "description": "工作流是否可用",
                    "is_required": True,
                    "default_value": "true",
                    "is_filterable": True,
                    "sort_order": 9
                }
            ]
            
            # 创建字段
            for field_data in fields:
                field = ModelField(
                    model_type_id=workflow_model.id,
                    **field_data
                )
                db.add(field)
            
            await db.commit()
            print("✅ 工作流动态模型创建成功")
            return workflow_model
            
        except Exception as e:
            await db.rollback()
            print(f"❌ 创建工作流模型失败: {e}")
            raise

async def create_task_execution_model():
    """创建任务执行动态模型"""
    async with AsyncSessionLocal() as db:
        try:
            # 检查是否已存在
            existing_query = select(ModelType).where(ModelType.name == "task_execution")
            result = await db.execute(existing_query)
            existing = result.scalar_one_or_none()
            
            if existing:
                print("✅ 任务执行模型已存在")
                return existing
            
            # 创建任务执行模型类型
            task_execution_model = ModelType(
                name="task_execution",
                display_name="任务执行",
                description="任务执行记录和状态跟踪",
                icon="play-circle",
                color="#faad14",
                is_active=True,
                is_system=True
            )
            
            db.add(task_execution_model)
            await db.flush()
            
            # 定义字段
            fields = [
                {
                    "field_name": "execution_id",
                    "field_type": "text",
                    "display_name": "执行ID",
                    "description": "任务执行的唯一标识符",
                    "is_required": True,
                    "is_unique": True,
                    "is_searchable": True,
                    "sort_order": 1
                },
                {
                    "field_name": "template_id",
                    "field_type": "text",
                    "display_name": "模板ID",
                    "description": "使用的任务模板ID",
                    "is_required": True,
                    "is_searchable": True,
                    "is_filterable": True,
                    "sort_order": 2
                },
                {
                    "field_name": "template_name",
                    "field_type": "text",
                    "display_name": "模板名称",
                    "description": "任务模板名称",
                    "is_required": False,
                    "is_searchable": True,
                    "sort_order": 3
                },
                {
                    "field_name": "status",
                    "field_type": "select",
                    "display_name": "状态",
                    "description": "任务执行状态",
                    "is_required": True,
                    "is_filterable": True,
                    "field_options": {
                        "options": [
                            {"value": "pending", "label": "等待中"},
                            {"value": "running", "label": "执行中"},
                            {"value": "success", "label": "成功"},
                            {"value": "failed", "label": "失败"},
                            {"value": "cancelled", "label": "已取消"},
                            {"value": "timeout", "label": "超时"}
                        ]
                    },
                    "default_value": "pending",
                    "sort_order": 4
                },
                {
                    "field_name": "agent_id",
                    "field_type": "text",
                    "display_name": "Agent ID",
                    "description": "执行任务的Agent ID",
                    "is_required": False,
                    "is_filterable": True,
                    "sort_order": 5
                },
                {
                    "field_name": "agent_name",
                    "field_type": "text",
                    "display_name": "Agent名称",
                    "description": "执行任务的Agent名称",
                    "is_required": False,
                    "is_searchable": True,
                    "sort_order": 6
                },
                {
                    "field_name": "parameters",
                    "field_type": "json",
                    "display_name": "执行参数",
                    "description": "任务执行时的参数",
                    "is_required": True,
                    "sort_order": 7
                },
                {
                    "field_name": "start_time",
                    "field_type": "datetime",
                    "display_name": "开始时间",
                    "description": "任务开始执行时间",
                    "is_required": False,
                    "is_filterable": True,
                    "sort_order": 8
                },
                {
                    "field_name": "end_time",
                    "field_type": "datetime",
                    "display_name": "结束时间",
                    "description": "任务结束时间",
                    "is_required": False,
                    "is_filterable": True,
                    "sort_order": 9
                },
                {
                    "field_name": "duration",
                    "field_type": "number",
                    "display_name": "执行时长",
                    "description": "任务执行时长(秒)",
                    "is_required": False,
                    "sort_order": 10
                },
                {
                    "field_name": "progress",
                    "field_type": "number",
                    "display_name": "进度",
                    "description": "任务执行进度(0-100)",
                    "is_required": False,
                    "default_value": "0",
                    "validation_rules": {"min": 0, "max": 100},
                    "sort_order": 11
                },
                {
                    "field_name": "output",
                    "field_type": "json",
                    "display_name": "执行结果",
                    "description": "任务执行的输出结果",
                    "is_required": False,
                    "sort_order": 12
                },
                {
                    "field_name": "error",
                    "field_type": "textarea",
                    "display_name": "错误信息",
                    "description": "任务执行失败时的错误信息",
                    "is_required": False,
                    "sort_order": 13
                },
                {
                    "field_name": "logs",
                    "field_type": "json",
                    "display_name": "执行日志",
                    "description": "任务执行过程中的日志",
                    "is_required": False,
                    "sort_order": 14
                },
                {
                    "field_name": "priority",
                    "field_type": "select",
                    "display_name": "优先级",
                    "description": "任务优先级",
                    "is_required": True,
                    "is_filterable": True,
                    "field_options": {
                        "options": [
                            {"value": "1", "label": "高"},
                            {"value": "2", "label": "中"},
                            {"value": "3", "label": "低"}
                        ]
                    },
                    "default_value": "2",
                    "sort_order": 15
                }
            ]
            
            # 创建字段
            for field_data in fields:
                field = ModelField(
                    model_type_id=task_execution_model.id,
                    **field_data
                )
                db.add(field)
            
            await db.commit()
            print("✅ 任务执行动态模型创建成功")
            return task_execution_model
            
        except Exception as e:
            await db.rollback()
            print(f"❌ 创建任务执行模型失败: {e}")
            raise

async def create_sample_task_templates():
    """创建示例任务模板"""
    async with AsyncSessionLocal() as db:
        try:
            # 获取任务模板模型类型
            model_query = select(ModelType).where(ModelType.name == "task_template")
            result = await db.execute(model_query)
            template_model = result.scalar_one()
            
            # 子域名发现模板
            subdomain_template_data = {
                "template_id": "subdomain_discovery_v1",
                "name": "子域名发现",
                "version": "1.0.0",
                "description": "使用多种工具进行子域名发现，包括subfinder、assetfinder等",
                "category": "reconnaissance",
                "tags": ["subdomain", "discovery", "passive", "reconnaissance"],
                "parameters": {
                    "target": {
                        "type": "string",
                        "required": True,
                        "description": "目标域名",
                        "pattern": "^[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
                    },
                    "wordlist": {
                        "type": "string",
                        "required": False,
                        "default": "common.txt",
                        "description": "字典文件路径"
                    },
                    "timeout": {
                        "type": "integer",
                        "required": False,
                        "default": 300,
                        "minimum": 60,
                        "maximum": 3600,
                        "description": "超时时间(秒)"
                    }
                },
                "tools": [
                    {
                        "tool_name": "subfinder",
                        "version": ">=2.6.0",
                        "command": "subfinder",
                        "args": ["-d", "{{target}}", "-o", "{{output_file}}", "-timeout", "{{timeout}}", "-silent"],
                        "env": {"SUBFINDER_CONFIG": "/config/subfinder.yaml"},
                        "output_format": "text",
                        "output_parser": "line_split",
                        "timeout": 300,
                        "retry_count": 3
                    },
                    {
                        "tool_name": "assetfinder",
                        "version": ">=0.1.0",
                        "command": "assetfinder",
                        "args": ["--subs-only", "{{target}}"],
                        "output_format": "text",
                        "output_parser": "line_split",
                        "timeout": 300,
                        "retry_count": 3
                    }
                ],
                "execution": {
                    "strategy": "parallel",
                    "merge_results": True,
                    "deduplication": True,
                    "timeout": "{{timeout}}",
                    "retry": {
                        "max_attempts": 3,
                        "backoff": "exponential"
                    }
                },
                "output_schema": {
                    "type": "object",
                    "properties": {
                        "subdomains": {
                            "type": "array",
                            "items": {"type": "string"}
                        },
                        "metadata": {
                            "type": "object",
                            "properties": {
                                "total_found": {"type": "integer"},
                                "tools_used": {"type": "array"},
                                "execution_time": {"type": "number"}
                            }
                        }
                    }
                },
                "is_active": True
            }
            
            # 端口扫描模板
            port_scan_template_data = {
                "template_id": "port_scan_v1",
                "name": "端口扫描",
                "version": "1.0.0",
                "description": "使用nmap进行端口扫描，支持多种扫描类型",
                "category": "scanning",
                "tags": ["port", "scan", "nmap", "network"],
                "parameters": {
                    "targets": {
                        "type": "array",
                        "items": {"type": "string"},
                        "required": True,
                        "description": "目标列表"
                    },
                    "ports": {
                        "type": "string",
                        "required": False,
                        "default": "common",
                        "enum": ["common", "full", "top1000"],
                        "description": "端口范围"
                    },
                    "scan_type": {
                        "type": "string",
                        "required": False,
                        "default": "syn",
                        "enum": ["syn", "tcp", "udp"],
                        "description": "扫描类型"
                    }
                },
                "tools": [
                    {
                        "tool_name": "nmap",
                        "version": ">=7.80",
                        "command": "nmap",
                        "args": ["-sS", "-p", "{{port_range}}", "-oX", "{{output_file}}", "{{targets_file}}"],
                        "output_format": "xml",
                        "output_parser": "nmap_xml",
                        "timeout": 1800,
                        "retry_count": 2
                    }
                ],
                "execution": {
                    "strategy": "sequential",
                    "timeout": 1800,
                    "retry": {
                        "max_attempts": 2,
                        "backoff": "linear"
                    }
                },
                "output_schema": {
                    "type": "object",
                    "properties": {
                        "open_ports": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "host": {"type": "string"},
                                    "port": {"type": "integer"},
                                    "protocol": {"type": "string"},
                                    "service": {"type": "string"},
                                    "state": {"type": "string"}
                                }
                            }
                        },
                        "web_services": {
                            "type": "array",
                            "items": {"type": "string"}
                        }
                    }
                },
                "is_active": True
            }
            
            # Nuclei扫描模板
            nuclei_template_data = {
                "template_id": "nuclei_scan_v1",
                "name": "Nuclei漏洞扫描",
                "version": "1.0.0",
                "description": "使用Nuclei进行漏洞扫描，支持多种严重程度筛选",
                "category": "vulnerability",
                "tags": ["vulnerability", "nuclei", "scan", "security"],
                "parameters": {
                    "targets": {
                        "type": "array",
                        "items": {"type": "string"},
                        "required": True,
                        "description": "目标URL列表"
                    },
                    "severity": {
                        "type": "string",
                        "required": False,
                        "default": "medium,high,critical",
                        "description": "漏洞严重程度"
                    },
                    "templates": {
                        "type": "string",
                        "required": False,
                        "default": "all",
                        "description": "模板路径"
                    }
                },
                "tools": [
                    {
                        "tool_name": "nuclei",
                        "version": ">=2.9.0",
                        "command": "nuclei",
                        "args": ["-l", "{{targets_file}}", "-s", "{{severity}}", "-o", "{{output_file}}", "-json"],
                        "output_format": "json",
                        "output_parser": "nuclei_json",
                        "timeout": 3600,
                        "retry_count": 1
                    }
                ],
                "execution": {
                    "strategy": "sequential",
                    "timeout": 3600,
                    "retry": {
                        "max_attempts": 1,
                        "backoff": "none"
                    }
                },
                "output_schema": {
                    "type": "object",
                    "properties": {
                        "vulnerabilities": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "template_id": {"type": "string"},
                                    "info": {"type": "object"},
                                    "matched_at": {"type": "string"},
                                    "severity": {"type": "string"}
                                }
                            }
                        }
                    }
                },
                "is_active": True
            }
            
            # 创建模板实体
            templates = [subdomain_template_data, port_scan_template_data, nuclei_template_data]
            
            for template_data in templates:
                # 检查是否已存在
                existing_query = select(DynamicEntity).where(
                    DynamicEntity.model_type_id == template_model.id,
                    DynamicEntity.entity_data.op('->>')('template_id') == template_data['template_id']
                )
                result = await db.execute(existing_query)
                existing = result.scalar_one_or_none()
                
                if not existing:
                    entity = DynamicEntity(
                        model_type_id=template_model.id,
                        entity_data=template_data
                    )
                    db.add(entity)
            
            await db.commit()
            print("✅ 示例任务模板创建成功")
            
        except Exception as e:
            await db.rollback()
            print(f"❌ 创建示例任务模板失败: {e}")
            raise

async def main():
    """主函数"""
    print("🚀 开始初始化基于动态模型的任务管理系统...")
    
    try:
        # 1. 创建动态模型
        await create_task_template_model()
        await create_workflow_model()
        await create_task_execution_model()
        
        # 2. 创建示例数据
        await create_sample_task_templates()
        
        print("🎉 基于动态模型的任务管理系统初始化完成!")
        print("\n📋 系统信息:")
        print("- 任务模板模型: ✅ 已创建")
        print("- 工作流模型: ✅ 已创建") 
        print("- 任务执行模型: ✅ 已创建")
        print("- 示例模板: 3个 (子域名发现、端口扫描、Nuclei扫描)")
        print("\n🔗 接下来可以:")
        print("1. 通过动态模型API管理任务模板")
        print("2. 使用前端界面创建和编辑模板")
        print("3. 执行任务并监控状态")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
