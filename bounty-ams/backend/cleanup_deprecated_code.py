#!/usr/bin/env python3
"""
清理废弃代码文件
移除unified_assets相关的旧代码
"""

import os
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def cleanup_deprecated_files():
    """清理废弃的文件"""
    
    # 要删除的文件列表
    files_to_remove = [
        "./routes/unified_assets.py",
        "./migrate_unified_assets.py", 
        "./test_unified_assets_api.py",
        "./routes/__pycache__/unified_assets.cpython-310.pyc",
        "./migrate_asset_relationships.py",  # 旧的迁移脚本
        "./fix_dynamic_model_relations.py",  # 旧的修复脚本
    ]
    
    # 可选删除（如果确认不需要）
    optional_files = [
        "./create_realistic_assets.py",
        "./create_test_assets.py",
    ]
    
    removed_count = 0
    
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                logger.info(f"✅ 删除文件: {file_path}")
                removed_count += 1
            except Exception as e:
                logger.error(f"❌ 删除文件失败: {file_path} - {e}")
        else:
            logger.debug(f"文件不存在: {file_path}")
    
    logger.info(f"📁 清理完成，共删除 {removed_count} 个文件")
    
    # 检查可选文件
    logger.info("\n🔍 检查可选文件（需要手动确认是否删除）：")
    for file_path in optional_files:
        if os.path.exists(file_path):
            logger.info(f"  • {file_path}")
    
    return removed_count

def cleanup_pycache():
    """清理Python缓存文件"""
    logger.info("🧹 清理Python缓存文件...")
    
    cache_dirs = []
    for root, dirs, files in os.walk("."):
        for dir_name in dirs:
            if dir_name == "__pycache__":
                cache_dirs.append(os.path.join(root, dir_name))
    
    removed_dirs = 0
    for cache_dir in cache_dirs:
        try:
            import shutil
            shutil.rmtree(cache_dir)
            logger.info(f"✅ 删除缓存目录: {cache_dir}")
            removed_dirs += 1
        except Exception as e:
            logger.error(f"❌ 删除缓存目录失败: {cache_dir} - {e}")
    
    logger.info(f"🗑️ 清理缓存完成，共删除 {removed_dirs} 个目录")
    
    return removed_dirs

def main():
    """主函数"""
    logger.info("🚀 开始清理废弃代码...")
    
    try:
        removed_files = cleanup_deprecated_files()
        removed_cache = cleanup_pycache()
        
        logger.info("\n🎉 清理完成！")
        logger.info(f"  • 删除文件: {removed_files}")
        logger.info(f"  • 删除缓存: {removed_cache}")
        
        logger.info("\n📋 建议后续操作：")
        logger.info("  1. 检查代码中是否还有unified_assets的引用")
        logger.info("  2. 运行测试确保系统正常工作")
        logger.info("  3. 提交代码变更")
        
    except Exception as e:
        logger.error(f"❌ 清理过程中发生错误: {e}")
        raise

if __name__ == "__main__":
    main()