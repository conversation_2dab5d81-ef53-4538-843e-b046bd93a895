#!/usr/bin/env python3
"""
检查平台和项目数据结构
"""

import asyncio
from sqlalchemy import select
from database import AsyncSessionLocal
from models_dynamic import ModelType, DynamicEntity
import json

async def check_platform_project_data():
    """检查平台和项目数据"""
    async with AsyncSessionLocal() as db:
        # 检查平台数据
        platform_query = select(ModelType).where(ModelType.name == 'platform')
        platform_result = await db.execute(platform_query)
        platform_model = platform_result.scalar_one_or_none()
        
        if platform_model:
            print(f"✅ Platform model found: {platform_model.id}")
            
            # 获取平台实体
            platform_entities_query = select(DynamicEntity).where(DynamicEntity.model_type_id == platform_model.id)
            platform_entities_result = await db.execute(platform_entities_query)
            platform_entities = platform_entities_result.scalars().all()
            
            print(f"Found {len(platform_entities)} platforms:")
            for entity in platform_entities:
                print(f"  Platform ID: {entity.id}")
                print(f"  Data: {json.dumps(entity.entity_data, indent=2, ensure_ascii=False)}")
                print()
        
        # 检查项目数据
        project_query = select(ModelType).where(ModelType.name == 'project')
        project_result = await db.execute(project_query)
        project_model = project_result.scalar_one_or_none()
        
        if project_model:
            print(f"✅ Project model found: {project_model.id}")
            
            # 获取项目实体
            project_entities_query = select(DynamicEntity).where(DynamicEntity.model_type_id == project_model.id)
            project_entities_result = await db.execute(project_entities_query)
            project_entities = project_entities_result.scalars().all()
            
            print(f"Found {len(project_entities)} projects:")
            for entity in project_entities:
                print(f"  Project ID: {entity.id}")
                print(f"  Data: {json.dumps(entity.entity_data, indent=2, ensure_ascii=False)}")
                print()

if __name__ == "__main__":
    asyncio.run(check_platform_project_data())