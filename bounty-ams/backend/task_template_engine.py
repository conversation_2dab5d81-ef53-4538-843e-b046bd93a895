"""
任务模板引擎 - 负责任务模板的管理、渲染和执行
"""

import json
import jsonschema
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
from jinja2 import Environment, DictLoader, StrictUndefined
import asyncio
from datetime import datetime, timedelta

class TaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"

class ExecutionStrategy(Enum):
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    CONDITIONAL = "conditional"

@dataclass
class ToolConfig:
    """工具配置"""
    tool_name: str
    version: str
    command: str
    args: List[str]
    env: Dict[str, str] = field(default_factory=dict)
    output_format: str = "json"
    output_parser: str = "json"
    timeout: int = 300
    retry_count: int = 3

@dataclass
class TaskTemplate:
    """任务模板"""
    template_id: str
    name: str
    version: str
    description: str
    category: str
    tags: List[str]
    parameters: Dict[str, Any]
    tools: List[ToolConfig]
    execution: Dict[str, Any]
    output_schema: Dict[str, Any]
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)

@dataclass
class TaskExecution:
    """任务执行实例"""
    execution_id: str
    template_id: str
    parameters: Dict[str, Any]
    status: TaskStatus = TaskStatus.PENDING
    agent_id: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    output: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    logs: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

class TaskTemplateEngine:
    """任务模板引擎"""
    
    def __init__(self):
        self.templates: Dict[str, TaskTemplate] = {}
        self.jinja_env = Environment(
            loader=DictLoader({}),
            undefined=StrictUndefined
        )
        self.load_builtin_templates()
    
    def load_builtin_templates(self):
        """加载内置模板"""
        # 子域名发现模板
        subdomain_template = TaskTemplate(
            template_id="subdomain_discovery_v1",
            name="子域名发现",
            version="1.0.0",
            description="使用多种工具进行子域名发现",
            category="reconnaissance",
            tags=["subdomain", "discovery", "passive"],
            parameters={
                "target": {
                    "type": "string",
                    "required": True,
                    "description": "目标域名",
                    "pattern": r"^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
                },
                "wordlist": {
                    "type": "string",
                    "required": False,
                    "default": "common.txt",
                    "description": "字典文件路径"
                },
                "timeout": {
                    "type": "integer",
                    "required": False,
                    "default": 300,
                    "minimum": 60,
                    "maximum": 3600,
                    "description": "超时时间(秒)"
                }
            },
            tools=[
                ToolConfig(
                    tool_name="subfinder",
                    version=">=2.6.0",
                    command="subfinder",
                    args=["-d", "{{target}}", "-o", "{{output_file}}", "-timeout", "{{timeout}}", "-silent"],
                    env={"SUBFINDER_CONFIG": "/config/subfinder.yaml"},
                    output_format="text",
                    output_parser="line_split"
                ),
                ToolConfig(
                    tool_name="assetfinder",
                    version=">=0.1.0",
                    command="assetfinder",
                    args=["--subs-only", "{{target}}"],
                    output_format="text",
                    output_parser="line_split"
                )
            ],
            execution={
                "strategy": "parallel",
                "merge_results": True,
                "deduplication": True,
                "timeout": "{{timeout}}",
                "retry": {
                    "max_attempts": 3,
                    "backoff": "exponential"
                }
            },
            output_schema={
                "type": "object",
                "properties": {
                    "subdomains": {
                        "type": "array",
                        "items": {"type": "string"}
                    },
                    "metadata": {
                        "type": "object",
                        "properties": {
                            "total_found": {"type": "integer"},
                            "tools_used": {"type": "array"},
                            "execution_time": {"type": "number"}
                        }
                    }
                }
            }
        )
        
        # 端口扫描模板
        port_scan_template = TaskTemplate(
            template_id="port_scan_v1",
            name="端口扫描",
            version="1.0.0",
            description="使用nmap进行端口扫描",
            category="scanning",
            tags=["port", "scan", "nmap"],
            parameters={
                "targets": {
                    "type": "array",
                    "items": {"type": "string"},
                    "required": True,
                    "description": "目标列表"
                },
                "ports": {
                    "type": "string",
                    "required": False,
                    "default": "common",
                    "enum": ["common", "full", "top1000"],
                    "description": "端口范围"
                },
                "scan_type": {
                    "type": "string",
                    "required": False,
                    "default": "syn",
                    "enum": ["syn", "tcp", "udp"],
                    "description": "扫描类型"
                }
            },
            tools=[
                ToolConfig(
                    tool_name="nmap",
                    version=">=7.80",
                    command="nmap",
                    args=[
                        "-sS" if "{{scan_type}}" == "syn" else "-sT",
                        "-p", "{{port_range}}",
                        "-oX", "{{output_file}}",
                        "{{targets_file}}"
                    ],
                    output_format="xml",
                    output_parser="nmap_xml"
                )
            ],
            execution={
                "strategy": "sequential",
                "timeout": 1800,  # 30分钟
                "retry": {
                    "max_attempts": 2,
                    "backoff": "linear"
                }
            },
            output_schema={
                "type": "object",
                "properties": {
                    "open_ports": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "host": {"type": "string"},
                                "port": {"type": "integer"},
                                "protocol": {"type": "string"},
                                "service": {"type": "string"},
                                "state": {"type": "string"}
                            }
                        }
                    },
                    "web_services": {
                        "type": "array",
                        "items": {"type": "string"}
                    }
                }
            }
        )
        
        self.templates[subdomain_template.template_id] = subdomain_template
        self.templates[port_scan_template.template_id] = port_scan_template
    
    def register_template(self, template: TaskTemplate):
        """注册新模板"""
        self.validate_template(template)
        self.templates[template.template_id] = template
    
    def validate_template(self, template: TaskTemplate):
        """验证模板格式"""
        # 验证参数schema
        try:
            jsonschema.Draft7Validator.check_schema(template.parameters)
        except jsonschema.SchemaError as e:
            raise ValueError(f"模板参数schema无效: {e}")
        
        # 验证输出schema
        try:
            jsonschema.Draft7Validator.check_schema(template.output_schema)
        except jsonschema.SchemaError as e:
            raise ValueError(f"模板输出schema无效: {e}")
        
        # 验证工具配置
        for tool in template.tools:
            if not tool.tool_name or not tool.command:
                raise ValueError(f"工具配置无效: {tool}")
    
    def validate_parameters(self, template: TaskTemplate, parameters: Dict[str, Any]):
        """验证任务参数"""
        validator = jsonschema.Draft7Validator(template.parameters)
        errors = list(validator.iter_errors(parameters))
        if errors:
            error_messages = [f"{error.json_path}: {error.message}" for error in errors]
            raise ValueError(f"参数验证失败: {'; '.join(error_messages)}")
    
    def render_template(self, template_id: str, parameters: Dict[str, Any]) -> TaskExecution:
        """渲染任务模板"""
        template = self.templates.get(template_id)
        if not template:
            raise ValueError(f"模板不存在: {template_id}")
        
        # 验证参数
        self.validate_parameters(template, parameters)
        
        # 生成执行ID
        execution_id = f"{template_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')}"
        
        # 创建任务执行实例
        execution = TaskExecution(
            execution_id=execution_id,
            template_id=template_id,
            parameters=parameters,
            metadata={
                "template_name": template.name,
                "template_version": template.version,
                "rendered_at": datetime.utcnow().isoformat()
            }
        )
        
        return execution
    
    def render_tool_config(self, tool: ToolConfig, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """渲染工具配置"""
        # 创建渲染上下文
        context = {
            **parameters,
            "output_file": f"/tmp/{tool.tool_name}_output_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            "targets_file": f"/tmp/targets_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.txt"
        }
        
        # 渲染命令参数
        rendered_args = []
        for arg in tool.args:
            if "{{" in arg and "}}" in arg:
                template = self.jinja_env.from_string(arg)
                rendered_arg = template.render(**context)
                rendered_args.append(rendered_arg)
            else:
                rendered_args.append(arg)
        
        # 渲染环境变量
        rendered_env = {}
        for key, value in tool.env.items():
            if "{{" in value and "}}" in value:
                template = self.jinja_env.from_string(value)
                rendered_value = template.render(**context)
                rendered_env[key] = rendered_value
            else:
                rendered_env[key] = value
        
        return {
            "tool_name": tool.tool_name,
            "command": tool.command,
            "args": rendered_args,
            "env": rendered_env,
            "output_format": tool.output_format,
            "output_parser": tool.output_parser,
            "timeout": tool.timeout,
            "retry_count": tool.retry_count
        }
    
    def get_template(self, template_id: str) -> Optional[TaskTemplate]:
        """获取模板"""
        return self.templates.get(template_id)
    
    def list_templates(self, category: Optional[str] = None, tags: Optional[List[str]] = None) -> List[TaskTemplate]:
        """列出模板"""
        templates = list(self.templates.values())
        
        if category:
            templates = [t for t in templates if t.category == category]
        
        if tags:
            templates = [t for t in templates if any(tag in t.tags for tag in tags)]
        
        return templates
    
    def delete_template(self, template_id: str) -> bool:
        """删除模板"""
        if template_id in self.templates:
            del self.templates[template_id]
            return True
        return False

# 全局模板引擎实例
template_engine = TaskTemplateEngine()
