#!/usr/bin/env python3
"""
资产管理系统 V3.0 初始化脚本
"""

import asyncio
import logging
from datetime import datetime

from database import AsyncSessionLocal
from elasticsearch_client import get_es_client
from asset_management_v3 import create_asset_manager_v3, DataSource, AssetType

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def check_elasticsearch_connection():
    """检查Elasticsearch连接"""
    try:
        es_client = await get_es_client()
        info = await es_client.info()
        logger.info(f"✅ Elasticsearch连接正常: {info['version']['number']}")
        return es_client
    except Exception as e:
        logger.error(f"❌ Elasticsearch连接失败: {e}")
        return None


async def create_sample_data(asset_manager):
    """创建示例数据"""
    logger.info("🔄 创建示例数据...")
    
    # 示例域名数据
    domain_data = [
        {"value": "example.com", "name": "示例网站", "tags": ["web", "demo"]},
        {"value": "test.example.com", "name": "测试子域名", "tags": ["subdomain", "test"]},
        {"value": "api.example.com", "name": "API接口", "tags": ["api", "service"]},
    ]
    
    # 示例IP数据
    ip_data = [
        {"value": "***********", "name": "内网网关", "tags": ["gateway", "internal"]},
        {"value": "*******", "name": "Google DNS", "tags": ["dns", "public"]},
        {"value": "*******", "name": "Cloudflare DNS", "tags": ["dns", "public"]},
    ]
    
    # 示例URL数据
    url_data = [
        {"value": "https://example.com/admin", "name": "管理后台", "tags": ["admin", "web"]},
        {"value": "https://api.example.com/v1", "name": "API端点", "tags": ["api", "endpoint"]},
    ]
    
    try:
        # 摄取域名数据
        result1 = await asset_manager.ingest_assets(
            raw_data_list=domain_data,
            data_source=DataSource.MANUAL_IMPORT,
            asset_type=AssetType.DOMAIN,
            source_id="init_sample_domains"
        )
        logger.info(f"域名数据摄取结果: {result1['storage']['success']} 成功")
        
        # 摄取IP数据
        result2 = await asset_manager.ingest_assets(
            raw_data_list=ip_data,
            data_source=DataSource.MANUAL_IMPORT,
            asset_type=AssetType.IP,
            source_id="init_sample_ips"
        )
        logger.info(f"IP数据摄取结果: {result2['storage']['success']} 成功")
        
        # 摄取URL数据
        result3 = await asset_manager.ingest_assets(
            raw_data_list=url_data,
            data_source=DataSource.MANUAL_IMPORT,
            asset_type=AssetType.URL,
            source_id="init_sample_urls"
        )
        logger.info(f"URL数据摄取结果: {result3['storage']['success']} 成功")
        
        logger.info("✅ 示例数据创建完成")
        
    except Exception as e:
        logger.error(f"❌ 创建示例数据失败: {e}")


async def test_search_functionality(asset_manager):
    """测试搜索功能"""
    logger.info("🔄 测试搜索功能...")
    
    try:
        # 测试基础搜索
        search_result = await asset_manager.search_assets(
            query="example",
            page=1,
            size=10
        )
        logger.info(f"搜索结果: 找到 {search_result['total']} 个资产")
        
        # 测试过滤搜索
        filter_result = await asset_manager.search_assets(
            filters={"metadata.asset_type": "domain"},
            page=1,
            size=10
        )
        logger.info(f"域名过滤结果: 找到 {filter_result['total']} 个域名")
        
        # 测试统计功能
        stats = await asset_manager.get_statistics()
        logger.info(f"统计信息: 总计 {stats['total_assets']} 个资产")
        
        logger.info("✅ 搜索功能测试完成")
        
    except Exception as e:
        logger.error(f"❌ 搜索功能测试失败: {e}")


async def verify_system_health(asset_manager):
    """验证系统健康状态"""
    logger.info("🔄 验证系统健康状态...")
    
    try:
        # 检查索引状态
        index_stats = await asset_manager.index_manager.get_index_stats()
        logger.info(f"索引统计: {index_stats['indices_count']} 个索引, "
                   f"{index_stats['total_documents']} 个文档, "
                   f"{index_stats['total_size_mb']} MB")
        
        # 检查搜索服务
        search_result = await asset_manager.search_assets(page=1, size=1)
        logger.info(f"搜索服务正常: 响应时间 {search_result.get('took', 0)} ms")
        
        logger.info("✅ 系统健康状态验证完成")
        
    except Exception as e:
        logger.error(f"❌ 系统健康状态验证失败: {e}")


async def main():
    """主函数"""
    print("🚀 资产管理系统 V3.0 初始化开始...")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 检查Elasticsearch连接
    es_client = await check_elasticsearch_connection()
    if not es_client:
        print("❌ 初始化失败: Elasticsearch连接不可用")
        return
    
    # 2. 获取数据库会话
    try:
        async with AsyncSessionLocal() as db_session:
            logger.info("✅ 数据库连接正常")
            
            # 3. 创建资产管理器
            logger.info("🏗️  初始化资产管理器...")
            asset_manager = await create_asset_manager_v3(es_client, db_session)
            logger.info("✅ 资产管理器初始化完成")
            
            # 4. 创建示例数据
            await create_sample_data(asset_manager)
            
            # 5. 测试搜索功能
            await test_search_functionality(asset_manager)
            
            # 6. 验证系统健康状态
            await verify_system_health(asset_manager)
            
            print("\n🎉 资产管理系统 V3.0 初始化完成！")
            print("📋 系统功能:")
            print("   • 多数据源统一摄取")
            print("   • 智能数据清洗和去重")
            print("   • 高性能搜索和分析")
            print("   • Elasticsearch主存储")
            print("   • RESTful API接口")
            print("\n🌐 API访问地址:")
            print("   • 搜索资产: GET /api/assets-v3/search")
            print("   • 摄取数据: POST /api/assets-v3/ingest")
            print("   • 上传CSV: POST /api/assets-v3/ingest/csv")
            print("   • 统计信息: GET /api/assets-v3/statistics")
            print("   • API文档: http://localhost:8000/docs")
            
    except Exception as e:
        logger.error(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
