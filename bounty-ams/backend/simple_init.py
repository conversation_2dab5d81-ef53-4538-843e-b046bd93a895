#!/usr/bin/env python3
"""
简化的资产管理V2初始化脚本
"""

import asyncio
import json
from datetime import datetime
from elasticsearch import AsyncElasticsearch
from elasticsearch_client import get_es_client

async def create_simple_template():
    """创建简化的索引模板"""
    es = await get_es_client()
    
    # 简化的索引模板
    template = {
        "index_patterns": ["unified-assets-*"],
        "template": {
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            },
            "mappings": {
                "properties": {
                    "asset_id": {"type": "keyword"},
                    "asset_type": {"type": "keyword"},
                    "asset_value": {
                        "type": "text",
                        "fields": {"keyword": {"type": "keyword"}}
                    },
                    "asset_host": {"type": "keyword"},
                    "asset_port": {"type": "integer"},
                    "asset_service": {"type": "keyword"},
                    "fingerprint_hash": {"type": "keyword"},
                    "is_duplicate": {"type": "boolean"},
                    "duplicate_of": {"type": "keyword"},
                    "source": {"type": "keyword"},
                    "confidence": {"type": "keyword"},
                    "status": {"type": "keyword"},
                    "tags": {"type": "keyword"},
                    "platform_id": {"type": "keyword"},
                    "project_id": {"type": "keyword"},
                    "platform_name": {"type": "keyword"},
                    "project_name": {"type": "keyword"},
                    "discovered_at": {"type": "date"},
                    "created_at": {"type": "date"},
                    "updated_at": {"type": "date"},
                    "searchable_text": {"type": "text"},
                    "data_quality_score": {"type": "float"},
                    "metadata": {"type": "object"},
                    "raw_data": {"type": "object", "enabled": False}
                }
            }
        }
    }
    
    try:
        # 删除旧模板（如果存在）
        try:
            await es.indices.delete_index_template(name="unified-assets-template")
            print("🗑️  删除旧模板")
        except:
            pass
        
        # 创建新模板
        await es.indices.put_index_template(
            name="unified-assets-template",
            body=template
        )
        print("✅ 创建索引模板成功")
        
        # 创建当前月份的索引
        index_name = f"unified-assets-{datetime.now().strftime('%Y-%m')}"
        
        if not await es.indices.exists(index=index_name):
            await es.indices.create(index=index_name)
            print(f"✅ 创建索引: {index_name}")
        
        # 创建别名
        await es.indices.put_alias(index=index_name, name="unified-assets")
        print("✅ 创建别名: unified-assets")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建模板失败: {e}")
        return False

async def migrate_data():
    """迁移现有数据"""
    es = await get_es_client()
    
    try:
        # 从 assets-2025-07 迁移数据
        print("📥 开始迁移数据...")
        
        # 查询现有数据
        response = await es.search(
            index="assets-2025-07",
            body={"query": {"match_all": {}}, "size": 100}
        )
        
        hits = response.get("hits", {}).get("hits", [])
        print(f"📊 找到 {len(hits)} 条数据")
        
        if hits:
            # 准备批量操作
            bulk_operations = []
            
            for hit in hits:
                source = hit["_source"]
                
                # 生成新的文档ID
                asset_id = f"migrated_{hit['_id']}"
                
                # 转换数据格式
                new_doc = {
                    "asset_id": asset_id,
                    "fingerprint_hash": asset_id,  # 简化的指纹
                    "asset_type": source.get("asset_type", "unknown"),
                    "asset_value": source.get("asset_value", ""),
                    "asset_host": source.get("asset_host"),
                    "asset_port": source.get("port"),
                    "asset_service": source.get("service"),
                    "source": "migrated",
                    "confidence": source.get("confidence", "medium"),
                    "status": source.get("status", "active"),
                    "tags": source.get("tags", []),
                    "platform_id": source.get("platform_id"),
                    "project_id": source.get("project_id"),
                    "discovered_at": source.get("discovered_at") or source.get("timestamp") or datetime.utcnow().isoformat(),
                    "created_at": datetime.utcnow().isoformat(),
                    "updated_at": datetime.utcnow().isoformat(),
                    "searchable_text": f"{source.get('asset_value', '')} {source.get('asset_host', '')}",
                    "data_quality_score": 0.8,
                    "is_duplicate": False,
                    "metadata": {
                        "migrated_from": "assets-2025-07",
                        "original_id": hit["_id"]
                    },
                    "raw_data": source
                }
                
                bulk_operations.extend([
                    {"index": {"_index": "unified-assets", "_id": asset_id}},
                    new_doc
                ])
            
            # 执行批量操作
            if bulk_operations:
                result = await es.bulk(body=bulk_operations, refresh=True)
                
                if result.get("errors"):
                    print("⚠️  部分数据迁移失败")
                    for item in result["items"]:
                        if "error" in item.get("index", {}):
                            print(f"错误: {item['index']['error']}")
                else:
                    print(f"✅ 成功迁移 {len(hits)} 条数据")
        
        # 验证迁移结果
        verify_response = await es.search(
            index="unified-assets",
            body={"query": {"match_all": {}}, "size": 0}
        )
        
        total = verify_response["hits"]["total"]["value"]
        print(f"📊 统一索引总数据量: {total}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据迁移失败: {e}")
        return False

async def main():
    """主函数"""
    print("🚀 开始简化初始化...")
    
    # 检查ES连接
    try:
        es = await get_es_client()
        health = await es.cluster.health()
        print(f"✅ ES连接正常: {health['status']}")
    except Exception as e:
        print(f"❌ ES连接失败: {e}")
        return
    
    # 创建模板和索引
    if await create_simple_template():
        print("✅ 索引设置完成")
        
        # 迁移数据
        if await migrate_data():
            print("✅ 数据迁移完成")
        
        print("\n🎉 初始化完成！")
        print("💡 现在可以测试API:")
        print("   curl http://localhost:8000/api/assets-v2-unified/statistics")
    else:
        print("❌ 初始化失败")

if __name__ == "__main__":
    asyncio.run(main())
