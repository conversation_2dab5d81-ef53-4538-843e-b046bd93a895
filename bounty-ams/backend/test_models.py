#!/usr/bin/env python3
"""
测试模型关系是否正确
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_model_imports():
    """测试模型导入"""
    print("🔧 测试模型导入...")
    
    try:
        from models_dynamic import User, Role, UserRole, UserActivityLog
        print("  ✅ 模型导入成功")
        return True
        
    except Exception as e:
        print(f"  ❌ 模型导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sqlalchemy_setup():
    """测试SQLAlchemy设置"""
    print("\n🔧 测试SQLAlchemy设置...")
    
    try:
        from sqlalchemy import create_engine
        from sqlalchemy.orm import sessionmaker
        from models_dynamic import Base, User, Role, UserRole, UserActivityLog
        
        # 创建内存数据库进行测试
        engine = create_engine("sqlite:///:memory:")
        
        # 尝试创建表结构
        Base.metadata.create_all(engine)
        print("  ✅ 表结构创建成功")
        
        # 测试会话创建
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        session = SessionLocal()
        session.close()
        print("  ✅ 会话创建成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ SQLAlchemy设置失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_relationships():
    """测试模型关系"""
    print("\n🔧 测试模型关系...")
    
    try:
        from sqlalchemy import create_engine
        from sqlalchemy.orm import sessionmaker
        from models_dynamic import Base, User, Role, UserRole, UserActivityLog
        
        # 创建内存数据库
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        with SessionLocal() as session:
            # 创建测试用户
            user = User(
                username="testuser",
                email="<EMAIL>",
                hashed_password="hashedpassword"
            )
            session.add(user)
            session.commit()
            session.refresh(user)
            print("  ✅ 用户创建成功")
            
            # 创建测试角色
            role = Role(
                name="test_role",
                display_name="测试角色",
                permissions=["test.read"]
            )
            session.add(role)
            session.commit()
            session.refresh(role)
            print("  ✅ 角色创建成功")
            
            # 创建用户角色关联
            user_role = UserRole(
                user_id=user.id,
                role_id=role.id
            )
            session.add(user_role)
            session.commit()
            print("  ✅ 用户角色关联创建成功")
            
            # 创建活动日志
            activity_log = UserActivityLog(
                user_id=user.id,
                action="test.action",
                details={"test": "data"}
            )
            session.add(activity_log)
            session.commit()
            print("  ✅ 活动日志创建成功")
            
            # 测试关系查询
            user_with_roles = session.query(User).filter(User.id == user.id).first()
            print(f"  ✅ 用户关系查询成功: {len(user_with_roles.user_roles)} 个角色")
            
        return True
        
    except Exception as e:
        print(f"  ❌ 模型关系测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 模型关系测试")
    print("=" * 50)
    
    success_count = 0
    total_tests = 3
    
    # 测试模型导入
    if test_model_imports():
        success_count += 1
    
    # 测试SQLAlchemy设置
    if test_sqlalchemy_setup():
        success_count += 1
    
    # 测试模型关系
    if test_model_relationships():
        success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有模型关系测试通过！")
        print("✅ 可以安全重启后端服务")
    else:
        print("⚠️  部分测试失败，需要修复模型关系")
    
    return 0 if success_count == total_tests else 1

if __name__ == "__main__":
    exit(main())
