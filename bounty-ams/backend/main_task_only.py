"""
仅包含任务管理系统的简化启动文件
用于避免模型冲突问题
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from routes.auth import router as auth_router
from routes.dynamic_users import router as dynamic_users_router
from routes.dynamic_models import router as dynamic_models_router
from routes.task_management import router as task_management_router

from config import settings
from elasticsearch_client import es_client
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Bounty AMS Task Management API", version="1.0.0")

@app.on_event("startup")
async def startup_event():
    """Initialize connections on startup"""
    try:
        await es_client.connect()
        logger.info("Elasticsearch connected successfully")
    except Exception as e:
        logger.error(f"Failed to connect to Elasticsearch: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    """Clean up connections on shutdown"""
    await es_client.disconnect()
    logger.info("Elasticsearch disconnected")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include only essential routers
app.include_router(auth_router, prefix="/api/auth", tags=["auth"])
app.include_router(dynamic_users_router, prefix="/api", tags=["dynamic-users"])
app.include_router(dynamic_models_router, prefix="/api", tags=["dynamic-models"])
app.include_router(task_management_router, prefix="/api", tags=["task-management"])

@app.get("/")
def root():
    return {"message": "Bounty AMS Task Management API is running"}

@app.get("/health")
def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
