"""
任务管理相关的Pydantic schemas
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum

class TaskStatusEnum(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"

class WorkflowStatusEnum(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"

class AgentStatusEnum(str, Enum):
    ONLINE = "online"
    OFFLINE = "offline"
    BUSY = "busy"
    MAINTENANCE = "maintenance"

# ==================== 任务模板相关 ====================

class ToolConfigSchema(BaseModel):
    tool_name: str
    version: str
    command: str
    args: List[str]
    env: Dict[str, str] = Field(default_factory=dict)
    output_format: str = "json"
    output_parser: str = "json"
    timeout: int = 300
    retry_count: int = 3

class TaskTemplateBase(BaseModel):
    template_id: str = Field(..., description="模板唯一标识")
    name: str = Field(..., description="模板名称")
    version: str = Field(..., description="模板版本")
    description: Optional[str] = Field(None, description="模板描述")
    category: str = Field(..., description="模板分类")
    tags: List[str] = Field(default_factory=list, description="标签列表")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="参数schema")
    tools: List[ToolConfigSchema] = Field(..., description="工具配置列表")
    execution: Dict[str, Any] = Field(default_factory=dict, description="执行配置")
    output_schema: Dict[str, Any] = Field(default_factory=dict, description="输出schema")

class TaskTemplateCreate(TaskTemplateBase):
    created_by: Optional[str] = Field(None, description="创建者")

class TaskTemplateResponse(TaskTemplateBase):
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# ==================== 工作流相关 ====================

class TaskDefinitionSchema(BaseModel):
    task_id: str
    template_id: str
    parameters: Dict[str, Any] = Field(default_factory=dict)
    condition: str = "always"
    depends_on: List[str] = Field(default_factory=list)
    timeout: Optional[int] = None
    retry_count: int = 0
    on_failure: str = "stop"

class WorkflowStageSchema(BaseModel):
    stage_id: str
    name: str
    tasks: List[TaskDefinitionSchema]
    parallel: bool = False
    condition: str = "always"
    timeout: Optional[int] = None
    on_failure: str = "stop"

class WorkflowBase(BaseModel):
    workflow_id: str = Field(..., description="工作流唯一标识")
    name: str = Field(..., description="工作流名称")
    version: str = Field(..., description="工作流版本")
    description: Optional[str] = Field(None, description="工作流描述")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="参数schema")
    stages: List[WorkflowStageSchema] = Field(..., description="阶段列表")
    error_handling: Dict[str, Any] = Field(default_factory=dict, description="错误处理配置")
    output_aggregation: Dict[str, Any] = Field(default_factory=dict, description="输出聚合配置")

class WorkflowCreate(WorkflowBase):
    created_by: Optional[str] = Field(None, description="创建者")

class WorkflowResponse(WorkflowBase):
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# ==================== Agent相关 ====================

class AgentCapabilitySchema(BaseModel):
    tool_name: str
    version: str
    supported_formats: List[str] = Field(default_factory=list)
    max_concurrent: int = 1
    resource_requirements: Dict[str, str] = Field(default_factory=dict)
    performance_score: float = 1.0

class AgentBase(BaseModel):
    agent_id: str = Field(..., description="Agent唯一标识")
    name: str = Field(..., description="Agent名称")
    capabilities: List[AgentCapabilitySchema] = Field(..., description="能力列表")
    resource_limits: Dict[str, str] = Field(default_factory=dict, description="资源限制")
    max_capacity: int = Field(10, description="最大容量")
    location: Optional[str] = Field(None, description="地理位置")
    tags: List[str] = Field(default_factory=list, description="标签列表")

class AgentCreate(AgentBase):
    pass

class AgentResponse(AgentBase):
    status: AgentStatusEnum
    current_load: int = 0
    last_heartbeat: Optional[datetime] = None
    performance_history: Dict[str, List[float]] = Field(default_factory=dict)
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# ==================== 任务执行相关 ====================

class TaskExecutionBase(BaseModel):
    template_id: str = Field(..., description="模板ID")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="执行参数")
    priority: int = Field(2, description="优先级 1=高 2=中 3=低")
    timeout: Optional[int] = Field(None, description="超时时间(秒)")

class TaskExecutionCreate(TaskExecutionBase):
    created_by: Optional[str] = Field(None, description="创建者")

class TaskExecutionResponse(TaskExecutionBase):
    execution_id: str
    status: TaskStatusEnum
    agent_id: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    progress: int = 0
    output: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    logs: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    retry_count: int = 0
    max_retries: int = 3
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# ==================== 工作流执行相关 ====================

class WorkflowExecutionBase(BaseModel):
    workflow_id: str = Field(..., description="工作流ID")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="执行参数")

class WorkflowExecutionCreate(WorkflowExecutionBase):
    created_by: Optional[str] = Field(None, description="创建者")

class WorkflowExecutionResponse(WorkflowExecutionBase):
    execution_id: str
    status: WorkflowStatusEnum
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    current_stage: Optional[str] = None
    progress: int = 0
    context: Dict[str, Any] = Field(default_factory=dict)
    stage_status: Dict[str, str] = Field(default_factory=dict)
    output: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    logs: List[str] = Field(default_factory=list)
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# ==================== 统计和监控相关 ====================

class TaskStatistics(BaseModel):
    total: int = 0
    pending: int = 0
    running: int = 0
    success: int = 0
    failed: int = 0
    cancelled: int = 0

class WorkflowStatistics(BaseModel):
    total: int = 0
    pending: int = 0
    running: int = 0
    success: int = 0
    failed: int = 0
    cancelled: int = 0

class AgentStatistics(BaseModel):
    total: int = 0
    online: int = 0
    offline: int = 0
    busy: int = 0
    maintenance: int = 0

class SystemStatistics(BaseModel):
    task_stats: TaskStatistics
    workflow_stats: WorkflowStatistics
    agent_stats: AgentStatistics
    queue_size: int = 0
    avg_execution_time: float = 0.0

# ==================== 队列和监控相关 ====================

class QueueStatus(BaseModel):
    queue_size: int
    running_tasks: int
    completed_tasks: int
    available_agents: int
    total_agents: int

class AgentHeartbeatData(BaseModel):
    current_load: int
    running_tasks: List[str] = Field(default_factory=list)
    system_info: Dict[str, Any] = Field(default_factory=dict)

class TaskLogEntry(BaseModel):
    level: str
    message: str
    timestamp: datetime
    source: str

# ==================== 批量操作相关 ====================

class BatchTaskCreate(BaseModel):
    template_id: str
    parameter_sets: List[Dict[str, Any]] = Field(..., description="参数集合列表")
    priority: int = 2
    created_by: Optional[str] = None

class BatchTaskResponse(BaseModel):
    execution_ids: List[str]
    total_count: int
    success_count: int
    failed_count: int

# ==================== 搜索和过滤相关 ====================

class TaskExecutionFilter(BaseModel):
    status: Optional[TaskStatusEnum] = None
    template_id: Optional[str] = None
    agent_id: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    created_by: Optional[str] = None

class WorkflowExecutionFilter(BaseModel):
    status: Optional[WorkflowStatusEnum] = None
    workflow_id: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    created_by: Optional[str] = None

# ==================== 验证器 ====================

class TaskTemplateCreate(TaskTemplateCreate):
    @validator('template_id')
    def validate_template_id(cls, v):
        if not v or len(v) < 3:
            raise ValueError('模板ID长度至少3个字符')
        return v
    
    @validator('tools')
    def validate_tools(cls, v):
        if not v:
            raise ValueError('至少需要配置一个工具')
        return v

class WorkflowCreate(WorkflowCreate):
    @validator('workflow_id')
    def validate_workflow_id(cls, v):
        if not v or len(v) < 3:
            raise ValueError('工作流ID长度至少3个字符')
        return v
    
    @validator('stages')
    def validate_stages(cls, v):
        if not v:
            raise ValueError('至少需要配置一个阶段')
        return v

class AgentCreate(AgentCreate):
    @validator('agent_id')
    def validate_agent_id(cls, v):
        if not v or len(v) < 3:
            raise ValueError('Agent ID长度至少3个字符')
        return v
    
    @validator('capabilities')
    def validate_capabilities(cls, v):
        if not v:
            raise ValueError('至少需要配置一个能力')
        return v
