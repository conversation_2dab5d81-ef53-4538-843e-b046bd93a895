#!/usr/bin/env python3
"""
清理重复的项目数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 同步数据库连接
SYNC_DATABASE_URL = 'postgresql://postgres:postgres@localhost:5432/bounty_ams'

def clean_duplicate_projects(db):
    """清理重复的项目数据"""
    print("\n🔧 清理重复的Project数据...")
    
    # 获取project模型ID
    result = db.execute(text("""
        SELECT id FROM model_types WHERE name = 'project'
    """))
    project_model_id = result.fetchone()[0]
    
    # 查找重复的项目（按platform_id + name）
    result = db.execute(text(f"""
        SELECT 
            entity_data->>'platform_id' as platform_id,
            entity_data->>'name' as name,
            array_agg(id ORDER BY created_at) as ids,
            COUNT(*) as count
        FROM dynamic_entities 
        WHERE model_type_id = '{project_model_id}'
        GROUP BY entity_data->>'platform_id', entity_data->>'name'
        HAVING COUNT(*) > 1;
    """))
    
    name_duplicates = list(result)
    
    for row in name_duplicates:
        platform_id = row[0]
        name = row[1]
        ids = row[2]
        count = row[3]
        
        print(f"  处理重复项目: {name} (平台: {platform_id}, {count}个实例)")
        
        # 保留第一个，删除其他的
        keep_id = ids[0]
        delete_ids = ids[1:]
        
        print(f"    保留: {keep_id}")
        for delete_id in delete_ids:
            print(f"    删除: {delete_id}")
            
            # 删除重复的项目
            db.execute(text(f"""
                DELETE FROM dynamic_entities WHERE id = '{delete_id}'
            """))
        
        print(f"    ✅ 项目 {name} 重复数据已清理")
    
    # 查找重复的项目（按platform_id + external_id）
    result = db.execute(text(f"""
        SELECT 
            entity_data->>'platform_id' as platform_id,
            entity_data->>'external_id' as external_id,
            array_agg(id ORDER BY created_at) as ids,
            COUNT(*) as count
        FROM dynamic_entities 
        WHERE model_type_id = '{project_model_id}'
        AND entity_data->>'external_id' IS NOT NULL
        AND entity_data->>'external_id' != ''
        GROUP BY entity_data->>'platform_id', entity_data->>'external_id'
        HAVING COUNT(*) > 1;
    """))
    
    external_duplicates = list(result)
    
    for row in external_duplicates:
        platform_id = row[0]
        external_id = row[1]
        ids = row[2]
        count = row[3]
        
        print(f"  处理重复external_id: {external_id} (平台: {platform_id}, {count}个实例)")
        
        # 保留第一个，删除其他的
        keep_id = ids[0]
        delete_ids = ids[1:]
        
        print(f"    保留: {keep_id}")
        for delete_id in delete_ids:
            print(f"    删除: {delete_id}")
            
            # 删除重复的项目
            db.execute(text(f"""
                DELETE FROM dynamic_entities WHERE id = '{delete_id}'
            """))
        
        print(f"    ✅ external_id {external_id} 重复数据已清理")

def add_simple_indexes(db):
    """添加简单的数据库索引"""
    print("\n🔧 添加数据库索引...")
    
    # 获取模型ID
    result = db.execute(text("SELECT id FROM model_types WHERE name = 'platform'"))
    platform_model_id = result.fetchone()[0]
    
    result = db.execute(text("SELECT id FROM model_types WHERE name = 'project'"))
    project_model_id = result.fetchone()[0]
    
    indexes_to_create = [
        # 为dynamic_entities表添加基本索引
        f"CREATE INDEX IF NOT EXISTS idx_dynamic_entities_model_type ON dynamic_entities (model_type_id)",
        f"CREATE INDEX IF NOT EXISTS idx_dynamic_entities_platform_name ON dynamic_entities ((entity_data->>'name')) WHERE model_type_id = '{platform_model_id}'",
        f"CREATE INDEX IF NOT EXISTS idx_dynamic_entities_project_platform ON dynamic_entities ((entity_data->>'platform_id')) WHERE model_type_id = '{project_model_id}'",
        f"CREATE INDEX IF NOT EXISTS idx_dynamic_entities_project_name ON dynamic_entities ((entity_data->>'name')) WHERE model_type_id = '{project_model_id}'",
        f"CREATE INDEX IF NOT EXISTS idx_dynamic_entities_project_external ON dynamic_entities ((entity_data->>'external_id')) WHERE model_type_id = '{project_model_id}'",
    ]
    
    for index_sql in indexes_to_create:
        try:
            db.execute(text(index_sql))
            print(f"  ✅ 索引创建成功")
        except Exception as e:
            print(f"  ⚠️  索引创建失败: {e}")

def final_check(db):
    """最终检查"""
    print("\n🔍 最终检查...")
    
    # 获取模型ID
    result = db.execute(text("SELECT id FROM model_types WHERE name = 'platform'"))
    platform_model_id = result.fetchone()[0]
    
    result = db.execute(text("SELECT id FROM model_types WHERE name = 'project'"))
    project_model_id = result.fetchone()[0]
    
    # 检查平台重复
    result = db.execute(text(f"""
        SELECT 
            entity_data->>'name' as name,
            COUNT(*) as count
        FROM dynamic_entities 
        WHERE model_type_id = '{platform_model_id}'
        GROUP BY entity_data->>'name'
        HAVING COUNT(*) > 1;
    """))
    
    platform_duplicates = list(result)
    if platform_duplicates:
        print("  ⚠️  仍有重复的Platform:")
        for row in platform_duplicates:
            print(f"    {row[0]}: {row[1]}个")
    else:
        print("  ✅ Platform名称无重复")
    
    # 检查项目名称重复
    result = db.execute(text(f"""
        SELECT 
            entity_data->>'platform_id' as platform_id,
            entity_data->>'name' as name,
            COUNT(*) as count
        FROM dynamic_entities 
        WHERE model_type_id = '{project_model_id}'
        GROUP BY entity_data->>'platform_id', entity_data->>'name'
        HAVING COUNT(*) > 1;
    """))
    
    project_name_duplicates = list(result)
    if project_name_duplicates:
        print("  ⚠️  仍有重复的Project名称:")
        for row in project_name_duplicates:
            print(f"    平台 {row[0]} - 项目 {row[1]}: {row[2]}个")
    else:
        print("  ✅ 同一平台下Project名称无重复")
    
    # 检查external_id重复
    result = db.execute(text(f"""
        SELECT 
            entity_data->>'platform_id' as platform_id,
            entity_data->>'external_id' as external_id,
            COUNT(*) as count
        FROM dynamic_entities 
        WHERE model_type_id = '{project_model_id}'
        AND entity_data->>'external_id' IS NOT NULL
        AND entity_data->>'external_id' != ''
        GROUP BY entity_data->>'platform_id', entity_data->>'external_id'
        HAVING COUNT(*) > 1;
    """))
    
    external_duplicates = list(result)
    if external_duplicates:
        print("  ⚠️  仍有重复的external_id:")
        for row in external_duplicates:
            print(f"    平台 {row[0]} - external_id {row[1]}: {row[2]}个")
    else:
        print("  ✅ 同一平台下external_id无重复")
    
    # 统计最终数据
    result = db.execute(text(f"""
        SELECT COUNT(*) FROM dynamic_entities WHERE model_type_id = '{platform_model_id}'
    """))
    platform_count = result.fetchone()[0]
    
    result = db.execute(text(f"""
        SELECT COUNT(*) FROM dynamic_entities WHERE model_type_id = '{project_model_id}'
    """))
    project_count = result.fetchone()[0]
    
    print(f"\n📊 最终统计:")
    print(f"  Platform数量: {platform_count}")
    print(f"  Project数量: {project_count}")

def main():
    """主函数"""
    try:
        engine = create_engine(SYNC_DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        with SessionLocal() as db:
            print("🔧 清理重复的项目数据")
            
            # 1. 清理重复的项目数据
            clean_duplicate_projects(db)
            
            # 2. 添加索引
            add_simple_indexes(db)
            
            # 3. 最终检查
            final_check(db)
            
            # 提交所有更改
            db.commit()
            
            print(f"\n✅ 重复数据清理完成")
            
    except Exception as e:
        print(f"❌ 清理过程中出错: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
