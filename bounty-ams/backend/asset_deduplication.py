"""
资产去重和数据清洗模块
实现智能去重算法、数据标准化和质量提升
"""

from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime
import hashlib
import logging
from dataclasses import asdict
from elasticsearch import AsyncElasticsearch

# 避免循环导入，使用动态导入
# from asset_management_v3 import Asset, AssetType, DataSource, ProcessingStatus, QualityLevel

logger = logging.getLogger(__name__)


class AssetDeduplicator:
    """资产去重器"""
    
    def __init__(self, es_client: AsyncElasticsearch):
        self.es_client = es_client
        self.similarity_threshold = 0.85
        self.exact_match_fields = ["value", "asset_type"]
        self.fuzzy_match_fields = ["name", "description"]
        
    async def find_duplicates(
        self,
        asset,
        index_name: str = "assets-current"
    ) -> List[Dict[str, Any]]:
        """查找重复资产"""
        try:
            # 1. 精确匹配查询
            exact_duplicates = await self._find_exact_duplicates(asset, index_name)
            
            # 2. 模糊匹配查询
            fuzzy_duplicates = await self._find_fuzzy_duplicates(asset, index_name)
            
            # 3. 合并去重结果
            all_duplicates = self._merge_duplicate_results(exact_duplicates, fuzzy_duplicates)
            
            return all_duplicates
            
        except Exception as e:
            logger.error(f"查找重复资产失败: {e}")
            return []
    
    async def _find_exact_duplicates(
        self,
        asset,
        index_name: str
    ) -> List[Dict[str, Any]]:
        """查找精确重复"""
        query = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "term": {
                                "data.value.keyword": asset.data.value
                            }
                        },
                        {
                            "term": {
                                "metadata.asset_type": asset.metadata.asset_type
                            }
                        }
                    ]
                }
            },
            "size": 100
        }
        
        try:
            response = await self.es_client.search(
                index=index_name,
                body=query
            )
            
            duplicates = []
            for hit in response["hits"]["hits"]:
                duplicates.append({
                    "id": hit["_id"],
                    "source": hit["_source"],
                    "score": hit["_score"],
                    "match_type": "exact"
                })
            
            return duplicates
            
        except Exception as e:
            logger.error(f"精确匹配查询失败: {e}")
            return []
    
    async def _find_fuzzy_duplicates(
        self,
        asset: Asset,
        index_name: str
    ) -> List[Dict[str, Any]]:
        """查找模糊重复"""
        query = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "term": {
                                "metadata.asset_type": asset.metadata.asset_type
                            }
                        }
                    ],
                    "should": [
                        {
                            "fuzzy": {
                                "data.value": {
                                    "value": asset.data.value,
                                    "fuzziness": "AUTO"
                                }
                            }
                        }
                    ],
                    "minimum_should_match": 1
                }
            },
            "size": 50
        }
        
        # 添加名称模糊匹配
        if asset.data.name:
            query["query"]["bool"]["should"].append({
                "fuzzy": {
                    "data.name": {
                        "value": asset.data.name,
                        "fuzziness": "AUTO"
                    }
                }
            })
        
        try:
            response = await self.es_client.search(
                index=index_name,
                body=query
            )
            
            duplicates = []
            for hit in response["hits"]["hits"]:
                # 计算相似度
                similarity = self._calculate_similarity(asset, hit["_source"])
                
                if similarity >= self.similarity_threshold:
                    duplicates.append({
                        "id": hit["_id"],
                        "source": hit["_source"],
                        "score": hit["_score"],
                        "similarity": similarity,
                        "match_type": "fuzzy"
                    })
            
            return duplicates
            
        except Exception as e:
            logger.error(f"模糊匹配查询失败: {e}")
            return []
    
    def _calculate_similarity(self, asset1: Asset, asset2_source: Dict[str, Any]) -> float:
        """计算资产相似度"""
        try:
            # 重建asset2对象
            asset2 = Asset.from_dict(asset2_source)
            
            similarities = []
            
            # 值相似度（权重最高）
            value_sim = self._string_similarity(asset1.data.value, asset2.data.value)
            similarities.append(value_sim * 0.6)
            
            # 名称相似度
            if asset1.data.name and asset2.data.name:
                name_sim = self._string_similarity(asset1.data.name, asset2.data.name)
                similarities.append(name_sim * 0.2)
            
            # 描述相似度
            if asset1.data.description and asset2.data.description:
                desc_sim = self._string_similarity(asset1.data.description, asset2.data.description)
                similarities.append(desc_sim * 0.1)
            
            # 标签相似度
            if asset1.data.tags and asset2.data.tags:
                tag_sim = self._list_similarity(asset1.data.tags, asset2.data.tags)
                similarities.append(tag_sim * 0.1)
            
            return sum(similarities) / len(similarities) if similarities else 0.0
            
        except Exception as e:
            logger.error(f"计算相似度失败: {e}")
            return 0.0
    
    def _string_similarity(self, str1: str, str2: str) -> float:
        """计算字符串相似度（使用Levenshtein距离）"""
        if not str1 or not str2:
            return 0.0
        
        str1, str2 = str1.lower(), str2.lower()
        
        if str1 == str2:
            return 1.0
        
        # 简化的Levenshtein距离计算
        len1, len2 = len(str1), len(str2)
        if len1 == 0:
            return 0.0
        if len2 == 0:
            return 0.0
        
        # 创建距离矩阵
        matrix = [[0] * (len2 + 1) for _ in range(len1 + 1)]
        
        for i in range(len1 + 1):
            matrix[i][0] = i
        for j in range(len2 + 1):
            matrix[0][j] = j
        
        for i in range(1, len1 + 1):
            for j in range(1, len2 + 1):
                cost = 0 if str1[i-1] == str2[j-1] else 1
                matrix[i][j] = min(
                    matrix[i-1][j] + 1,      # 删除
                    matrix[i][j-1] + 1,      # 插入
                    matrix[i-1][j-1] + cost  # 替换
                )
        
        distance = matrix[len1][len2]
        max_len = max(len1, len2)
        
        return 1.0 - (distance / max_len)
    
    def _list_similarity(self, list1: List[str], list2: List[str]) -> float:
        """计算列表相似度（Jaccard相似度）"""
        if not list1 or not list2:
            return 0.0
        
        set1 = set(item.lower() for item in list1)
        set2 = set(item.lower() for item in list2)
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0
    
    def _merge_duplicate_results(
        self,
        exact_duplicates: List[Dict[str, Any]],
        fuzzy_duplicates: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """合并去重结果"""
        seen_ids = set()
        merged_results = []
        
        # 先添加精确匹配结果
        for duplicate in exact_duplicates:
            if duplicate["id"] not in seen_ids:
                seen_ids.add(duplicate["id"])
                merged_results.append(duplicate)
        
        # 再添加模糊匹配结果
        for duplicate in fuzzy_duplicates:
            if duplicate["id"] not in seen_ids:
                seen_ids.add(duplicate["id"])
                merged_results.append(duplicate)
        
        # 按相似度排序
        merged_results.sort(key=lambda x: x.get("similarity", x.get("score", 0)), reverse=True)
        
        return merged_results
    
    async def merge_duplicates(
        self,
        primary_asset: Asset,
        duplicate_assets: List[Dict[str, Any]],
        merge_strategy: str = "highest_quality"
    ) -> Asset:
        """合并重复资产"""
        try:
            if merge_strategy == "highest_quality":
                return await self._merge_by_quality(primary_asset, duplicate_assets)
            elif merge_strategy == "most_recent":
                return await self._merge_by_time(primary_asset, duplicate_assets)
            elif merge_strategy == "highest_confidence":
                return await self._merge_by_confidence(primary_asset, duplicate_assets)
            else:
                return primary_asset
                
        except Exception as e:
            logger.error(f"合并资产失败: {e}")
            return primary_asset
    
    async def _merge_by_quality(
        self,
        primary_asset: Asset,
        duplicate_assets: List[Dict[str, Any]]
    ) -> Asset:
        """按质量合并资产"""
        # 找到质量最高的资产
        best_asset = primary_asset
        best_quality_score = self._calculate_quality_score(primary_asset)
        
        for duplicate in duplicate_assets:
            try:
                dup_asset = Asset.from_dict(duplicate["source"])
                dup_quality_score = self._calculate_quality_score(dup_asset)
                
                if dup_quality_score > best_quality_score:
                    best_asset = dup_asset
                    best_quality_score = dup_quality_score
                    
            except Exception as e:
                logger.error(f"处理重复资产失败: {e}")
                continue
        
        # 合并其他资产的有用信息
        merged_asset = self._merge_asset_data(best_asset, [primary_asset] + 
                                            [Asset.from_dict(d["source"]) for d in duplicate_assets])
        
        return merged_asset
    
    def _calculate_quality_score(self, asset: Asset) -> float:
        """计算资产质量分数"""
        score = 0.0
        
        # 基础分数
        if asset.data.value:
            score += 1.0
        
        # 名称分数
        if asset.data.name:
            score += 0.5
        
        # 描述分数
        if asset.data.description:
            score += 0.3
        
        # 标签分数
        score += len(asset.data.tags) * 0.1
        
        # 属性分数
        score += len(asset.data.attributes) * 0.05
        
        # 置信度分数
        score += asset.metadata.confidence
        
        # 质量等级分数
        quality_scores = {
            QualityLevel.HIGH: 2.0,
            QualityLevel.MEDIUM: 1.0,
            QualityLevel.LOW: 0.5,
            QualityLevel.UNKNOWN: 0.0
        }
        score += quality_scores.get(asset.metadata.quality_level, 0.0)
        
        return score
    
    def _merge_asset_data(self, primary_asset: Asset, all_assets: List[Asset]) -> Asset:
        """合并资产数据"""
        merged_asset = primary_asset
        
        # 合并标签
        all_tags = set(primary_asset.data.tags)
        for asset in all_assets:
            all_tags.update(asset.data.tags)
        merged_asset.data.tags = list(all_tags)
        
        # 合并属性
        merged_attributes = primary_asset.data.attributes.copy()
        for asset in all_assets:
            for key, value in asset.data.attributes.items():
                if key not in merged_attributes:
                    merged_attributes[key] = value
        merged_asset.data.attributes = merged_attributes
        
        # 合并关联资产
        all_related = set(primary_asset.relation.related_assets)
        for asset in all_assets:
            all_related.update(asset.relation.related_assets)
        merged_asset.relation.related_assets = list(all_related)
        
        # 更新处理信息
        merged_asset.processing.processing_notes = f"合并了 {len(all_assets)} 个重复资产"
        merged_asset.metadata.updated_at = datetime.utcnow()
        
        return merged_asset


class DataCleaner:
    """数据清洗器"""
    
    def __init__(self):
        self.cleaning_rules = {
            AssetType.DOMAIN: self._clean_domain,
            AssetType.IP: self._clean_ip,
            AssetType.URL: self._clean_url,
            AssetType.EMAIL: self._clean_email,
        }
    
    def clean_asset(self, asset: Asset) -> Asset:
        """清洗资产数据"""
        try:
            # 通用清洗
            asset = self._clean_common(asset)
            
            # 类型特定清洗
            if asset.metadata.asset_type in self.cleaning_rules:
                cleaner = self.cleaning_rules[asset.metadata.asset_type]
                asset = cleaner(asset)
            
            return asset
            
        except Exception as e:
            logger.error(f"数据清洗失败: {e}")
            return asset
    
    def _clean_common(self, asset: Asset) -> Asset:
        """通用数据清洗"""
        # 清理值
        if asset.data.value:
            asset.data.value = asset.data.value.strip().lower()
        
        # 清理名称
        if asset.data.name:
            asset.data.name = asset.data.name.strip()
        
        # 清理描述
        if asset.data.description:
            asset.data.description = asset.data.description.strip()
        
        # 清理标签
        asset.data.tags = [tag.strip().lower() for tag in asset.data.tags if tag.strip()]
        asset.data.tags = list(set(asset.data.tags))  # 去重
        
        return asset
    
    def _clean_domain(self, asset: Asset) -> Asset:
        """清洗域名数据"""
        value = asset.data.value
        
        # 移除协议前缀
        if value.startswith(('http://', 'https://')):
            value = value.split('://', 1)[1]
        
        # 移除路径
        if '/' in value:
            value = value.split('/')[0]
        
        # 移除端口
        if ':' in value:
            value = value.split(':')[0]
        
        # 移除www前缀（可选）
        if value.startswith('www.'):
            value = value[4:]
        
        asset.data.value = value
        return asset
    
    def _clean_ip(self, asset: Asset) -> Asset:
        """清洗IP地址数据"""
        value = asset.data.value
        
        # 移除端口
        if ':' in value and not value.count(':') > 1:  # 不是IPv6
            value = value.split(':')[0]
        
        asset.data.value = value
        return asset
    
    def _clean_url(self, asset: Asset) -> Asset:
        """清洗URL数据"""
        from urllib.parse import urlparse, urlunparse
        
        try:
            parsed = urlparse(asset.data.value)
            
            # 标准化URL
            cleaned_url = urlunparse((
                parsed.scheme.lower(),
                parsed.netloc.lower(),
                parsed.path,
                parsed.params,
                parsed.query,
                parsed.fragment
            ))
            
            asset.data.value = cleaned_url
            
        except Exception:
            # 如果解析失败，保持原值
            pass
        
        return asset
    
    def _clean_email(self, asset: Asset) -> Asset:
        """清洗邮箱数据"""
        value = asset.data.value.lower()
        asset.data.value = value
        return asset
