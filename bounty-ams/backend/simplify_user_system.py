#!/usr/bin/env python3
"""
简化用户管理系统
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 同步数据库连接
SYNC_DATABASE_URL = 'postgresql://postgres:postgres@localhost:5432/bounty_ams'

def clean_existing_roles(db):
    """清理现有的复杂角色系统"""
    print("\n🔧 清理现有角色系统...")
    
    try:
        # 删除所有用户角色关联
        db.execute(text("DELETE FROM user_roles"))
        print("  ✅ 清理用户角色关联")
        
        # 删除所有角色
        db.execute(text("DELETE FROM roles"))
        print("  ✅ 清理角色数据")
        
        # 删除活动日志
        db.execute(text("DELETE FROM user_activity_logs"))
        print("  ✅ 清理活动日志")
        
    except Exception as e:
        print(f"  ⚠️  清理过程中出错: {e}")

def create_simple_roles(db):
    """创建简单的角色系统"""
    print("\n🔧 创建简单角色系统...")
    
    # 获取admin用户ID
    result = db.execute(text("SELECT id FROM users WHERE username = 'admin' LIMIT 1"))
    admin_user = result.fetchone()
    admin_user_id = admin_user[0] if admin_user else None
    
    simple_roles = [
        {
            'name': 'admin',
            'display_name': '管理员',
            'description': '系统管理员，拥有所有权限',
            'permissions': [
                'user.create', 'user.read', 'user.update', 'user.delete',
                'platform.create', 'platform.read', 'platform.update', 'platform.delete',
                'project.create', 'project.read', 'project.update', 'project.delete',
                'asset.create', 'asset.read', 'asset.update', 'asset.delete',
                'agent.create', 'agent.read', 'agent.update', 'agent.delete',
                'task.create', 'task.read', 'task.update', 'task.delete',
                'workflow.create', 'workflow.read', 'workflow.update', 'workflow.delete',
                'system.admin'
            ],
            'is_system': True
        },
        {
            'name': 'user',
            'display_name': '普通用户',
            'description': '普通用户，只有查看权限',
            'permissions': [
                'platform.read',
                'project.read',
                'asset.read',
                'agent.read',
                'task.read',
                'workflow.read'
            ],
            'is_system': True
        }
    ]
    
    for role_data in simple_roles:
        permissions_json = str(role_data['permissions']).replace("'", '"')
        
        insert_sql = f"""
        INSERT INTO roles (name, display_name, description, permissions, is_system, is_active, created_by_user_id)
        VALUES (
            '{role_data['name']}',
            '{role_data['display_name']}',
            '{role_data['description']}',
            '{permissions_json}'::jsonb,
            {role_data['is_system']},
            true,
            {'NULL' if not admin_user_id else f"'{admin_user_id}'"}
        )
        """
        
        try:
            db.execute(text(insert_sql))
            print(f"  ✅ 角色 {role_data['display_name']} 创建成功")
        except Exception as e:
            print(f"  ⚠️  角色 {role_data['display_name']} 创建失败: {e}")

def assign_simple_roles(db):
    """为现有用户分配简单角色"""
    print("\n🔧 分配用户角色...")
    
    # 获取角色ID
    result = db.execute(text("SELECT id FROM roles WHERE name = 'admin'"))
    admin_role = result.fetchone()
    admin_role_id = admin_role[0] if admin_role else None
    
    result = db.execute(text("SELECT id FROM roles WHERE name = 'user'"))
    user_role = result.fetchone()
    user_role_id = user_role[0] if user_role else None
    
    if not admin_role_id or not user_role_id:
        print("  ❌ 角色创建失败，无法分配")
        return
    
    # 获取所有用户
    result = db.execute(text("SELECT id, username, is_admin FROM users"))
    users = list(result)
    
    for user in users:
        user_id, username, is_admin = user
        role_id = admin_role_id if is_admin else user_role_id
        role_name = "管理员" if is_admin else "普通用户"
        
        try:
            db.execute(text(f"""
                INSERT INTO user_roles (user_id, role_id, assigned_by_user_id)
                VALUES ('{user_id}', '{role_id}', '{user_id}')
            """))
            print(f"  ✅ 用户 {username} 分配角色: {role_name}")
        except Exception as e:
            print(f"  ⚠️  用户 {username} 角色分配失败: {e}")

def clean_extra_users(db):
    """清理多余的测试用户"""
    print("\n🔧 清理多余用户...")
    
    try:
        # 删除testuser（如果存在）
        result = db.execute(text("SELECT id FROM users WHERE username = 'testuser'"))
        testuser = result.fetchone()
        
        if testuser:
            testuser_id = testuser[0]
            # 先删除相关的角色关联
            db.execute(text(f"DELETE FROM user_roles WHERE user_id = '{testuser_id}'"))
            # 删除用户
            db.execute(text(f"DELETE FROM users WHERE id = '{testuser_id}'"))
            print("  ✅ 删除测试用户 testuser")
        else:
            print("  ℹ️  没有找到测试用户")
            
    except Exception as e:
        print(f"  ⚠️  清理用户失败: {e}")

def verify_final_state(db):
    """验证最终状态"""
    print("\n🔍 验证最终状态...")
    
    # 检查用户
    result = db.execute(text("SELECT username, email, is_admin FROM users"))
    users = list(result)
    print(f"  用户数量: {len(users)}")
    for user in users:
        print(f"    - {user[0]} ({user[1]}) - Admin: {user[2]}")
    
    # 检查角色
    result = db.execute(text("SELECT name, display_name FROM roles ORDER BY name"))
    roles = list(result)
    print(f"  角色数量: {len(roles)}")
    for role in roles:
        print(f"    - {role[1]} ({role[0]})")
    
    # 检查用户角色关联
    result = db.execute(text("""
        SELECT u.username, r.display_name
        FROM users u
        JOIN user_roles ur ON u.id = ur.user_id
        JOIN roles r ON ur.role_id = r.id
        ORDER BY u.username
    """))
    user_roles = list(result)
    print(f"  用户角色关联: {len(user_roles)}")
    for ur in user_roles:
        print(f"    - {ur[0]} -> {ur[1]}")

def main():
    """主函数"""
    try:
        engine = create_engine(SYNC_DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        with SessionLocal() as db:
            print("🔧 简化用户管理系统")
            print("=" * 50)
            
            # 1. 清理现有角色系统
            clean_existing_roles(db)
            
            # 2. 创建简单角色
            create_simple_roles(db)
            
            # 3. 分配角色
            assign_simple_roles(db)
            
            # 4. 清理多余用户
            clean_extra_users(db)
            
            # 5. 验证最终状态
            verify_final_state(db)
            
            # 提交所有更改
            db.commit()
            
            print(f"\n✅ 用户管理系统简化完成")
            print("\n📋 简化总结:")
            print("1. ✅ 清理了复杂的角色系统")
            print("2. ✅ 创建了简单的两级角色：管理员、普通用户")
            print("3. ✅ 为用户分配了合适的角色")
            print("4. ✅ 清理了测试数据")
            print("5. ✅ 保持了数据一致性")
            
    except Exception as e:
        print(f"❌ 简化过程中出错: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
