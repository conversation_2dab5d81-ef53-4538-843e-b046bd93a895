"""
统一资产聚合管道
负责将多种扫描器结果聚合成统一的资产数据，然后同步到PostgreSQL
"""

import asyncio
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, asdict
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, select, insert, update
from elasticsearch import AsyncElasticsearch

from database import get_db
from elasticsearch_client import get_es_client
from models_dynamic import ModelType, ModelField


@dataclass
class UnifiedAsset:
    """统一资产模型 - 支持全面的资产信息"""
    asset_id: str
    fingerprint_hash: str
    asset_type: str  # domain, subdomain, ip, port, service, url, certificate
    asset_value: str  # 主要值
    
    # 基础网络信息
    asset_host: Optional[str] = None
    asset_port: Optional[int] = None
    asset_service: Optional[str] = None
    ip_address: Optional[str] = None
    url: Optional[str] = None
    transport_protocol: Optional[str] = None  # 传输层协议
    scan_time: Optional[datetime] = None  # 测绘时间
    
    # 网络路径和域名信息
    website_path: Optional[str] = None  # 网站路径
    asn_number: Optional[str] = None  # 自治域号码
    asn_organization: Optional[str] = None  # 自治域归属组织
    international_domain: Optional[str] = None  # 国际化域名
    website_host: Optional[str] = None  # 网站Host（域名）
    
    # ICP备案信息
    icp_unit: Optional[str] = None  # ICP备案单位
    icp_number: Optional[str] = None  # ICP备案编号
    icp_update_time: Optional[datetime] = None  # ICP更新时间
    
    # 网站信息
    website_keywords: Optional[str] = None  # 网站关键词
    website_status_code: Optional[int] = None  # 网站状态码
    website_type: Optional[str] = None  # 网站类型
    webpage_title: Optional[str] = None  # 网页标题
    
    # 应用信息
    application_name: Optional[str] = None  # 应用名称
    application_level: Optional[str] = None  # 应用层级
    application_vendor: Optional[str] = None  # 应用生产厂商
    application_category: Optional[str] = None  # 应用类别
    application_type: Optional[str] = None  # 应用类型
    
    # 服务信息
    service_product: Optional[str] = None  # 服务产品
    service_raw_response: Optional[str] = None  # 服务原始响应
    service_name: Optional[str] = None  # 服务名称
    service_version: Optional[str] = None  # 服务版本号
    service_certificate: Optional[str] = None  # 服务证书
    
    # 地理位置信息（中文）
    country_cn: Optional[str] = None  # 国家（中文）
    province_cn: Optional[str] = None  # 省份（中文）
    city_cn: Optional[str] = None  # 城市（中文）
    district_cn: Optional[str] = None  # 区县（中文）
    
    # 地理位置信息（英文）
    country_en: Optional[str] = None  # 国家（英文）
    province_en: Optional[str] = None  # 省份（英文）
    city_en: Optional[str] = None  # 城市（英文）
    district_en: Optional[str] = None  # 区县（英文）
    
    # 运营商和证书信息
    isp: Optional[str] = None  # 运营商
    certificate_subject: Optional[str] = None  # 主体
    certificate_subject_country: Optional[str] = None  # 主体所属国家
    certificate_subject_organization: Optional[str] = None  # 主体所属组织
    certificate_subject_common_name: Optional[str] = None  # 主体通用名
    certificate_issuer: Optional[str] = None  # 签发者
    certificate_serial_number: Optional[str] = None  # 证书序列号
    
    # 置信度和状态
    confidence: float = 0.5
    status: str = 'discovered'  # discovered, verified, validated, false_positive
    
    # 来源信息
    source: str = 'unknown'
    source_task_id: Optional[str] = None
    source_task_type: Optional[str] = None
    platform_id: Optional[str] = None
    project_id: Optional[str] = None
    
    # 时间戳
    discovered_at: datetime = None
    verified_at: Optional[datetime] = None
    updated_at: datetime = None
    
    # 标签和元数据
    tags: List[str] = None
    metadata: Dict[str, Any] = None
    
    # 关联信息
    parent_asset_id: Optional[str] = None
    child_asset_ids: List[str] = None
    related_asset_ids: List[str] = None
    
    def __post_init__(self):
        if self.discovered_at is None:
            self.discovered_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = datetime.utcnow()
        if self.tags is None:
            self.tags = []
        if self.metadata is None:
            self.metadata = {}
        if self.child_asset_ids is None:
            self.child_asset_ids = []
        if self.related_asset_ids is None:
            self.related_asset_ids = []
    
    def generate_fingerprint(self) -> str:
        """生成资产指纹用于去重"""
        fingerprint_data = f"{self.asset_type}:{self.asset_value}"
        if self.asset_host:
            fingerprint_data += f":{self.asset_host}"
        if self.asset_port:
            fingerprint_data += f":{self.asset_port}"
        return hashlib.sha256(fingerprint_data.encode()).hexdigest()


class AssetAggregationPipeline:
    """资产聚合管道"""
    
    def __init__(self, es_client: AsyncElasticsearch, db: AsyncSession):
        self.es_client = es_client
        self.db = db
        self.unified_index = "unified_assets"
        # 更新为新的扫描器专用索引模式
        self.raw_indices = [
            "subfinder-results-*",
            "amass-results-*", 
            "nmap-results-*",
            "naabu-results-*",
            "httpx-results-*",
            "nuclei-results-*",
            "ffuf-results-*",
            "fofa-results-*",
            "quake-results-*",
            "shodan-results-*",
            "censys-results-*",
            "hunter-results-*",
            "zoomeye-results-*",
            # 保留旧索引以兼容历史数据
            "enhanced_asset-*",
            "assets-*",
            "unified-assets-*"
        ]
    
    async def ensure_unified_index(self):
        """确保统一索引存在"""
        mapping = {
            "mappings": {
                "properties": {
                    "asset_id": {"type": "keyword"},
                    "fingerprint_hash": {"type": "keyword"},
                    "asset_type": {"type": "keyword"},
                    "asset_value": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
                    
                    # 基础网络信息
                    "asset_host": {"type": "keyword"},
                    "asset_port": {"type": "integer"},
                    "asset_service": {"type": "keyword"},
                    "ip_address": {"type": "ip"},
                    "url": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
                    "transport_protocol": {"type": "keyword"},
                    "scan_time": {"type": "date"},
                    
                    # 网络路径和域名信息
                    "website_path": {"type": "text"},
                    "asn_number": {"type": "keyword"},
                    "asn_organization": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
                    "international_domain": {"type": "keyword"},
                    "website_host": {"type": "keyword"},
                    
                    # ICP备案信息
                    "icp_unit": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
                    "icp_number": {"type": "keyword"},
                    "icp_update_time": {"type": "date"},
                    
                    # 网站信息
                    "website_keywords": {"type": "text"},
                    "website_status_code": {"type": "integer"},
                    "website_type": {"type": "keyword"},
                    "webpage_title": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
                    
                    # 应用信息
                    "application_name": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
                    "application_level": {"type": "keyword"},
                    "application_vendor": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
                    "application_category": {"type": "keyword"},
                    "application_type": {"type": "keyword"},
                    
                    # 服务信息
                    "service_product": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
                    "service_raw_response": {"type": "text", "index": False},
                    "service_name": {"type": "keyword"},
                    "service_version": {"type": "keyword"},
                    "service_certificate": {"type": "text", "index": False},
                    
                    # 地理位置信息（中文）
                    "country_cn": {"type": "keyword"},
                    "province_cn": {"type": "keyword"},
                    "city_cn": {"type": "keyword"},
                    "district_cn": {"type": "keyword"},
                    
                    # 地理位置信息（英文）
                    "country_en": {"type": "keyword"},
                    "province_en": {"type": "keyword"},
                    "city_en": {"type": "keyword"},
                    "district_en": {"type": "keyword"},
                    
                    # 运营商和证书信息
                    "isp": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
                    "certificate_subject": {"type": "text"},
                    "certificate_subject_country": {"type": "keyword"},
                    "certificate_subject_organization": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
                    "certificate_subject_common_name": {"type": "keyword"},
                    "certificate_issuer": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
                    "certificate_serial_number": {"type": "keyword"},
                    
                    # 原有字段
                    "confidence": {"type": "float"},
                    "status": {"type": "keyword"},
                    "source": {"type": "keyword"},
                    "source_task_id": {"type": "keyword"},
                    "source_task_type": {"type": "keyword"},
                    "platform_id": {"type": "keyword"},
                    "project_id": {"type": "keyword"},
                    "discovered_at": {"type": "date"},
                    "verified_at": {"type": "date"},
                    "updated_at": {"type": "date"},
                    "tags": {"type": "keyword"},
                    "metadata": {"type": "object", "enabled": False},
                    "parent_asset_id": {"type": "keyword"},
                    "child_asset_ids": {"type": "keyword"},
                    "related_asset_ids": {"type": "keyword"}
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            }
        }
        
        # 检查索引是否存在
        if not await self.es_client.indices.exists(index=self.unified_index):
            await self.es_client.indices.create(index=self.unified_index, body=mapping)
            print(f"Created unified index: {self.unified_index}")
    
    async def collect_raw_assets(self) -> List[Dict[str, Any]]:
        """从各个原始索引收集资产数据"""
        raw_assets = []
        
        for index_pattern in self.raw_indices:
            try:
                query = {
                    "query": {"match_all": {}},
                    "size": 10000,  # 分批处理
                    "sort": [{"_timestamp": {"order": "desc"}}] if "_timestamp" in index_pattern else []
                }
                
                response = await self.es_client.search(
                    index=index_pattern,
                    body=query,
                    ignore_unavailable=True
                )
                
                hits = response.get('hits', {}).get('hits', [])
                for hit in hits:
                    raw_asset = {
                        '_id': hit['_id'],
                        '_index': hit['_index'],
                        '_source': hit['_source']
                    }
                    raw_assets.append(raw_asset)
                    
                print(f"Collected {len(hits)} assets from {index_pattern}")
                
            except Exception as e:
                print(f"Error collecting from {index_pattern}: {e}")
                continue
        
        return raw_assets
    
    def normalize_asset(self, raw_asset: Dict[str, Any]) -> Optional[UnifiedAsset]:
        """将原始资产数据标准化为统一资产模型"""
        source = raw_asset['_source']
        index = raw_asset['_index']
        
        try:
            # 根据不同索引的数据结构进行转换
            if any(pattern in index for pattern in ['subfinder-results', 'amass-results']):
                return self._normalize_scanner_asset(source, 'subdomain')
            elif any(pattern in index for pattern in ['nmap-results', 'naabu-results']):
                return self._normalize_scanner_asset(source, 'port')
            elif 'httpx-results' in index:
                return self._normalize_scanner_asset(source, 'url')
            elif 'nuclei-results' in index:
                return self._normalize_scanner_asset(source, 'vulnerability')
            elif 'ffuf-results' in index:
                return self._normalize_scanner_asset(source, 'directory')
            elif any(pattern in index for pattern in ['fofa-results', 'quake-results', 'shodan-results', 'censys-results', 'hunter-results', 'zoomeye-results']):
                return self._normalize_scanner_asset(source, 'service')
            elif 'enhanced_asset' in index:
                return self._normalize_enhanced_asset(source)
            elif 'unified-assets' in index:
                return self._normalize_unified_asset(source)
            elif 'assets' in index:
                return self._normalize_legacy_asset(source)
            else:
                return self._normalize_generic_asset(source)
                
        except Exception as e:
            print(f"Error normalizing asset from {index}: {e}")
            return None
    
    def _normalize_scanner_asset(self, source: Dict[str, Any], default_asset_type: str) -> UnifiedAsset:
        """标准化扫描器数据（新格式）"""
        # 获取核心字段
        asset_value = (
            source.get('asset_value') or 
            source.get('host') or 
            source.get('url') or 
            source.get('name') or
            source.get('subdomain') or
            source.get('ip') or
            source.get('domain') or
            ''
        )
        
        asset_type = source.get('asset_type', default_asset_type)
        confidence = source.get('confidence', 0.5)
        
        # 处理置信度值
        if isinstance(confidence, str):
            confidence_map = {'low': 0.3, 'medium': 0.6, 'high': 0.9, 'critical': 1.0}
            confidence = confidence_map.get(confidence.lower(), 0.5)
        
        asset = UnifiedAsset(
            asset_id=source.get('id', f"scanner_{hash(str(source))}"),
            fingerprint_hash="",  # 将被重新计算
            asset_type=asset_type,
            asset_value=asset_value,
            
            # 基础网络信息
            asset_host=source.get('asset_host') or source.get('target_host') or source.get('host') or source.get('hostname'),
            asset_port=source.get('asset_port') or source.get('port'),
            asset_service=source.get('service_name') or source.get('service'),
            ip_address=source.get('ip_address') or source.get('ip') or source.get('ip_str'),
            url=source.get('url'),
            transport_protocol=source.get('protocol') or source.get('transport_protocol'),
            scan_time=self._parse_datetime(source.get('scan_time') or source.get('time') or source.get('lastupdatetime')),
            
            # 网络路径和域名信息
            website_path=source.get('website_path') or source.get('path'),
            asn_number=source.get('asn_number') or source.get('as_number'),
            asn_organization=source.get('asn_organization') or source.get('as_organization') or source.get('org'),
            international_domain=source.get('international_domain'),
            website_host=source.get('website_host') or source.get('host'),
            
            # ICP备案信息
            icp_unit=source.get('icp_unit') or source.get('icp_company'),
            icp_number=source.get('icp_number') or source.get('icp_license'),
            icp_update_time=self._parse_datetime(source.get('icp_update_time')),
            
            # 网站信息
            website_keywords=source.get('website_keywords') or source.get('keywords'),
            website_status_code=source.get('website_status_code') or source.get('status_code') or source.get('status-code'),
            website_type=source.get('website_type'),
            webpage_title=source.get('webpage_title') or source.get('title') or source.get('web_title'),
            
            # 应用信息
            application_name=source.get('application_name') or source.get('app') or source.get('app_name'),
            application_level=source.get('application_level'),
            application_vendor=source.get('application_vendor') or source.get('vendor'),
            application_category=source.get('application_category') or source.get('category'),
            application_type=source.get('application_type'),
            
            # 服务信息
            service_product=source.get('service_product') or source.get('product'),
            service_raw_response=source.get('service_raw_response') or source.get('banner') or source.get('body') or source.get('data'),
            service_name=source.get('service_name') or source.get('service') or source.get('server'),
            service_version=source.get('service_version') or source.get('version'),
            service_certificate=source.get('service_certificate'),
            
            # 地理位置信息（中文）
            country_cn=source.get('country_cn') or source.get('country_name'),
            province_cn=source.get('province_cn'),
            city_cn=source.get('city_cn'),
            district_cn=source.get('district_cn'),
            
            # 地理位置信息（英文）
            country_en=source.get('country_en') or source.get('country'),
            province_en=source.get('province_en') or source.get('region'),
            city_en=source.get('city_en') or source.get('city'),
            district_en=source.get('district_en'),
            
            # 运营商和证书信息
            isp=source.get('isp'),
            certificate_subject=source.get('certificate_subject') or source.get('cert_subject') or source.get('tls_subject'),
            certificate_subject_country=source.get('certificate_subject_country'),
            certificate_subject_organization=source.get('certificate_subject_organization'),
            certificate_subject_common_name=source.get('certificate_subject_common_name'),
            certificate_issuer=source.get('certificate_issuer') or source.get('cert_issuer') or source.get('tls_issuer'),
            certificate_serial_number=source.get('certificate_serial_number') or source.get('cert_serial'),
            
            # 原有字段
            confidence=float(confidence),
            status=source.get('status', 'discovered'),
            source=source.get('scanner_type', 'scanner'),
            source_task_id=source.get('task_id'),
            source_task_type=source.get('scanner_type'),
            platform_id=source.get('platform_id'),
            project_id=source.get('project_id'),
            discovered_at=self._parse_datetime(source.get('discovered_at')),
            tags=source.get('tags', []),
            metadata=source.get('raw_data', source)  # 保存原始数据
        )
        
        # 处理嵌套的地理位置数据（如Quake格式）
        if 'location' in source and isinstance(source['location'], dict):
            location = source['location']
            if not asset.country_cn:
                asset.country_cn = location.get('country_cn')
            if not asset.country_en:
                asset.country_en = location.get('country_en')
            if not asset.province_cn:
                asset.province_cn = location.get('province_cn')
            if not asset.province_en:
                asset.province_en = location.get('province_en')
            if not asset.city_cn:
                asset.city_cn = location.get('city_cn')
            if not asset.city_en:
                asset.city_en = location.get('city_en')
            if not asset.district_cn:
                asset.district_cn = location.get('district_cn')
            if not asset.district_en:
                asset.district_en = location.get('district_en')
            if not asset.isp:
                asset.isp = location.get('isp')
        
        # 处理嵌套的服务数据
        if 'service' in source and isinstance(source['service'], dict):
            service = source['service']
            if not asset.service_name:
                asset.service_name = service.get('name')
            if not asset.service_product:
                asset.service_product = service.get('product')
            if not asset.service_version:
                asset.service_version = service.get('version')
            if not asset.service_raw_response:
                asset.service_raw_response = service.get('banner')
            
            # 处理HTTP信息
            if 'http' in service and isinstance(service['http'], dict):
                http = service['http']
                if not asset.webpage_title:
                    asset.webpage_title = http.get('title')
                if not asset.website_status_code:
                    asset.website_status_code = http.get('status_code')
                if not asset.website_host:
                    asset.website_host = http.get('host')
            
            # 处理证书信息
            if 'cert' in service and isinstance(service['cert'], dict):
                cert = service['cert']
                if not asset.certificate_subject:
                    asset.certificate_subject = cert.get('subject')
                if not asset.certificate_issuer:
                    asset.certificate_issuer = cert.get('issuer')
                if not asset.certificate_serial_number:
                    asset.certificate_serial_number = cert.get('serial')
        
        # 处理深层嵌套的TLS证书信息
        if 'service' in source and isinstance(source['service'], dict):
            service = source['service']
            
            # 处理TLS证书信息（Quake格式）
            if 'tls' in service and isinstance(service['tls'], dict):
                tls = service['tls']
                if 'handshake_log' in tls and isinstance(tls['handshake_log'], dict):
                    handshake = tls['handshake_log']
                    if 'server_certificates' in handshake and isinstance(handshake['server_certificates'], dict):
                        cert_info = handshake['server_certificates']
                        if 'certificate' in cert_info and isinstance(cert_info['certificate'], dict):
                            cert = cert_info['certificate']
                            if 'parsed' in cert and isinstance(cert['parsed'], dict):
                                parsed_cert = cert['parsed']
                                
                                if not asset.certificate_subject:
                                    asset.certificate_subject = parsed_cert.get('subject_dn')
                                if not asset.certificate_issuer:
                                    asset.certificate_issuer = parsed_cert.get('issuer_dn')
                                if not asset.certificate_serial_number:
                                    asset.certificate_serial_number = str(parsed_cert.get('serial_number', ''))
                                
                                # 提取通用名
                                if 'subject' in parsed_cert and isinstance(parsed_cert['subject'], dict):
                                    subject = parsed_cert['subject']
                                    if 'common_name' in subject and isinstance(subject['common_name'], list):
                                        if subject['common_name'] and not asset.certificate_subject_common_name:
                                            asset.certificate_subject_common_name = subject['common_name'][0]
        
        # 处理组件信息（如Quake的components）
        if 'components' in source and isinstance(source['components'], list) and source['components']:
            component = source['components'][0]  # 取第一个组件
            if isinstance(component, dict):
                if not asset.application_name:
                    asset.application_name = component.get('product_name_cn') or component.get('product_name')
                if not asset.application_vendor:
                    asset.application_vendor = component.get('product_vendor')
                if not asset.application_level:
                    asset.application_level = component.get('product_level')
                if not asset.application_category:
                    # 处理数组格式的catalog
                    catalog = component.get('product_catalog')
                    if isinstance(catalog, list) and catalog:
                        asset.application_category = ', '.join(catalog)
                    elif isinstance(catalog, str):
                        asset.application_category = catalog
                if not asset.application_type:
                    # 处理数组格式的type
                    app_type = component.get('product_type')
                    if isinstance(app_type, list) and app_type:
                        asset.application_type = ', '.join(app_type)
                    elif isinstance(app_type, str):
                        asset.application_type = app_type
        
        asset.fingerprint_hash = asset.generate_fingerprint()
        return asset
    
    def _normalize_enhanced_asset(self, source: Dict[str, Any]) -> UnifiedAsset:
        """标准化enhanced_asset数据"""
        entity_data = source.get('entity_data', {})
        
        asset = UnifiedAsset(
            asset_id=source.get('id', f"enhanced_{hash(str(source))}"),
            fingerprint_hash="",  # 将被重新计算
            asset_type=entity_data.get('asset_type', 'unknown'),
            asset_value=entity_data.get('asset_value', ''),
            asset_host=entity_data.get('asset_host'),
            asset_port=entity_data.get('asset_port'),
            asset_service=entity_data.get('asset_service'),
            confidence=self._normalize_confidence(entity_data.get('confidence', 'medium')),
            status=entity_data.get('status', 'discovered'),
            source=entity_data.get('source_task_type', 'enhanced'),
            source_task_id=entity_data.get('source_task_id'),
            source_task_type=entity_data.get('source_task_type'),
            platform_id=entity_data.get('platform_id'),
            project_id=entity_data.get('project_id'),
            discovered_at=self._parse_datetime(entity_data.get('discovered_at')),
            tags=entity_data.get('tags', []),
            metadata=entity_data.get('metadata', {})
        )
        
        asset.fingerprint_hash = asset.generate_fingerprint()
        return asset
    
    def _normalize_unified_asset(self, source: Dict[str, Any]) -> UnifiedAsset:
        """标准化unified-assets数据"""
        asset = UnifiedAsset(
            asset_id=source.get('asset_id', f"unified_{hash(str(source))}"),
            fingerprint_hash=source.get('fingerprint_hash', ''),
            asset_type=source.get('asset_type', 'unknown'),
            asset_value=source.get('asset_value', ''),
            asset_host=source.get('asset_host'),
            asset_port=source.get('asset_port'),
            asset_service=source.get('asset_service'),
            confidence=float(source.get('confidence', 0.5)),
            status=source.get('status', 'discovered'),
            source=source.get('source', 'unified'),
            platform_id=source.get('platform_id'),
            project_id=source.get('project_id'),
            discovered_at=self._parse_datetime(source.get('discovered_at')),
            tags=source.get('tags', []),
            metadata=source.get('metadata', {})
        )
        
        if not asset.fingerprint_hash:
            asset.fingerprint_hash = asset.generate_fingerprint()
        return asset
    
    def _normalize_legacy_asset(self, source: Dict[str, Any]) -> UnifiedAsset:
        """标准化legacy assets数据"""
        asset = UnifiedAsset(
            asset_id=source.get('id', f"legacy_{hash(str(source))}"),
            fingerprint_hash="",
            asset_type=source.get('type', 'unknown'),
            asset_value=source.get('value', ''),
            confidence=0.5,
            status='discovered',
            source='legacy',
            discovered_at=self._parse_datetime(source.get('created_at')),
            tags=source.get('tags', []),
            metadata=source
        )
        
        asset.fingerprint_hash = asset.generate_fingerprint()
        return asset
    
    def _normalize_generic_asset(self, source: Dict[str, Any]) -> UnifiedAsset:
        """标准化通用资产数据"""
        # 智能字段匹配
        asset_value = (
            source.get('asset_value') or 
            source.get('value') or 
            source.get('domain') or 
            source.get('ip') or 
            source.get('url') or 
            str(source)[:100]
        )
        
        asset_type = (
            source.get('asset_type') or 
            source.get('type') or 
            self._guess_asset_type(asset_value)
        )
        
        asset = UnifiedAsset(
            asset_id=f"generic_{hash(str(source))}",
            fingerprint_hash="",
            asset_type=asset_type,
            asset_value=asset_value,
            confidence=0.3,  # 通用数据置信度较低
            status='discovered',
            source='generic',
            discovered_at=datetime.utcnow(),
            metadata=source
        )
        
        asset.fingerprint_hash = asset.generate_fingerprint()
        return asset
    
    def _normalize_confidence(self, confidence_str: str) -> float:
        """将置信度字符串转换为数值"""
        confidence_map = {
            'low': 0.3,
            'medium': 0.6,
            'high': 0.9,
            'critical': 1.0
        }
        
        if isinstance(confidence_str, (int, float)):
            return float(confidence_str)
        
        return confidence_map.get(str(confidence_str).lower(), 0.5)
    
    def _parse_datetime(self, dt_str: Any) -> datetime:
        """解析各种格式的时间字符串"""
        if not dt_str:
            return datetime.utcnow()
        
        if isinstance(dt_str, datetime):
            return dt_str
        
        try:
            # 尝试多种时间格式
            for fmt in [
                '%Y-%m-%dT%H:%M:%S.%fZ',
                '%Y-%m-%dT%H:%M:%S.%f',
                '%Y-%m-%dT%H:%M:%SZ',
                '%Y-%m-%dT%H:%M:%S',
                '%Y-%m-%d %H:%M:%S'
            ]:
                try:
                    return datetime.strptime(str(dt_str), fmt)
                except ValueError:
                    continue
                    
            # 如果都失败了，返回当前时间
            return datetime.utcnow()
            
        except Exception:
            return datetime.utcnow()
    
    def _guess_asset_type(self, value: str) -> str:
        """根据值猜测资产类型"""
        if not value:
            return 'unknown'
        
        value = str(value).strip()
        
        # IP地址
        if self._is_ip(value):
            return 'ip'
        
        # 域名
        if '.' in value and not value.startswith('http'):
            return 'domain' if value.count('.') <= 2 else 'subdomain'
        
        # URL
        if value.startswith(('http://', 'https://')):
            return 'url'
        
        # 端口
        if ':' in value and value.split(':')[-1].isdigit():
            return 'port'
        
        return 'unknown'
    
    def _is_ip(self, value: str) -> bool:
        """检查是否为IP地址"""
        try:
            parts = value.split('.')
            return len(parts) == 4 and all(0 <= int(part) <= 255 for part in parts)
        except:
            return False
    
    async def deduplicate_assets(self, assets: List[UnifiedAsset]) -> List[UnifiedAsset]:
        """资产去重和合并"""
        fingerprint_groups = {}
        
        # 按指纹分组
        for asset in assets:
            fingerprint = asset.fingerprint_hash
            if fingerprint not in fingerprint_groups:
                fingerprint_groups[fingerprint] = []
            fingerprint_groups[fingerprint].append(asset)
        
        deduplicated_assets = []
        
        for fingerprint, group_assets in fingerprint_groups.items():
            if len(group_assets) == 1:
                deduplicated_assets.append(group_assets[0])
            else:
                # 合并多个相同资产
                merged_asset = self._merge_assets(group_assets)
                deduplicated_assets.append(merged_asset)
        
        return deduplicated_assets
    
    def _merge_assets(self, assets: List[UnifiedAsset]) -> UnifiedAsset:
        """合并多个相同的资产"""
        # 选择置信度最高的作为基础
        base_asset = max(assets, key=lambda a: a.confidence)
        
        # 合并标签
        all_tags = set()
        for asset in assets:
            all_tags.update(asset.tags)
        base_asset.tags = list(all_tags)
        
        # 合并元数据
        for asset in assets:
            base_asset.metadata.update(asset.metadata)
        
        # 使用最新的时间戳
        base_asset.updated_at = max(asset.updated_at for asset in assets)
        
        # 如果有验证状态，优先使用
        verified_assets = [a for a in assets if a.status in ['verified', 'validated']]
        if verified_assets:
            base_asset.status = verified_assets[0].status
            base_asset.verified_at = verified_assets[0].verified_at or datetime.utcnow()
        
        return base_asset
    
    async def save_to_unified_index(self, assets: List[UnifiedAsset]):
        """保存到统一ES索引"""
        if not assets:
            return
        
        actions = []
        for asset in assets:
            action = {
                "_index": self.unified_index,
                "_id": asset.asset_id,
                "_source": asdict(asset)
            }
            actions.append(action)
        
        try:
            from elasticsearch.helpers import async_bulk
            success, failed = await async_bulk(self.es_client, actions)
            print(f"Saved {success} assets to unified index, {len(failed)} failed")
        except Exception as e:
            print(f"Error saving to unified index: {e}")
    
    async def sync_to_postgresql(self, assets: List[UnifiedAsset]):
        """同步到PostgreSQL"""
        if not assets:
            return
            
        # 确保资产表存在
        await self._ensure_assets_table()
        
        for asset in assets:
            try:
                # 检查是否已存在
                existing_query = text("""
                    SELECT id FROM unified_assets 
                    WHERE fingerprint_hash = :fingerprint_hash
                """)
                
                result = await self.db.execute(existing_query, {
                    "fingerprint_hash": asset.fingerprint_hash
                })
                
                existing = result.scalar_one_or_none()
                
                if existing:
                    # 更新现有资产
                    update_query = text("""
                        UPDATE unified_assets SET
                            asset_type = :asset_type,
                            asset_value = :asset_value,
                            asset_host = :asset_host,
                            asset_port = :asset_port,
                            asset_service = :asset_service,
                            confidence = :confidence,
                            status = :status,
                            source = :source,
                            platform_id = :platform_id,
                            project_id = :project_id,
                            verified_at = :verified_at,
                            updated_at = :updated_at,
                            tags = :tags,
                            metadata = :metadata
                        WHERE fingerprint_hash = :fingerprint_hash
                    """)
                    
                    await self.db.execute(update_query, {
                        "fingerprint_hash": asset.fingerprint_hash,
                        "asset_type": asset.asset_type,
                        "asset_value": asset.asset_value,
                        "asset_host": asset.asset_host,
                        "asset_port": asset.asset_port,
                        "asset_service": asset.asset_service,
                        "confidence": asset.confidence,
                        "status": asset.status,
                        "source": asset.source,
                        "platform_id": asset.platform_id,
                        "project_id": asset.project_id,
                        "verified_at": asset.verified_at,
                        "updated_at": asset.updated_at,
                        "tags": json.dumps(asset.tags),
                        "metadata": json.dumps(asset.metadata)
                    })
                else:
                    # 插入新资产
                    insert_query = text("""
                        INSERT INTO unified_assets (
                            asset_id, fingerprint_hash, asset_type, asset_value,
                            asset_host, asset_port, asset_service, confidence,
                            status, source, source_task_id, source_task_type,
                            platform_id, project_id, discovered_at, verified_at,
                            updated_at, tags, metadata, parent_asset_id
                        ) VALUES (
                            :asset_id, :fingerprint_hash, :asset_type, :asset_value,
                            :asset_host, :asset_port, :asset_service, :confidence,
                            :status, :source, :source_task_id, :source_task_type,
                            :platform_id, :project_id, :discovered_at, :verified_at,
                            :updated_at, :tags, :metadata, :parent_asset_id
                        )
                    """)
                    
                    await self.db.execute(insert_query, {
                        "asset_id": asset.asset_id,
                        "fingerprint_hash": asset.fingerprint_hash,
                        "asset_type": asset.asset_type,
                        "asset_value": asset.asset_value,
                        "asset_host": asset.asset_host,
                        "asset_port": asset.asset_port,
                        "asset_service": asset.asset_service,
                        "confidence": asset.confidence,
                        "status": asset.status,
                        "source": asset.source,
                        "source_task_id": asset.source_task_id,
                        "source_task_type": asset.source_task_type,
                        "platform_id": asset.platform_id,
                        "project_id": asset.project_id,
                        "discovered_at": asset.discovered_at,
                        "verified_at": asset.verified_at,
                        "updated_at": asset.updated_at,
                        "tags": json.dumps(asset.tags),
                        "metadata": json.dumps(asset.metadata),
                        "parent_asset_id": asset.parent_asset_id
                    })
                
            except Exception as e:
                print(f"Error syncing asset {asset.asset_id} to PostgreSQL: {e}")
                continue
        
        await self.db.commit()
        print(f"Synced {len(assets)} assets to PostgreSQL")
    
    async def _ensure_assets_table(self):
        """确保统一资产表存在"""
        # 分别执行每个SQL语句
        statements = [
            """
            CREATE TABLE IF NOT EXISTS unified_assets (
                id SERIAL PRIMARY KEY,
                asset_id VARCHAR(255) UNIQUE NOT NULL,
                fingerprint_hash VARCHAR(64) UNIQUE NOT NULL,
                asset_type VARCHAR(50) NOT NULL,
                asset_value TEXT NOT NULL,
                
                -- 基础网络信息
                asset_host VARCHAR(255),
                asset_port INTEGER,
                asset_service VARCHAR(100),
                ip_address INET,
                url TEXT,
                transport_protocol VARCHAR(20),
                scan_time TIMESTAMP WITH TIME ZONE,
                
                -- 网络路径和域名信息
                website_path TEXT,
                asn_number VARCHAR(20),
                asn_organization TEXT,
                international_domain VARCHAR(255),
                website_host VARCHAR(255),
                
                -- ICP备案信息
                icp_unit TEXT,
                icp_number VARCHAR(50),
                icp_update_time TIMESTAMP WITH TIME ZONE,
                
                -- 网站信息
                website_keywords TEXT,
                website_status_code INTEGER,
                website_type VARCHAR(50),
                webpage_title TEXT,
                
                -- 应用信息
                application_name TEXT,
                application_level VARCHAR(50),
                application_vendor TEXT,
                application_category VARCHAR(50),
                application_type VARCHAR(50),
                
                -- 服务信息
                service_product TEXT,
                service_raw_response TEXT,
                service_name VARCHAR(100),
                service_version VARCHAR(100),
                service_certificate TEXT,
                
                -- 地理位置信息（中文）
                country_cn VARCHAR(50),
                province_cn VARCHAR(50),
                city_cn VARCHAR(50),
                district_cn VARCHAR(50),
                
                -- 地理位置信息（英文）
                country_en VARCHAR(50),
                province_en VARCHAR(50),
                city_en VARCHAR(50),
                district_en VARCHAR(50),
                
                -- 运营商和证书信息
                isp TEXT,
                certificate_subject TEXT,
                certificate_subject_country VARCHAR(50),
                certificate_subject_organization TEXT,
                certificate_subject_common_name VARCHAR(255),
                certificate_issuer TEXT,
                certificate_serial_number VARCHAR(100),
                
                -- 原有字段
                confidence FLOAT DEFAULT 0.5,
                status VARCHAR(50) DEFAULT 'discovered',
                source VARCHAR(100),
                source_task_id VARCHAR(255),
                source_task_type VARCHAR(100),
                platform_id VARCHAR(255),
                project_id VARCHAR(255),
                discovered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                verified_at TIMESTAMP WITH TIME ZONE,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                tags JSONB,
                metadata JSONB,
                parent_asset_id VARCHAR(255),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
            """,
            "CREATE INDEX IF NOT EXISTS idx_unified_assets_fingerprint ON unified_assets(fingerprint_hash)",
            "CREATE INDEX IF NOT EXISTS idx_unified_assets_type ON unified_assets(asset_type)",
            "CREATE INDEX IF NOT EXISTS idx_unified_assets_value ON unified_assets(asset_value)",
            "CREATE INDEX IF NOT EXISTS idx_unified_assets_status ON unified_assets(status)",
            "CREATE INDEX IF NOT EXISTS idx_unified_assets_platform ON unified_assets(platform_id)",
            "CREATE INDEX IF NOT EXISTS idx_unified_assets_ip ON unified_assets(ip_address)",
            "CREATE INDEX IF NOT EXISTS idx_unified_assets_host ON unified_assets(asset_host)",
            "CREATE INDEX IF NOT EXISTS idx_unified_assets_port ON unified_assets(asset_port)",
            "CREATE INDEX IF NOT EXISTS idx_unified_assets_country_cn ON unified_assets(country_cn)",
            "CREATE INDEX IF NOT EXISTS idx_unified_assets_city_cn ON unified_assets(city_cn)",
            "CREATE INDEX IF NOT EXISTS idx_unified_assets_application ON unified_assets(application_name)",
            "CREATE INDEX IF NOT EXISTS idx_unified_assets_service ON unified_assets(service_name)",
            "CREATE INDEX IF NOT EXISTS idx_unified_assets_icp ON unified_assets(icp_number)",
            "CREATE INDEX IF NOT EXISTS idx_unified_assets_tags ON unified_assets USING GIN(tags)"
        ]
        
        for stmt in statements:
            await self.db.execute(text(stmt))
        
        await self.db.commit()
    
    async def run_full_aggregation(self):
        """运行完整的资产聚合流程"""
        print("Starting asset aggregation pipeline...")
        
        # 1. 确保索引和表存在
        await self.ensure_unified_index()
        
        # 2. 收集原始资产数据
        raw_assets = await self.collect_raw_assets()
        print(f"Collected {len(raw_assets)} raw assets")
        
        # 3. 标准化资产数据
        normalized_assets = []
        for raw_asset in raw_assets:
            normalized = self.normalize_asset(raw_asset)
            if normalized:
                normalized_assets.append(normalized)
        
        print(f"Normalized {len(normalized_assets)} assets")
        
        # 4. 去重和合并
        deduplicated_assets = await self.deduplicate_assets(normalized_assets)
        print(f"After deduplication: {len(deduplicated_assets)} assets")
        
        # 5. 保存到统一ES索引
        await self.save_to_unified_index(deduplicated_assets)
        
        # 6. 同步到PostgreSQL
        await self.sync_to_postgresql(deduplicated_assets)
        
        print("Asset aggregation pipeline completed!")
        return len(deduplicated_assets)


# API端点和调度任务
async def run_asset_aggregation():
    """运行资产聚合任务"""
    from database import AsyncSessionLocal
    from elasticsearch_client import get_es_client
    
    async with AsyncSessionLocal() as db:
        es_client = await get_es_client()
        pipeline = AssetAggregationPipeline(es_client, db)
        return await pipeline.run_full_aggregation()


if __name__ == "__main__":
    # 测试运行
    asyncio.run(run_asset_aggregation())