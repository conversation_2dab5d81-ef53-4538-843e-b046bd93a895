#!/usr/bin/env python3
"""
Debug script to test the stats endpoint and see detailed error messages
"""

import requests
import json

def test_stats_endpoint():
    # Login and get token
    login_data = {'username': 'admin', 'password': 'password'}
    response = requests.post('http://localhost:8000/api/auth/login', json=login_data)
    
    if response.status_code == 200:
        token = response.json().get('access_token')
        headers = {'Authorization': f'Bearer {token}'}
        
        # Test stats endpoint and get detailed error
        response = requests.get('http://localhost:8000/api/unified-assets/stats', headers=headers)
        print(f'Status code: {response.status_code}')
        print(f'Response: {response.text}')
        
        if response.status_code != 200:
            try:
                error_detail = response.json()
                print(f'Error detail: {json.dumps(error_detail, indent=2)}')
            except:
                print('Could not parse error as JSO<PERSON>')
    else:
        print('<PERSON>gin failed')

if __name__ == "__main__":
    test_stats_endpoint()