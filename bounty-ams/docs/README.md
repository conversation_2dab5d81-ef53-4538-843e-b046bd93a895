# Bounty AMS 项目文档

## 📋 目录

### 🏗️ 架构文档
- [系统架构概览](./architecture/system-overview.md)
- [前端架构](./architecture/frontend-architecture.md)
- [后端架构](./architecture/backend-architecture.md)
- [数据库设计](./architecture/database-design.md)
- [性能优化](./architecture/performance-optimization.md)

### 🔌 API 文档
- [API 概览](./api/overview.md)
- [认证与授权](./api/authentication.md)
- [资产管理 API](./api/asset-management.md)
- [用户管理 API](./api/user-management.md)
- [Agent 管理 API](./api/agent-management.md)
- [搜索分析 API](./api/search-analytics.md)

### 🚀 部署指南
- [环境要求](./deployment/requirements.md)
- [Docker 部署](./deployment/docker-deployment.md)
- [生产环境部署](./deployment/production-deployment.md)
- [监控与日志](./deployment/monitoring.md)

### 👥 用户指南
- [快速开始](./user-guide/quick-start.md)
- [用户管理](./user-guide/user-management.md)
- [资产管理](./user-guide/asset-management.md)
- [平台项目管理](./user-guide/platform-project-management.md)
- [搜索与分析](./user-guide/search-analytics.md)
- [Agent 配置](./user-guide/agent-configuration.md)

### 💻 开发指南
- [开发环境搭建](./development/setup.md)
- [代码规范](./development/coding-standards.md)
- [测试指南](./development/testing.md)
- [贡献指南](./development/contributing.md)
- [故障排除](./development/troubleshooting.md)

## 🎯 项目概述

Bounty AMS (Asset Management System) 是一个现代化的资产管理系统，专为安全研究和漏洞赏金项目设计。

### 核心功能

- **🔍 资产发现与管理**: 自动化资产发现、分类和管理
- **👥 用户权限管理**: 基于角色的访问控制系统
- **🤖 Agent 系统**: 分布式 Agent 架构支持多种扫描任务
- **📊 数据分析**: 强大的搜索和分析功能
- **🔄 工作流管理**: 自动化工作流和任务调度
- **📈 仪表板**: 实时数据可视化和统计

### 技术栈

**前端**:
- React 18 + TypeScript
- Ant Design UI 组件库
- Vite 构建工具
- React Router 路由管理
- Axios HTTP 客户端

**后端**:
- FastAPI (Python)
- PostgreSQL 数据库
- SQLAlchemy ORM
- Elasticsearch 搜索引擎
- Redis 缓存

**部署**:
- Docker & Docker Compose
- Nginx 反向代理
- 支持 Kubernetes

### 系统特性

- **🚀 高性能**: 前端代码分割、懒加载，后端异步处理
- **🔒 安全**: JWT 认证、RBAC 权限控制、API 限流
- **📱 响应式**: 支持桌面和移动设备
- **🌐 国际化**: 支持中英文界面
- **🔧 可扩展**: 模块化设计，易于扩展新功能

## 🚀 快速开始

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd bounty-ams
   ```

2. **启动开发环境**
   ```bash
   # 使用 Docker Compose
   docker-compose up -d
   
   # 或手动启动
   cd backend && python -m uvicorn main:app --reload
   cd frontend && npm run dev
   ```

3. **访问应用**
   - 前端: http://localhost:5173
   - 后端 API: http://localhost:8000
   - API 文档: http://localhost:8000/docs

## 📞 支持与联系

- 📧 邮箱: <EMAIL>
- 📖 文档: [在线文档](https://docs.bounty-ams.com)
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-org/bounty-ams/issues)

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../LICENSE) 文件了解详情。

---

**最后更新**: 2025-07-16
**版本**: v1.0.0
