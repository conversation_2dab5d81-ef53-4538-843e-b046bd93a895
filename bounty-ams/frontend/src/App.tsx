import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AuthProvider } from './context/AuthContext';
import ErrorBoundary from './components/ErrorBoundary';

// 基础组件
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import MainLayout from './components/Layout/MainLayout';
import BasicAssets from './pages/BasicAssets';

// 所有页面组件
import Tasks from './pages/Tasks';
import PlatformProjectManager from './pages/PlatformProjectManager';
import Agents from './pages/Agents';
import Models from './pages/Models';
import Workflows from './pages/Workflows';
import DataImport from './pages/DataImport';
import SearchAnalytics from './pages/SearchAnalytics';
import UserManagement from './pages/UserManagement';
import Profile from './pages/Profile';
import Settings from './pages/Settings';

const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <ErrorBoundary>
        <AuthProvider>
          <Router>
            <Routes>
              {/* 登录路由 */}
              <Route path="/login" element={<Login />} />

              {/* 主应用路由 - 使用MainLayout包装 */}
              <Route path="/" element={<MainLayout />}>
                <Route index element={<Dashboard />} />
                <Route path="dashboard" element={<Dashboard />} />
                <Route path="platform-project" element={<PlatformProjectManager />} />
                <Route path="assets" element={<BasicAssets />} />
                <Route path="agents" element={<Agents />} />
                <Route path="tasks" element={<Tasks />} />
                <Route path="models" element={<Models />} />
                <Route path="workflows" element={<Workflows />} />
                <Route path="data-import" element={<DataImport />} />
                <Route path="search-analytics" element={<SearchAnalytics />} />
                <Route path="user-management" element={<UserManagement />} />
                <Route path="profile" element={<Profile />} />
                <Route path="settings" element={<Settings />} />
              </Route>
            </Routes>
          </Router>
        </AuthProvider>
      </ErrorBoundary>
    </ConfigProvider>
  );
};

export default App;
