import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider, Spin } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AuthProvider } from './context/AuthContext';
import ErrorBoundary from './components/ErrorBoundary';
import LoadingSkeleton from './components/LoadingSkeleton';

// 基础组件 - 立即加载
import Login from './pages/Login';
import MainLayout from './components/Layout/MainLayout';

// 懒加载页面组件
const Dashboard = React.lazy(() => import('./pages/Dashboard'));
const BasicAssets = React.lazy(() => import('./pages/BasicAssets'));
const Tasks = React.lazy(() => import('./pages/Tasks'));
const PlatformProjectManager = React.lazy(() => import('./pages/PlatformProjectManager'));
const Agents = React.lazy(() => import('./pages/Agents'));
const Models = React.lazy(() => import('./pages/Models'));
const Workflows = React.lazy(() => import('./pages/Workflows'));
const DataImport = React.lazy(() => import('./pages/DataImport'));
const SearchAnalytics = React.lazy(() => import('./pages/SearchAnalytics'));
const UserManagement = React.lazy(() => import('./pages/UserManagement'));
const Profile = React.lazy(() => import('./pages/Profile'));
const Settings = React.lazy(() => import('./pages/Settings'));

// 页面加载组件
const PageLoader = () => (
  <div style={{ padding: '20px', textAlign: 'center' }}>
    <LoadingSkeleton type="dashboard" />
  </div>
);

const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <ErrorBoundary>
        <AuthProvider>
          <Router>
            <Routes>
              {/* 登录路由 */}
              <Route path="/login" element={<Login />} />

              {/* 主应用路由 - 使用MainLayout包装 */}
              <Route path="/" element={<MainLayout />}>
                <Route
                  index
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <Dashboard />
                    </Suspense>
                  }
                />
                <Route
                  path="dashboard"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <Dashboard />
                    </Suspense>
                  }
                />
                <Route
                  path="platform-project"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <PlatformProjectManager />
                    </Suspense>
                  }
                />
                <Route
                  path="assets"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <BasicAssets />
                    </Suspense>
                  }
                />
                <Route
                  path="agents"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <Agents />
                    </Suspense>
                  }
                />
                <Route
                  path="tasks"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <Tasks />
                    </Suspense>
                  }
                />
                <Route
                  path="models"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <Models />
                    </Suspense>
                  }
                />
                <Route
                  path="workflows"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <Workflows />
                    </Suspense>
                  }
                />
                <Route
                  path="data-import"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <DataImport />
                    </Suspense>
                  }
                />
                <Route
                  path="search-analytics"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <SearchAnalytics />
                    </Suspense>
                  }
                />
                <Route
                  path="user-management"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <UserManagement />
                    </Suspense>
                  }
                />
                <Route
                  path="profile"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <Profile />
                    </Suspense>
                  }
                />
                <Route
                  path="settings"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <Settings />
                    </Suspense>
                  }
                />
              </Route>
            </Routes>
          </Router>
        </AuthProvider>
      </ErrorBoundary>
    </ConfigProvider>
  );
};

export default App;
