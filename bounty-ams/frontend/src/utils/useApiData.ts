import { useEffect, useRef, useState } from 'react';
import { apiRetry } from './apiRetry';

export interface UseApiDataOptions {
  // 是否在组件挂载时立即加载
  immediate?: boolean;
  // 依赖项数组
  deps?: any[];
  // 重试次数
  retries?: number;
  // 缓存键
  cacheKey?: string;
  // 是否在路由切换时重新加载
  reloadOnRouteChange?: boolean;
}

export function useApiData<T>(
  apiCall: () => Promise<{ data: T }>,
  options: UseApiDataOptions = {}
) {
  const {
    immediate = true,
    deps = [],
    retries = 3,
    cacheKey,
    reloadOnRouteChange = false
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const mounted = useRef(true);
  const loadingRef = useRef(false);

  const loadData = async (force = false) => {
    // 防止重复加载
    if (loadingRef.current && !force) {
      console.log('数据正在加载中，跳过重复请求');
      return;
    }

    setLoading(true);
    setError(null);
    loadingRef.current = true;

    try {
      const apiKey = cacheKey || `api_${Date.now()}`;
      const response = await apiRetry.executeWithRetry(
        apiKey,
        apiCall,
        retries
      );

      // 只有组件仍然挂载时才更新状态
      if (mounted.current) {
        setData(response.data);
        console.log('数据加载成功:', apiKey);
      }
    } catch (err) {
      const error = err as Error;
      console.error('数据加载失败:', error);
      
      if (mounted.current) {
        setError(error);
      }
    } finally {
      if (mounted.current) {
        setLoading(false);
      }
      loadingRef.current = false;
    }
  };

  const reload = () => loadData(true);

  useEffect(() => {
    mounted.current = true;
    
    if (immediate) {
      loadData();
    }

    return () => {
      mounted.current = false;
      loadingRef.current = false;
    };
  }, [immediate, ...deps]);

  // 路由变化时重新加载
  useEffect(() => {
    if (reloadOnRouteChange && data !== null) {
      loadData(true);
    }
  }, [window.location.pathname]);

  return {
    data,
    loading,
    error,
    reload,
    isLoading: loadingRef.current
  };
}

// 简化版本的 hook，用于基本的 API 调用
export function useSimpleApi<T>(
  apiCall: () => Promise<{ data: T }>,
  deps: any[] = []
) {
  return useApiData(apiCall, {
    immediate: true,
    deps,
    retries: 2
  });
}