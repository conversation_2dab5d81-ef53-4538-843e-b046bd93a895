// API 重试机制
export class ApiRetryHandler {
  private static instance: ApiRetryHandler;
  private retryCount = new Map<string, number>();
  private maxRetries = 3;
  private retryDelay = 1000; // 1秒

  static getInstance(): ApiRetryHandler {
    if (!ApiRetryHandler.instance) {
      ApiRetryHandler.instance = new ApiRetryHandler();
    }
    return ApiRetryHandler.instance;
  }

  async executeWithRetry<T>(
    key: string, 
    apiCall: () => Promise<T>, 
    maxRetries = this.maxRetries
  ): Promise<T> {
    const currentRetry = this.retryCount.get(key) || 0;
    
    try {
      const result = await apiCall();
      // 成功后重置重试计数
      this.retryCount.delete(key);
      return result;
    } catch (error: any) {
      console.warn(`API调用失败 (${key}), 重试次数: ${currentRetry}/${maxRetries}`, error);
      
      // 检查是否应该重试
      if (currentRetry < maxRetries && this.shouldRetry(error)) {
        this.retryCount.set(key, currentRetry + 1);
        
        // 延迟后重试
        await this.delay(this.retryDelay * (currentRetry + 1));
        return this.executeWithRetry(key, apiCall, maxRetries);
      }
      
      // 重试失败，清理计数并抛出错误
      this.retryCount.delete(key);
      throw error;
    }
  }

  private shouldRetry(error: any): boolean {
    // 网络错误或5xx服务器错误才重试
    return (
      error.code === 'NETWORK_ERROR' ||
      error.code === 'ECONNABORTED' || // 超时
      (error.response && error.response.status >= 500) ||
      !error.response // 网络错误
    );
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 清理重试状态
  clearRetryState(key?: string): void {
    if (key) {
      this.retryCount.delete(key);
    } else {
      this.retryCount.clear();
    }
  }

  // 获取重试状态
  getRetryState(key: string): number {
    return this.retryCount.get(key) || 0;
  }
}

export const apiRetry = ApiRetryHandler.getInstance();