import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Drawer,
  Progress,
  message,
  Badge,
  Modal,
  Popconfirm,
  Form,
  Input,
  DatePicker,
  Alert,
  Tooltip,
  Divider,
  Dropdown,
} from 'antd';
import type { MenuProps } from 'antd';
import {
  EyeOutlined,
  ReloadOutlined,
  DeleteOutlined,
  KeyOutlined,
  PlusOutlined,
  StopOutlined,
  CopyOutlined,
  CheckCircleOutlined,
  SyncOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  DisconnectOutlined,
  WifiOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  PoweroffOutlined,
  RedoOutlined,
  MinusCircleOutlined,
  DownOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { agentService, agentKeyService } from '../services/api';
import LoadingSkeleton from '../components/LoadingSkeleton';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

const Agents: React.FC = () => {
  const [agents, setAgents] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState<any>(null);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [keyModalVisible, setKeyModalVisible] = useState(false);
  const [createKeyModalVisible, setCreateKeyModalVisible] = useState(false);
  const [agentKeys, setAgentKeys] = useState<any[]>([]);
  const [newKeyValue, setNewKeyValue] = useState<string>('');
  const [newKeyVisible, setNewKeyVisible] = useState(false);
  const [globalKeyModalVisible, setGlobalKeyModalVisible] = useState(false);
  const [globalKeys, setGlobalKeys] = useState<any[]>([]);
  const [globalKeyLoading, setGlobalKeyLoading] = useState(false);
  const [form] = Form.useForm();
  const [globalKeyForm] = Form.useForm();

  const loadGlobalKeys = async () => {
    try {
      setGlobalKeyLoading(true);
      const response = await agentKeyService.getKeys();
      setGlobalKeys(response.data);
    } catch (error: any) {
      console.error('Load global keys error:', error);
      message.error('加载密钥失败');
    } finally {
      setGlobalKeyLoading(false);
    }
  };

  useEffect(() => {
    // 添加认证状态调试信息
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    console.log('Auth debug - Token exists:', !!token);
    console.log('Auth debug - User exists:', !!user);
    console.log('Auth debug - Token value:', token?.substring(0, 20) + '...');
    
    loadAgents();
    loadGlobalKeys();
  }, []);

  const loadAgents = async () => {
    try {
      setLoading(true);
      // 使用Promise.race实现快速失败
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('请求超时')), 3000)
      );
      
      const response = await Promise.race([
        agentService.getAgents(),
        timeoutPromise
      ]);
      
      console.log('API Response:', response);
      console.log('Response data:', (response as any).data);
      
      // 从响应中提取agents数组
      const responseData = (response as any).data;
      const agentsData = responseData?.agents || [];
      
      console.log('Parsed agents data:', agentsData);
      console.log('Agents count:', agentsData.length);
      
      setAgents(agentsData);
      
      if (agentsData.length === 0) {
        console.log('No agents found');
      }
    } catch (error: any) {
      console.error('Error loading agents:', error);
      console.error('Error response:', error.response);
      console.error('Error status:', error.response?.status);
      console.error('Error data:', error.response?.data);
      
      if (error.message === '请求超时' || error.code === 'ECONNABORTED') {
        message.warning('连接超时，可能Agent服务未启动');
      } else if (error.response?.status === 401) {
        message.error('认证失败，请重新登录');
      } else {
        message.error(`加载Agent失败: ${error.message || '未知错误'}`);
      }
      // 即使失败也设置空数组，避免页面一直loading
      setAgents([]);
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetails = async (agent: any) => {
    try {
      const response = await agentService.getAgentDetails(agent.agent_id);
      setSelectedAgent(response.data);
      setDrawerVisible(true);
    } catch (error) {
      message.error('加载Agent详情失败');
    }
  };

  const handleDeleteAgent = async (agent: any) => {
    try {
      await agentService.deleteAgent(agent.agent_id);
      message.success(`Agent "${agent.name}" 已删除`);
      loadAgents(); // 重新加载列表
    } catch (error: any) {
      console.error('Delete agent error:', error);
      message.error(`删除Agent失败: ${error.response?.data?.detail || '未知错误'}`);
    }
  };

  const handleControlAgent = async (agent: any, action: string, actionName: string) => {
    try {
      const response = await agentService.sendCommand(agent.agent_id, action);
      const result = response.data;
      
      message.success(`${actionName}任务已创建 (任务ID: ${result.task_id.substring(0, 8)}...)`);
      
      // 添加说明信息
      if (action === 'pause' || action === 'stop' || action === 'restart') {
        message.info(`注意: Agent控制命令已发送，但当前Go Agent版本可能需要重启才能处理控制命令`, 5);
      }
      
      // 短时间后刷新状态
      setTimeout(() => {
        loadAgents();
      }, 2000);
    } catch (error: any) {
      console.error(`${action} agent error:`, error);
      message.error(`${actionName}任务创建失败: ${error.response?.data?.detail || '任务创建失败'}`);
    }
  };

  const handlePauseAgent = (agent: any) => handleControlAgent(agent, 'pause', '暂停');
  const handleResumeAgent = (agent: any) => handleControlAgent(agent, 'resume', '恢复');
  const handleStopAgent = (agent: any) => handleControlAgent(agent, 'stop', '停止');
  const handleRestartAgent = (agent: any) => handleControlAgent(agent, 'restart', '重启');
  const handleCancelTasks = (agent: any) => handleControlAgent(agent, 'cancel_tasks', '取消任务');

  // 获取Agent控制菜单项
  const getAgentControlMenuItems = (agent: any): MenuProps['items'] => {
    if (agent.status === 'online') {
      return [
        {
          key: 'pause',
          icon: <PauseCircleOutlined />,
          label: '暂停',
          onClick: () => handlePauseAgent(agent),
        },
        {
          key: 'cancel_tasks',
          icon: <MinusCircleOutlined />,
          label: '取消任务',
          onClick: () => handleCancelTasks(agent),
        },
        {
          type: 'divider',
        },
        {
          key: 'restart',
          icon: <RedoOutlined />,
          label: '重启',
          onClick: () => handleRestartAgent(agent),
        },
        {
          key: 'stop',
          icon: <PoweroffOutlined />,
          label: '停止',
          danger: true,
          onClick: () => handleStopAgent(agent),
        },
      ];
    } else if (agent.status === 'paused') {
      return [
        {
          key: 'resume',
          icon: <PlayCircleOutlined />,
          label: '恢复',
          onClick: () => handleResumeAgent(agent),
        },
        {
          type: 'divider',
        },
        {
          key: 'restart',
          icon: <RedoOutlined />,
          label: '重启',
          onClick: () => handleRestartAgent(agent),
        },
        {
          key: 'stop',
          icon: <PoweroffOutlined />,
          label: '停止',
          danger: true,
          onClick: () => handleStopAgent(agent),
        },
      ];
    }
    return [];
  };

  const handleManageKeys = async (agent: any) => {
    try {
      setSelectedAgent(agent);
      const response = await agentKeyService.getKeys({ agent_id: agent.agent_id });
      setAgentKeys(response.data);
      setKeyModalVisible(true);
    } catch (error: any) {
      console.error('Load keys error:', error);
      message.error('加载密钥失败');
    }
  };

  const handleCreateKey = async (values: any) => {
    try {
      const keyData = {
        ...values,
        agent_id: selectedAgent?.agent_id,
      };
      const response = await agentKeyService.createKey(keyData);
      setNewKeyValue(response.data.key_value);
      setNewKeyVisible(true);
      
      message.success('密钥创建成功');
      setCreateKeyModalVisible(false);
      form.resetFields();
      
      // 重新加载密钥列表
      const keysResponse = await agentKeyService.getKeys({ agent_id: selectedAgent?.agent_id });
      setAgentKeys(keysResponse.data);
    } catch (error: any) {
      console.error('Create key error:', error);
      message.error(`创建密钥失败: ${error.response?.data?.detail || '未知错误'}`);
    }
  };

  const handleCreateGlobalKey = async (values: any) => {
    try {
      // 创建未绑定的密钥
      const response = await agentKeyService.createKey(values);
      setNewKeyValue(response.data.key_value);
      setNewKeyVisible(true);
      
      message.success('密钥创建成功');
      setGlobalKeyModalVisible(false);
      globalKeyForm.resetFields();
      
      // 重新加载全局密钥列表
      loadGlobalKeys();
    } catch (error: any) {
      console.error('Create global key error:', error);
      message.error(`创建密钥失败: ${error.response?.data?.detail || '未知错误'}`);
    }
  };

  const handleRevokeGlobalKey = async (keyId: string) => {
    try {
      await agentKeyService.revokeKey(keyId);
      message.success('密钥已撤销');
      loadGlobalKeys();
    } catch (error: any) {
      console.error('Revoke global key error:', error);
      message.error(`撤销密钥失败: ${error.response?.data?.detail || '未知错误'}`);
    }
  };

  const handleDeleteGlobalKey = async (keyId: string) => {
    try {
      await agentKeyService.deleteKey(keyId);
      message.success('密钥已删除');
      loadGlobalKeys();
    } catch (error: any) {
      console.error('Delete global key error:', error);
      message.error(`删除密钥失败: ${error.response?.data?.detail || '未知错误'}`);
    }
  };

  const handleRevokeKey = async (keyId: string) => {
    try {
      await agentKeyService.revokeKey(keyId);
      message.success('密钥已撤销');
      
      // 重新加载密钥列表
      const response = await agentKeyService.getKeys({ agent_id: selectedAgent?.agent_id });
      setAgentKeys(response.data);
    } catch (error: any) {
      console.error('Revoke key error:', error);
      message.error(`撤销密钥失败: ${error.response?.data?.detail || '未知错误'}`);
    }
  };

  const handleDeleteKey = async (keyId: string) => {
    try {
      await agentKeyService.deleteKey(keyId);
      message.success('密钥已删除');
      
      // 重新加载密钥列表
      const response = await agentKeyService.getKeys({ agent_id: selectedAgent?.agent_id });
      setAgentKeys(response.data);
    } catch (error: any) {
      console.error('Delete key error:', error);
      message.error(`删除密钥失败: ${error.response?.data?.detail || '未知错误'}`);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    message.success('已复制到剪贴板');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
      case 'active':
        return 'success';
      case 'idle':
        return 'warning';
      case 'busy':
        return 'processing';
      case 'offline':
        return 'error';
      default:
        return 'default';
    }
  };

  const columns = [
    {
      title: 'Agent ID',
      dataIndex: 'agent_id',
      key: 'agent_id',
      render: (id: string) => (
        <Text code>{id}</Text>
      ),
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Badge 
          status={getStatusColor(status) as any}
          text={status}
        />
      ),
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
    },
    {
      title: '主机名',
      dataIndex: 'hostname',
      key: 'hostname',
    },
    {
      title: 'IP地址',
      dataIndex: 'ip_address',
      key: 'ip_address',
    },
    {
      title: '当前任务',
      dataIndex: 'current_tasks',
      key: 'current_tasks',
      render: (current: number, record: any) => (
        <Text>
          {current}/{record.max_concurrent_tasks}
        </Text>
      ),
    },
    {
      title: '最后活跃',
      dataIndex: 'last_seen_at',
      key: 'last_seen_at',
      render: (date: string) => (
        <Text type="secondary">
          {dayjs(date).format('YYYY-MM-DD HH:mm')}
        </Text>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (record: any) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetails(record)}
            title="查看详情"
          >
            详情
          </Button>
          <Button
            type="text"
            icon={<KeyOutlined />}
            onClick={() => handleManageKeys(record)}
            title="管理密钥"
          >
            密钥
          </Button>
          
          {/* Agent控制下拉菜单 */}
          {(record.status === 'online' || record.status === 'paused') && (
            <Dropdown
              menu={{ items: getAgentControlMenuItems(record) }}
              trigger={['click']}
              placement="bottomLeft"
            >
              <Button
                type="text"
                icon={<SettingOutlined />}
                title="Agent控制"
              >
                控制 <DownOutlined />
              </Button>
            </Dropdown>
          )}
          
          <Popconfirm
            title="删除Agent"
            description={
              record.status === 'online' 
                ? `警告：Agent "${record.name}" 当前在线，删除后可能影响正在执行的任务。确定要删除吗？`
                : `确定要删除Agent "${record.name}" 吗？此操作不可恢复。`
            }
            onConfirm={() => handleDeleteAgent(record)}
            okText="确定"
            cancelText="取消"
            okType="danger"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              title={record.status === 'online' ? '删除在线Agent' : '删除Agent'}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Space align="center" style={{ marginBottom: 16 }}>
            <Title level={3} style={{ margin: 0 }}>
              Agent管理
            </Title>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadAgents}
              loading={loading}
            >
              刷新
            </Button>
            <Button
              type="primary"
              icon={<KeyOutlined />}
              onClick={() => setGlobalKeyModalVisible(true)}
            >
              密钥管理
            </Button>
          </Space>
        </Col>
      </Row>

      {/* 密钥管理卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Card title="API密钥管理" size="small">
            <Space style={{ marginBottom: 16 }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setGlobalKeyModalVisible(true)}
              >
                创建密钥
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadGlobalKeys}
                loading={globalKeyLoading}
              >
                刷新密钥
              </Button>
            </Space>
            <Table
              dataSource={globalKeys}
              rowKey="id"
              pagination={false}
              size="small"
              loading={globalKeyLoading}
              columns={[
                {
                  title: '密钥名称',
                  dataIndex: 'name',
                  key: 'name',
                },
                {
                  title: '密钥ID',
                  dataIndex: 'key_id',
                  key: 'key_id',
                  render: (keyId: string) => (
                    <Space>
                      <Text code>{keyId.substring(0, 20)}...</Text>
                      <Button
                        type="text"
                        size="small"
                        icon={<CopyOutlined />}
                        onClick={() => copyToClipboard(keyId)}
                      />
                    </Space>
                  ),
                },
                {
                  title: '绑定状态',
                  dataIndex: 'agent_id',
                  key: 'agent_binding',
                  render: (agentId: string) => (
                    agentId ? (
                      <Badge status="success" text={`已绑定 ${agentId.substring(0, 8)}...`} />
                    ) : (
                      <Badge status="warning" text="未绑定" />
                    )
                  ),
                },
                {
                  title: '状态',
                  dataIndex: 'status',
                  key: 'status',
                  render: (status: string) => {
                    const colors = {
                      active: 'success',
                      suspended: 'warning',
                      revoked: 'error',
                      expired: 'default'
                    };
                    const texts = {
                      active: '活跃',
                      suspended: '暂停',
                      revoked: '已撤销',
                      expired: '已过期'
                    };
                    return (
                      <Badge
                        status={colors[status as keyof typeof colors] as any}
                        text={texts[status as keyof typeof texts]}
                      />
                    );
                  },
                },
                {
                  title: '使用次数',
                  dataIndex: 'usage_count',
                  key: 'usage_count',
                },
                {
                  title: '过期时间',
                  dataIndex: 'expires_at',
                  key: 'expires_at',
                  render: (date?: string) => (
                    date ? (
                      <Text type="secondary">
                        {dayjs(date).format('YYYY-MM-DD')}
                      </Text>
                    ) : (
                      <Text type="secondary">永不过期</Text>
                    )
                  ),
                },
                {
                  title: '操作',
                  key: 'action',
                  render: (record: any) => (
                    <Space>
                      {record.status === 'active' && (
                        <Popconfirm
                          title="撤销密钥"
                          description="确定要撤销此密钥吗？撤销后Agent将无法使用此密钥。"
                          onConfirm={() => handleRevokeGlobalKey(record.key_id)}
                          okText="确定"
                          cancelText="取消"
                          okType="danger"
                        >
                          <Button
                            type="text"
                            danger
                            size="small"
                            icon={<StopOutlined />}
                          >
                            撤销
                          </Button>
                        </Popconfirm>
                      )}
                      <Popconfirm
                        title="删除密钥"
                        description={
                          record.agent_id 
                            ? "警告：此密钥已绑定Agent，删除后Agent将无法连接。确定要删除吗？"
                            : "确定要删除此密钥吗？此操作不可恢复。"
                        }
                        onConfirm={() => handleDeleteGlobalKey(record.key_id)}
                        okText="确定"
                        cancelText="取消"
                        okType="danger"
                      >
                        <Button
                          type="text"
                          danger
                          size="small"
                          icon={<DeleteOutlined />}
                          title={record.agent_id ? "删除已绑定密钥" : "删除密钥"}
                        >
                          删除
                        </Button>
                      </Popconfirm>
                    </Space>
                  ),
                },
              ]}
            />
          </Card>
        </Col>
      </Row>

      <LoadingSkeleton type="table" loading={loading} rows={6}>
        <Card>
          <Table
            columns={columns}
            dataSource={agents}
            loading={false}
            rowKey="agent_id"
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }}
            locale={{
              emptyText: agents.length === 0 ? '暂无Agent数据，可能Agent服务未启动' : '暂无数据'
            }}
          />
        </Card>
      </LoadingSkeleton>

      {/* Agent详情抽屉 */}
      <Drawer
        title="Agent详情"
        width={600}
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
      >
        {selectedAgent && (
          <div>
            <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Text strong>Agent ID:</Text>
                  <br />
                  <Text code>{selectedAgent.agent_id}</Text>
                </Col>
                <Col span={12}>
                  <Text strong>名称:</Text>
                  <br />
                  <Text>{selectedAgent.name}</Text>
                </Col>
                <Col span={12}>
                  <Text strong>状态:</Text>
                  <br />
                  <Badge 
                    status={getStatusColor(selectedAgent.status) as any}
                    text={selectedAgent.status}
                  />
                </Col>
                <Col span={12}>
                  <Text strong>版本:</Text>
                  <br />
                  <Text>{selectedAgent.version}</Text>
                </Col>
                <Col span={12}>
                  <Text strong>主机名:</Text>
                  <br />
                  <Text>{selectedAgent.hostname}</Text>
                </Col>
                <Col span={12}>
                  <Text strong>IP地址:</Text>
                  <br />
                  <Text>{selectedAgent.ip_address}</Text>
                </Col>
              </Row>
            </Card>

            <Card title="任务信息" size="small" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Text strong>当前任务:</Text>
                  <br />
                  <Progress
                    percent={Math.round((selectedAgent.current_tasks / selectedAgent.max_concurrent_tasks) * 100)}
                    format={() => `${selectedAgent.current_tasks}/${selectedAgent.max_concurrent_tasks}`}
                  />
                </Col>
                <Col span={12}>
                  <Text strong>最大并发:</Text>
                  <br />
                  <Text>{selectedAgent.max_concurrent_tasks}</Text>
                </Col>
              </Row>
            </Card>

            <Card title="能力" size="small" style={{ marginBottom: 16 }}>
              <Space wrap>
                {selectedAgent.capabilities?.map((cap: string) => (
                  <Tag key={cap} color="blue">
                    {cap}
                  </Tag>
                ))}
              </Space>
            </Card>

            <Card title="时间信息" size="small">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Text strong>创建时间:</Text>
                  <br />
                  <Text type="secondary">
                    {dayjs(selectedAgent.created_at).format('YYYY-MM-DD HH:mm:ss')}
                  </Text>
                </Col>
                <Col span={12}>
                  <Text strong>最后活跃:</Text>
                  <br />
                  <Text type="secondary">
                    {dayjs(selectedAgent.last_seen_at).format('YYYY-MM-DD HH:mm:ss')}
                  </Text>
                </Col>
              </Row>
            </Card>
          </div>
        )}
      </Drawer>

      {/* 密钥管理模态框 */}
      <Modal
        title={`${selectedAgent?.name} - 密钥管理`}
        open={keyModalVisible}
        onCancel={() => setKeyModalVisible(false)}
        footer={[
          <Button
            key="create"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateKeyModalVisible(true)}
            disabled={agentKeys.some(key => key.status === 'active')}
          >
            创建密钥
          </Button>,
          <Button key="close" onClick={() => setKeyModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={800}
      >
        {agentKeys.some(key => key.status === 'active') && (
          <Alert
            message="一个Agent只能有一个活跃密钥"
            description="当前Agent已有活跃密钥，如需创建新密钥，请先撤销现有密钥。"
            type="info"
            style={{ marginBottom: 16 }}
          />
        )}
        
        <Table
          dataSource={agentKeys}
          rowKey="id"
          pagination={false}
          size="small"
          columns={[
            {
              title: '密钥ID',
              dataIndex: 'key_id',
              key: 'key_id',
              render: (keyId: string) => (
                <Space>
                  <Text code>{keyId}</Text>
                  <Button
                    type="text"
                    size="small"
                    icon={<CopyOutlined />}
                    onClick={() => copyToClipboard(keyId)}
                  />
                </Space>
              ),
            },
            {
              title: '名称',
              dataIndex: 'name',
              key: 'name',
            },
            {
              title: '状态',
              dataIndex: 'status',
              key: 'status',
              render: (status: string) => {
                const colors = {
                  active: 'success',
                  suspended: 'warning',
                  revoked: 'error',
                  expired: 'default'
                };
                const texts = {
                  active: '活跃',
                  suspended: '暂停',
                  revoked: '已撤销',
                  expired: '已过期'
                };
                return (
                  <Badge
                    status={colors[status as keyof typeof colors] as any}
                    text={texts[status as keyof typeof texts]}
                  />
                );
              },
            },
            {
              title: '使用次数',
              dataIndex: 'usage_count',
              key: 'usage_count',
            },
            {
              title: '最后使用',
              dataIndex: 'last_used_at',
              key: 'last_used_at',
              render: (date?: string) => (
                date ? (
                  <Text type="secondary">
                    {dayjs(date).format('MM-DD HH:mm')}
                  </Text>
                ) : (
                  <Text type="secondary">从未使用</Text>
                )
              ),
            },
            {
              title: '操作',
              key: 'action',
              render: (record: any) => (
                <Space>
                  {record.status === 'active' && (
                    <Popconfirm
                      title="撤销密钥"
                      description="确定要撤销此密钥吗？撤销后Agent将无法使用此密钥。"
                      onConfirm={() => handleRevokeKey(record.key_id)}
                      okText="确定"
                      cancelText="取消"
                      okType="danger"
                    >
                      <Button
                        type="text"
                        danger
                        size="small"
                        icon={<StopOutlined />}
                      >
                        撤销
                      </Button>
                    </Popconfirm>
                  )}
                  {(record.status === 'revoked' || record.status === 'expired') && (
                    <Popconfirm
                      title="删除密钥"
                      description="确定要删除此密钥吗？此操作不可恢复。"
                      onConfirm={() => handleDeleteKey(record.key_id)}
                      okText="确定"
                      cancelText="取消"
                      okType="danger"
                    >
                      <Button
                        type="text"
                        danger
                        size="small"
                        icon={<DeleteOutlined />}
                      >
                        删除
                      </Button>
                    </Popconfirm>
                  )}
                </Space>
              ),
            },
          ]}
        />
      </Modal>

      {/* 创建密钥模态框 */}
      <Modal
        title="创建Agent密钥"
        open={createKeyModalVisible}
        onOk={() => form.submit()}
        onCancel={() => {
          setCreateKeyModalVisible(false);
          form.resetFields();
        }}
        width={500}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateKey}
        >
          <Form.Item
            name="name"
            label="密钥名称"
            rules={[{ required: true, message: '请输入密钥名称' }]}
            initialValue={`${selectedAgent?.name} 密钥`}
          >
            <Input placeholder="请输入密钥名称" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea
              placeholder="请输入密钥描述（可选）"
              rows={3}
            />
          </Form.Item>
          
          <Form.Item
            name="expires_at"
            label="过期时间"
          >
            <DatePicker
              showTime
              placeholder="选择过期时间（可选，不选择则永不过期）"
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 全局密钥创建模态框 */}
      <Modal
        title="创建API密钥"
        open={globalKeyModalVisible}
        onOk={() => globalKeyForm.submit()}
        onCancel={() => {
          setGlobalKeyModalVisible(false);
          globalKeyForm.resetFields();
        }}
        width={500}
      >
        <Alert
          message="密钥创建说明"
          description="创建的密钥将处于未绑定状态，当Agent使用此密钥连接时将自动绑定。"
          type="info"
          style={{ marginBottom: 16 }}
        />
        <Form
          form={globalKeyForm}
          layout="vertical"
          onFinish={handleCreateGlobalKey}
        >
          <Form.Item
            name="name"
            label="密钥名称"
            rules={[{ required: true, message: '请输入密钥名称' }]}
          >
            <Input placeholder="请输入密钥名称" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea
              placeholder="请输入密钥描述（可选）"
              rows={3}
            />
          </Form.Item>
          
          <Form.Item
            name="expires_at"
            label="过期时间"
          >
            <DatePicker
              showTime
              placeholder="选择过期时间（可选，不选择则永不过期）"
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        title="密钥创建成功"
        open={newKeyVisible}
        onOk={() => setNewKeyVisible(false)}
        onCancel={() => setNewKeyVisible(false)}
        footer={[
          <Button
            key="copy"
            type="primary"
            icon={<CopyOutlined />}
            onClick={() => copyToClipboard(newKeyValue)}
          >
            复制密钥
          </Button>,
          <Button key="close" onClick={() => setNewKeyVisible(false)}>
            关闭
          </Button>,
        ]}
      >
        <Alert
          message="请立即保存密钥"
          description="出于安全考虑，密钥值只会显示一次。请立即复制并妥善保管。"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
        <Paragraph>
          <Text strong>密钥值：</Text>
        </Paragraph>
        <Paragraph>
          <Text code copyable>{newKeyValue}</Text>
        </Paragraph>
      </Modal>
    </div>
  );
};

export default Agents;