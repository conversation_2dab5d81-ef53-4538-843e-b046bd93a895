import React, { useState } from 'react';
import { Card, Typography, Tabs, Input, Button, Space, Alert, Table, Tag, Statistic, Row, Col, Switch, Tooltip, Modal, Form, Badge, Select } from 'antd';
import { SearchOutlined, CodeOutlined, RocketOutlined, SaveOutlined, FolderOpenOutlined, ExportOutlined, InfoCircleOutlined, ClearOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;

// 搜索分析服务
const searchAnalyticsService = {
  baseURL: '/api/search-analytics',
  
  getAuthHeaders: () => ({
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
  }),

  // 执行ES查询
  executeQuery: async (query: any) => {
    const response = await fetch(`${searchAnalyticsService.baseURL}/execute`, {
      method: 'POST',
      headers: searchAnalyticsService.getAuthHeaders(),
      body: JSON.stringify(query)
    });
    return response.json();
  },

  // 保存搜索模板
  saveSearchTemplate: async (template: any) => {
    const response = await fetch(`${searchAnalyticsService.baseURL}/templates`, {
      method: 'POST',
      headers: searchAnalyticsService.getAuthHeaders(),
      body: JSON.stringify(template)
    });
    return response.json();
  },

  // 获取搜索模板
  getSearchTemplates: async () => {
    const response = await fetch(`${searchAnalyticsService.baseURL}/templates`, {
      headers: searchAnalyticsService.getAuthHeaders()
    });
    return response.json();
  },

  // 获取可用索引
  getAvailableIndices: async () => {
    const response = await fetch(`${searchAnalyticsService.baseURL}/indices`, {
      headers: searchAnalyticsService.getAuthHeaders()
    });
    return response.json();
  },

  // 清洗数据
  cleanData: async (data: any[], rules: any[]) => {
    const response = await fetch(`${searchAnalyticsService.baseURL}/clean`, {
      method: 'POST',
      headers: searchAnalyticsService.getAuthHeaders(),
      body: JSON.stringify({ data, rules })
    });
    return response.json();
  },

  // 获取动态模型类型
  getModelTypes: async () => {
    const response = await fetch(`${searchAnalyticsService.baseURL}/model-types`, {
      headers: searchAnalyticsService.getAuthHeaders()
    });
    return response.json();
  },

  // 获取字段映射
  getFieldMappings: async (sourceIndex?: string) => {
    const url = sourceIndex 
      ? `${searchAnalyticsService.baseURL}/field-mappings?source_index=${sourceIndex}`
      : `${searchAnalyticsService.baseURL}/field-mappings`;
    const response = await fetch(url, {
      headers: searchAnalyticsService.getAuthHeaders()
    });
    return response.json();
  },

  // 创建字段映射
  createFieldMapping: async (mappingData: any) => {
    const response = await fetch(`${searchAnalyticsService.baseURL}/field-mappings`, {
      method: 'POST',
      headers: searchAnalyticsService.getAuthHeaders(),
      body: JSON.stringify(mappingData)
    });
    return response.json();
  },

  // 获取清洗规则
  getCleaningRules: async () => {
    const response = await fetch(`${searchAnalyticsService.baseURL}/cleaning-rules`, {
      headers: searchAnalyticsService.getAuthHeaders()
    });
    return response.json();
  },

  // 创建清洗规则
  createCleaningRule: async (ruleData: any) => {
    const response = await fetch(`${searchAnalyticsService.baseURL}/cleaning-rules`, {
      method: 'POST',
      headers: searchAnalyticsService.getAuthHeaders(),
      body: JSON.stringify(ruleData)
    });
    return response.json();
  }
};

const SearchAnalytics: React.FC = () => {
  const [activeTab, setActiveTab] = useState('query-builder');
  const [loading, setLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searchStats, setSearchStats] = useState<any>(null);
  const [queryStatus, setQueryStatus] = useState<string>('');
  const [useRealAPI, setUseRealAPI] = useState(true);
  const [savedTemplates, setSavedTemplates] = useState<any[]>([]);
  const [templateModalVisible, setTemplateModalVisible] = useState(false);
  const [availableIndices, setAvailableIndices] = useState<any[]>([]);
  const [queryHistory, setQueryHistory] = useState<any[]>([]);
  const [cleanedData, setCleanedData] = useState<any[]>([]);
  const [cleaningRules, setCleaningRules] = useState<any[]>([]);
  const [modelTypes, setModelTypes] = useState<any[]>([]);
  const [fieldMappings, setFieldMappings] = useState<any[]>([]);
  const [savedCleaningRules, setSavedCleaningRules] = useState<any[]>([]);
  const [cleaningModalVisible, setCleaningModalVisible] = useState(false);
  const [fieldMappingModalVisible, setFieldMappingModalVisible] = useState(false);
  const [processingLog, setProcessingLog] = useState<any[]>([]);
  const [selectedIndexFields, setSelectedIndexFields] = useState<any[]>([]); // 当前索引的字段信息
  const [currentQuery, setCurrentQuery] = useState<any>({
    index: '', // 不再硬编码，动态设置
    body: {
      query: { match_all: {} },
      size: 20
    }
  });

  // 加载选中索引的字段信息
  const loadIndexFields = async (indexName: string) => {
    try {
      setSelectedIndexFields([]);
      
      if (!indexName || !indexName.startsWith('dynamic_')) {
        console.log('非动态索引，跳过字段加载');
        return;
      }
      
      // 提取模型类型名称
      const modelTypeName = indexName.replace('dynamic_', '');
      
      // 从已加载的模型类型中查找
      const modelType = modelTypes.find(mt => mt.name === modelTypeName);
      if (modelType && modelType.fields) {
        setSelectedIndexFields(modelType.fields);
        console.log(`已加载模型 ${modelTypeName} 的字段信息:`, modelType.fields);
        
        // 更新查询模板，使用真实字段
        updateQueryTemplatesForModel(modelType.fields, modelTypeName);
        setQueryStatus(`已加载 ${modelType.display_name || modelTypeName} 的 ${modelType.fields.length} 个字段`);
      } else {
        console.log(`未找到模型类型: ${modelTypeName}，尝试重新加载模型数据`);
        // 如果没找到，尝试重新加载模型类型数据
        await loadModelTypes();
        // 重新查找
        const reloadedModelType = modelTypes.find(mt => mt.name === modelTypeName);
        if (reloadedModelType && reloadedModelType.fields) {
          setSelectedIndexFields(reloadedModelType.fields);
          setQueryStatus(`已加载 ${reloadedModelType.display_name || modelTypeName} 的 ${reloadedModelType.fields.length} 个字段`);
        } else {
          setQueryStatus(`警告: 无法加载模型 ${modelTypeName} 的字段信息`);
        }
      }
    } catch (error) {
      console.error('加载索引字段失败:', error);
      setQueryStatus(`加载字段信息失败: ${error}`);
    }
  };

  // 根据模型字段更新查询模板
  const updateQueryTemplatesForModel = (fields: any[], modelTypeName: string) => {
    // 根据字段类型生成合适的查询示例
    const textFields = fields.filter(f => f.field_type === 'text' || f.field_type === 'textarea');
    const numberFields = fields.filter(f => f.field_type === 'number');
    const dateFields = fields.filter(f => f.field_type === 'date' || f.field_type === 'datetime');
    
    console.log(`为模型 ${modelTypeName} 生成查询模板:`);
    console.log(`- 文本字段 (${textFields.length}个):`, textFields.map(f => f.field_name));
    console.log(`- 数字字段 (${numberFields.length}个):`, numberFields.map(f => f.field_name)); 
    console.log(`- 日期字段 (${dateFields.length}个):`, dateFields.map(f => f.field_name));
    
    // 这里可以根据字段类型动态生成更智能的查询模板
    // 暂时保持简单的实现
  };

  // 表单
  const [templateForm] = Form.useForm();

  // 加载可用索引
  const loadAvailableIndices = async () => {
    try {
      const result = await searchAnalyticsService.getAvailableIndices();
      if (result.success) {
        setAvailableIndices(result.data);
        
        // 自动选择第一个有数据的动态索引
        const dynamicIndices = result.data.filter((idx: any) => 
          idx.is_dynamic && idx.docs_count > 0
        );
        
        if (dynamicIndices.length > 0) {
          const firstIndex = dynamicIndices[0].name;
          setCurrentQuery((prev: any) => ({...prev, index: firstIndex}));
          setQueryStatus(`已自动选择索引: ${firstIndex} (${dynamicIndices[0].docs_count.toLocaleString()} 条数据)`);
          // 自动加载字段信息
          await loadIndexFields(firstIndex);
        } else if (result.data.length > 0) {
          // 如果没有动态索引，选择第一个有数据的索引
          const firstAvailable = result.data.find((idx: any) => idx.docs_count > 0);
          if (firstAvailable) {
            setCurrentQuery((prev: any) => ({...prev, index: firstAvailable.name}));
            setQueryStatus(`已选择索引: ${firstAvailable.name} (${firstAvailable.docs_count.toLocaleString()} 条数据)`);
            await loadIndexFields(firstAvailable.name);
          }
        } else {
          setQueryStatus('未找到可用的索引，请先创建动态模型并添加数据');
        }
      }
    } catch (error) {
      console.error('加载索引失败:', error);
      setQueryStatus('加载索引失败，请检查后端服务');
    }
  };

  // 组件加载时获取索引和模型数据
  React.useEffect(() => {
    loadAvailableIndices();
    loadModelTypes();
    loadSavedTemplates();
    loadSavedCleaningRules();
  }, []);

  // 加载动态模型类型
  const loadModelTypes = async () => {
    try {
      const result = await searchAnalyticsService.getModelTypes();
      if (result.success) {
        setModelTypes(result.data);
      }
    } catch (error) {
      console.error('加载模型类型失败:', error);
    }
  };

  // 加载保存的模板
  const loadSavedTemplates = async () => {
    try {
      const result = await searchAnalyticsService.getSearchTemplates();
      if (result.success) {
        setSavedTemplates(result.data);
      }
    } catch (error) {
      console.error('加载模板失败:', error);
    }
  };

  // 加载保存的清洗规则
  const loadSavedCleaningRules = async () => {
    try {
      const result = await searchAnalyticsService.getCleaningRules();
      if (result.success) {
        setSavedCleaningRules(result.data);
      }
    } catch (error) {
      console.error('加载清洗规则失败:', error);
    }
  };

  const executeSearch = async () => {
    console.log('开始执行查询:', currentQuery);
    
    setLoading(true);
    setQueryStatus('正在执行查询...');
    setSearchResults([]);
    setSearchStats(null);

    // 记录查询历史
    const historyItem = {
      id: Date.now().toString(),
      query: JSON.parse(JSON.stringify(currentQuery)),
      timestamp: new Date().toISOString()
    };
    setQueryHistory(prev => [historyItem, ...prev.slice(0, 9)]); // 保留最近10条
    
    try {
      // 调用真实API
      const result = await searchAnalyticsService.executeQuery(currentQuery);
      
      if (result.success) {
        setSearchResults(result.data.hits || []);
        setSearchStats(result.data.stats);
        setQueryStatus(`🌐 查询完成！找到 ${result.data.total || 0} 条结果`);
      } else {
        throw new Error(result.detail || '查询失败');
      }
      
      console.log('查询结果:', searchResults);
      
    } catch (error) {
      setQueryStatus(`查询执行失败: ${error instanceof Error ? error.message : '未知错误'}`);
      console.error('查询错误:', error);
    } finally {
      setLoading(false);
    }
  };

  // 预设查询模板
  const queryTemplates = [
    {
      name: '全部资产',
      description: '查询所有资产',
      query: { query: { match_all: {} }, size: 100 }
    },
    {
      name: '子域名查询',
      description: '查询所有子域名资产',
      query: {
        query: {
          bool: {
            should: [
              { term: { asset_type: 'subdomain' } },
              { term: { 'metadata.asset_type': 'subdomain' } }
            ]
          }
        },
        size: 50
      }
    },
    {
      name: '高置信度资产',
      description: '查询高置信度的资产',
      query: {
        query: {
          bool: {
            should: [
              { term: { confidence: 'high' } },
              { range: { confidence: { gte: 0.8 } } }
            ]
          }
        },
        size: 30
      }
    },
    {
      name: '最近发现',
      description: '查询最近7天发现的资产',
      query: {
        query: {
          range: {
            '@timestamp': {
              gte: 'now-7d',
              lte: 'now'
            }
          }
        },
        sort: [{ '@timestamp': { order: 'desc' } }],
        size: 50
      }
    },
    {
      name: '聚合统计',
      description: '按资产类型聚合统计',
      query: {
        size: 0,
        aggs: {
          asset_types: {
            terms: {
              script: {
                source: "params._source.asset_type ?: params._source.metadata?.asset_type ?: 'unknown'",
                lang: 'painless'
              },
              size: 20
            }
          }
        }
      }
    }
  ];

  const applyTemplate = (template: any) => {
    setCurrentQuery({
      ...currentQuery,
      body: template.query
    });
    setQueryStatus(`已应用模板: ${template.name}`);
  };

  const saveSearchTemplate = async (values: any) => {
    try {
      const template = {
        name: values.name,
        description: values.description,
        query: currentQuery,
        created_at: new Date().toISOString(),
        tags: values.tags?.split(',').map((t: string) => t.trim()) || []
      };

      const result = await searchAnalyticsService.saveSearchTemplate(template);
      if (result.success) {
        // 重新加载模板列表
        await loadSavedTemplates();
        setQueryStatus('搜索模板保存成功');
      } else {
        throw new Error('保存失败');
      }

      setTemplateModalVisible(false);
      templateForm.resetFields();
    } catch (error) {
      setQueryStatus('保存搜索模板失败: ' + (error instanceof Error ? error.message : '未知错误'));
    }
  };

  const exportResults = () => {
    if (searchResults.length === 0) {
      setQueryStatus('没有数据可导出');
      return;
    }

    // 创建动态CSV数据
    const allFields = new Set<string>();
    
    // 收集所有字段
    searchResults.forEach(item => {
      allFields.add('_id');
      allFields.add('_index'); 
      allFields.add('_score');
      
      if (item._source) {
        Object.keys(item._source).forEach(key => {
          allFields.add(key);
        });
      }
    });

    const fields = Array.from(allFields);
    
    const csvData = searchResults.map(item => {
      const row: any = {};
      
      fields.forEach(field => {
        if (field.startsWith('_')) {
          // ES内置字段
          row[field] = item[field] || '';
        } else {
          // _source字段
          const value = item._source?.[field];
          if (Array.isArray(value)) {
            row[field] = value.join(';');
          } else if (typeof value === 'object' && value !== null) {
            row[field] = JSON.stringify(value);
          } else {
            row[field] = value || '';
          }
        }
      });
      
      return row;
    });

    // 转换为CSV字符串
    const headers = fields;
    const csvContent = [
      headers.join(','),
      ...csvData.map(row => headers.map(header => `"${String(row[header]).replace(/"/g, '""')}"`).join(','))
    ].join('\n');

    // 下载文件
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `search_results_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    setQueryStatus('导出成功');
  };

  // 添加智能清洗规则
  const addSmartCleaningRule = () => {
    const newRule = {
      id: Date.now().toString(),
      name: `规则 ${cleaningRules.length + 1}`,
      type: 'smart_deduplication',
      field: 'asset_value',
      enabled: true,
      strategy: 'keep_highest_score',
      similarity_threshold: 0.9,
      mapping_rules: {},
      enrichment_rules: [],
      validation_rules: []
    };
    setCleaningRules([...cleaningRules, newRule]);
  };

  // 执行智能数据清洗
  const executeDataCleaning = async () => {
    if (searchResults.length === 0) {
      setQueryStatus('没有数据可清洗');
      return;
    }

    setLoading(true);
    setQueryStatus('正在清洗数据...');

    try {
      const enabledRules = cleaningRules.filter(rule => rule.enabled);
      
      const result = await searchAnalyticsService.cleanData(searchResults, enabledRules);
      if (result.success) {
        setCleanedData(result.data.cleaned_data);
        setProcessingLog(result.data.processing_summary || []);
        
        const summary = result.data.processing_summary || [];
        const removedCount = result.data.original_count - result.data.processed_count;
        
        setQueryStatus(
          `🧹 数据清洗完成！处理了 ${result.data.processed_count} 条数据` +
          `${removedCount > 0 ? `，移除了 ${removedCount} 条重复/无效数据` : ''}` +
          `，应用了 ${result.data.rules_applied} 个规则`
        );
      } else {
        throw new Error('清洗失败');
      }
    } catch (error) {
      setQueryStatus('数据清洗失败: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 动态生成表格列
  const generateDynamicColumns = (data: any[]): ColumnsType<any> => {
    if (!data || data.length === 0) return [];

    // 收集所有可能的字段
    const fieldSet = new Set<string>();
    
    data.forEach(item => {
      // ES基础字段
      fieldSet.add('_id');
      fieldSet.add('_index');
      fieldSet.add('_score');
      
      // _source中的字段
      if (item._source) {
        Object.keys(item._source).forEach(key => {
          fieldSet.add(key);
        });
      }
    });

    const fields = Array.from(fieldSet);
    const columns: ColumnsType<any> = [];

    // 添加基础ES字段
    columns.push({
      title: 'ID',
      dataIndex: '_id',
      key: '_id',
      width: 120,
      render: (value: string) => <Text code style={{ fontSize: '11px' }}>{value?.slice(0, 8)}...</Text>
    });

    columns.push({
      title: '索引',
      dataIndex: '_index',
      key: '_index',
      width: 150,
      render: (value: string) => <Tag color="blue" style={{ fontSize: '11px' }}>{value}</Tag>
    });

    columns.push({
      title: '评分',
      dataIndex: '_score',
      key: '_score',
      width: 80,
      render: (value: number) => value?.toFixed(2) || '-'
    });

    // 动态添加_source字段
    const sourceFields = fields.filter(field => !field.startsWith('_'));
    
    sourceFields.forEach(field => {
      const column: any = {
        title: field,
        key: field,
        width: Math.min(Math.max(field.length * 12, 100), 250),
        render: (record: any) => {
          const value = record._source?.[field];
          
          if (value === undefined || value === null) {
            return <Text type="secondary">-</Text>;
          }
          
          // 处理不同类型的值
          if (Array.isArray(value)) {
            if (value.length === 0) {
              return <Text type="secondary">[]</Text>;
            }
            return (
              <Space wrap size="small">
                {value.slice(0, 3).map((item, index) => (
                  <Tag key={index}>{String(item)}</Tag>
                ))}
                {value.length > 3 && <Text type="secondary">+{value.length - 3}</Text>}
              </Space>
            );
          } else if (typeof value === 'object') {
            return (
              <Tooltip title={JSON.stringify(value, null, 2)}>
                <Tag color="orange">Object</Tag>
              </Tooltip>
            );
          } else if (typeof value === 'boolean') {
            return <Tag color={value ? 'green' : 'red'}>{String(value)}</Tag>;
          } else if (typeof value === 'number') {
            return <Text>{value.toLocaleString()}</Text>;
          } else {
            const strValue = String(value);
            
            // 检测URL
            if (strValue.match(/^https?:\/\//)) {
              return (
                <a href={strValue} target="_blank" rel="noopener noreferrer" style={{ fontSize: '12px' }}>
                  {strValue.length > 30 ? `${strValue.slice(0, 30)}...` : strValue}
                </a>
              );
            }
            
            // 检测时间戳
            if (field.includes('time') || field.includes('date') || field === '@timestamp') {
              const date = new Date(strValue);
              if (!isNaN(date.getTime())) {
                return <Text style={{ fontSize: '11px' }}>{date.toLocaleString()}</Text>;
              }
            }
            
            // 检测IP地址
            if (strValue.match(/^\d+\.\d+\.\d+\.\d+$/)) {
              return <Text code style={{ fontSize: '11px' }}>{strValue}</Text>;
            }
            
            // 检测域名
            if (field.includes('domain') || field.includes('host') || strValue.match(/^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/)) {
              return <Text code style={{ fontSize: '11px' }}>{strValue}</Text>;
            }
            
            // 普通文本
            if (strValue.length > 50) {
              return (
                <Tooltip title={strValue}>
                  <Text style={{ fontSize: '11px' }}>{strValue.slice(0, 50)}...</Text>
                </Tooltip>
              );
            }
            
            return <Text style={{ fontSize: '11px' }}>{strValue}</Text>;
          }
        }
      };
      
      columns.push(column);
    });

    return columns;
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <SearchOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
        智能搜索分析平台
      </Title>
      
      <Alert
        message="统一搜索分析平台"
        description="强大的Elasticsearch查询构建器，支持动态模型索引、复杂搜索、模板保存、数据清洗和导出分析。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* 状态显示 */}
      {queryStatus && (
        <Alert
          message={queryStatus}
          type={loading ? 'info' : (queryStatus.includes('失败') ? 'error' : 'success')}
          style={{ marginBottom: 16 }}
          showIcon
          action={
            availableIndices.length > 0 && (
              <Tooltip title={`当前可用 ${availableIndices.length} 个索引`}>
                <Badge count={availableIndices.length} color="blue">
                  <Button size="small" type="link">
                    索引信息
                  </Button>
                </Badge>
              </Tooltip>
            )
          }
        />
      )}

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={<><CodeOutlined />查询构建器</>} key="query-builder">
          <div style={{ display: 'flex', gap: 16 }}>
            {/* 左侧查询编辑器 */}
            <div style={{ flex: 2 }}>
              <Card title="ES查询编辑器" extra={
                <Space>
                  <Button 
                    type="primary" 
                    icon={<RocketOutlined />} 
                    onClick={executeSearch}
                    loading={loading}
                  >
                    {loading ? '执行中...' : '执行查询'}
                  </Button>
                  <Button 
                    icon={<SaveOutlined />} 
                    onClick={() => setTemplateModalVisible(true)}
                  >
                    保存模板
                  </Button>
                </Space>
              }>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div>
                    <Text strong>索引:</Text>
                    <Space.Compact style={{ marginTop: 8, width: '100%' }}>
                      <Select
                        value={currentQuery.index}
                        onChange={async (value) => {
                          setCurrentQuery({...currentQuery, index: value});
                          const selectedIndex = availableIndices.find(idx => idx.name === value);
                          if (selectedIndex) {
                            setQueryStatus(`已选择索引: ${selectedIndex.model_info?.display_name || value} (${selectedIndex.docs_count.toLocaleString()} 条数据)`);
                            // 加载字段信息
                            await loadIndexFields(value);
                          }
                        }}
                        placeholder="选择索引"
                        style={{ flex: 1 }}
                        showSearch
                        optionFilterProp="children"
                      >
                        {availableIndices.map(idx => (
                          <Select.Option key={idx.name} value={idx.name}>
                            <Space>
                              {idx.is_dynamic && <Tag color="blue">动态</Tag>}
                              <Text>{idx.model_info?.display_name || idx.name}</Text>
                              <Text type="secondary">({idx.docs_count.toLocaleString()})</Text>
                            </Space>
                          </Select.Option>
                        ))}
                      </Select>
                      <Tooltip title="刷新索引列表">
                        <Button 
                          icon={<SearchOutlined />} 
                          onClick={loadAvailableIndices}
                          loading={loading}
                        />
                      </Tooltip>
                    </Space.Compact>
                    {availableIndices.length > 0 && (
                      <div style={{ marginTop: 8 }}>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          可用索引: {availableIndices.slice(0, 3).map(idx => 
                            `${idx.model_info?.display_name || idx.name} (${idx.docs_count.toLocaleString()})`
                          ).join(', ')}
                          {availableIndices.length > 3 && ` 等${availableIndices.length}个`}
                        </Text>
                      </div>
                    )}
                  </div>
                  <div>
                    <Text strong>查询体:</Text>
                    <TextArea
                      rows={12}
                      value={JSON.stringify(currentQuery.body, null, 2)}
                      onChange={(e) => {
                        try {
                          const body = JSON.parse(e.target.value);
                          setCurrentQuery({...currentQuery, body});
                        } catch (error) {
                          // JSON格式错误时不更新
                        }
                      }}
                      style={{ marginTop: 8, fontFamily: 'Monaco, monospace' }}
                    />
                  </div>
                </Space>
              </Card>
            </div>

            {/* 右侧模板选择和历史 */}
            <div style={{ flex: 1 }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                {/* 字段信息 */}
                {selectedIndexFields.length > 0 && (
                  <Card title={`字段信息 (${selectedIndexFields.length}个)`} size="small" style={{ maxHeight: '500px', overflow: 'auto' }}>
                    <Space direction="vertical" style={{ width: '100%' }} size="small">
                      {selectedIndexFields.map((field: any, index: number) => (
                        <div key={index} style={{ 
                          padding: '6px 8px', 
                          border: '1px solid #f0f0f0', 
                          borderRadius: '4px',
                          fontSize: '11px',
                          cursor: 'pointer',
                          backgroundColor: '#fafafa'
                        }}
                        onClick={() => {
                          // 快速添加字段查询
                          const fieldQuery = {
                            query: {
                              exists: { field: `entity_data.${field.field_name}` }
                            },
                            size: 20
                          };
                          setCurrentQuery({...currentQuery, body: fieldQuery});
                          setQueryStatus(`已设置查询字段: ${field.display_name || field.field_name}`);
                        }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Text strong style={{ fontSize: '11px' }}>
                              {field.display_name || field.field_name}
                            </Text>
                            <Tag color={
                              field.field_type === 'text' ? 'blue' :
                              field.field_type === 'textarea' ? 'cyan' :
                              field.field_type === 'number' ? 'green' :
                              field.field_type === 'date' ? 'orange' :
                              field.field_type === 'datetime' ? 'red' :
                              field.field_type === 'boolean' ? 'purple' :
                              field.field_type === 'select' ? 'gold' :
                              'default'
                            }>{field.field_type}</Tag>
                          </div>
                          <div style={{ marginTop: 2 }}>
                            <Text type="secondary" style={{ fontSize: '10px' }}>
                              {field.field_name}
                            </Text>
                            <div style={{ marginTop: 2 }}>
                              {field.is_required && <Tag color="red" style={{ fontSize: '9px' }}>必填</Tag>}
                              {field.is_searchable && <Tag color="green" style={{ fontSize: '9px' }}>可搜索</Tag>}
                              {field.is_filterable && <Tag color="blue" style={{ fontSize: '9px' }}>可筛选</Tag>}
                              {field.is_unique && <Tag color="orange" style={{ fontSize: '9px' }}>唯一</Tag>}
                            </div>
                          </div>
                          {field.description && (
                            <Text type="secondary" style={{ fontSize: '10px', fontStyle: 'italic' }}>
                              {field.description.length > 30 ? `${field.description.slice(0, 30)}...` : field.description}
                            </Text>
                          )}
                        </div>
                      ))}
                    </Space>
                  </Card>
                )}
                
                <Card title="查询模板" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    {queryTemplates.map((template, index) => (
                      <Tooltip key={index} title={template.description} placement="left">
                        <Button
                          block
                          onClick={() => applyTemplate(template)}
                          style={{ textAlign: 'left' }}
                        >
                          {template.name}
                        </Button>
                      </Tooltip>
                    ))}
                  </Space>
                </Card>
                
                {queryHistory.length > 0 && (
                  <Card title="查询历史" size="small">
                    <Space direction="vertical" style={{ width: '100%' }}>
                      {queryHistory.slice(0, 5).map((item) => (
                        <div key={item.id} style={{ 
                          padding: '8px', 
                          border: '1px solid #f0f0f0', 
                          borderRadius: '4px',
                          cursor: 'pointer'
                        }}
                        onClick={() => {
                          setCurrentQuery(item.query);
                          setQueryStatus('已恢复历史查询');
                        }}>
                          <Text style={{ fontSize: '12px' }}>
                            🌐 {new Date(item.timestamp).toLocaleTimeString()}
                          </Text>
                          <br />
                          <Text type="secondary" style={{ fontSize: '11px' }}>
                            {item.query.index || 'default'}
                          </Text>
                        </div>
                      ))}
                    </Space>
                  </Card>
                )}
              </Space>
            </div>
          </div>

          {/* 查询统计 */}
          {searchStats && (
            <Card title="查询统计" style={{ marginTop: 16 }}>
              <Row gutter={16}>
                <Col span={6}>
                  <Statistic title="查询时间" value={searchStats.took} suffix="ms" />
                </Col>
                <Col span={6}>
                  <Statistic title="总命中数" value={searchStats.total} />
                </Col>
                <Col span={6}>
                  <Statistic title="最大评分" value={searchStats.max_score?.toFixed(2) || 0} />
                </Col>
                <Col span={6}>
                  <Statistic title="结果数量" value={searchResults.length} />
                </Col>
              </Row>
            </Card>
          )}

          {/* 查询结果 */}
          {searchResults.length > 0 && (
            <Card 
              title={`查询结果 (${searchResults.length} 条)`}
              style={{ marginTop: 16 }}
              extra={
                <Button 
                  icon={<ExportOutlined />} 
                  onClick={exportResults}
                >
                  导出CSV
                </Button>
              }
            >
              <Table
                columns={generateDynamicColumns(searchResults)}
                dataSource={searchResults}
                rowKey="_id"
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                }}
                scroll={{ x: 'max-content' }}
                size="small"
              />
            </Card>
          )}
        </TabPane>
        
        <TabPane tab={<><FolderOpenOutlined />模板管理</>} key="templates">
          <Row gutter={16}>
            <Col span={16}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Card title="预设查询模板">
                  <Table
                    dataSource={queryTemplates.map((template, index) => ({
                      ...template,
                      id: `preset_${index}`,
                      type: 'preset',
                      created_at: '系统预设'
                    }))}
                    rowKey="id"
                    columns={[
                      { title: '模板名称', dataIndex: 'name', key: 'name' },
                      { title: '描述', dataIndex: 'description', key: 'description' },
                      { title: '类型', key: 'type', render: () => <Tag color="blue">预设</Tag> },
                      { title: '创建时间', dataIndex: 'created_at', key: 'created_at' },
                      {
                        title: '操作',
                        key: 'actions',
                        render: (record: any) => (
                          <Button 
                            size="small" 
                            onClick={() => {
                              applyTemplate(record);
                              setActiveTab('query-builder');
                            }}
                          >
                            应用
                          </Button>
                        )
                      }
                    ]}
                    pagination={false}
                    size="small"
                  />
                </Card>

                <Card title="已保存的搜索模板">
                  <Table
                    dataSource={savedTemplates.map(template => ({
                      ...template,
                      type: 'saved'
                    }))}
                    rowKey="id"
                    columns={[
                      { title: '模板名称', dataIndex: 'name', key: 'name' },
                      { title: '描述', dataIndex: 'description', key: 'description' },
                      { 
                        title: '标签', 
                        dataIndex: 'tags', 
                        key: 'tags',
                        render: (tags: string[]) => (
                          <Space wrap>
                            {tags?.map(tag => <Tag key={tag}>{tag}</Tag>)}
                          </Space>
                        )
                      },
                      { title: '类型', key: 'type', render: () => <Tag color="green">已保存</Tag> },
                      { 
                        title: '创建时间', 
                        dataIndex: 'created_at', 
                        key: 'created_at',
                        render: (date: string) => new Date(date).toLocaleString()
                      },
                      {
                        title: '操作',
                        key: 'actions',
                        render: (record: any) => (
                          <Space>
                            <Button 
                              size="small" 
                              onClick={() => {
                                applyTemplate(record);
                                setActiveTab('query-builder');
                              }}
                            >
                              应用
                            </Button>
                            <Button 
                              size="small" 
                              danger
                              onClick={async () => {
                                try {
                                  // 这里可以添加删除API调用
                                  setSavedTemplates(prev => prev.filter(t => t.id !== record.id));
                                  setQueryStatus('模板删除成功');
                                } catch (error) {
                                  setQueryStatus('模板删除失败');
                                }
                              }}
                            >
                              删除
                            </Button>
                          </Space>
                        )
                      }
                    ]}
                    pagination={false}
                    size="small"
                  />
                </Card>
              </Space>
            </Col>
            
            <Col span={8}>
              <Card title="索引信息" size="small">
                {availableIndices.length > 0 ? (
                  <Space direction="vertical" style={{ width: '100%' }}>
                    {availableIndices.slice(0, 8).map((idx, index) => (
                      <div key={index} style={{ 
                        padding: '8px', 
                        border: `1px solid ${idx.is_dynamic ? '#1890ff' : '#f0f0f0'}`, 
                        borderRadius: '4px',
                        cursor: 'pointer',
                        backgroundColor: idx.is_dynamic ? '#f6ffed' : 'white'
                      }}
                      onClick={() => {
                        setCurrentQuery({...currentQuery, index: idx.name});
                        setActiveTab('query-builder');
                        setQueryStatus(`已选择索引: ${idx.name}`);
                      }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Text strong style={{ fontSize: '12px' }}>
                            {idx.model_info?.display_name || idx.name}
                            {idx.is_dynamic && <Tag color="blue" style={{ marginLeft: 4 }}>动态模型</Tag>}
                          </Text>
                          <Text type="secondary" style={{ fontSize: '10px' }}>
                            {idx.docs_count.toLocaleString()} 条
                          </Text>
                        </div>
                        <Text type="secondary" style={{ fontSize: '11px' }}>
                          {idx.model_info?.description || `索引: ${idx.name}`} | 大小: {idx.store_size}
                        </Text>
                      </div>
                    ))}
                  </Space>
                ) : (
                  <Alert 
                    message="正在加载索引信息..." 
                    type="info" 
                    showIcon 
                  />
                )}
              </Card>
            </Col>
          </Row>
        </TabPane>
        
        <TabPane tab={<><ClearOutlined />智能数据清洗</>} key="data-cleaning">
          <Row gutter={16}>
            <Col span={8}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Card 
                  title="清洗规则" 
                  size="small"
                  extra={
                    <Space>
                      <Button size="small" onClick={addSmartCleaningRule}>
                        添加规则
                      </Button>
                      <Button size="small" onClick={() => setCleaningModalVisible(true)}>
                        规则库
                      </Button>
                    </Space>
                  }
                >
                  <Space direction="vertical" style={{ width: '100%' }}>
                    {cleaningRules.map((rule, index) => (
                      <Card key={rule.id} size="small" style={{ marginBottom: 8 }}>
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Text strong>{rule.name || `规则 ${index + 1}`}</Text>
                            <Switch 
                              size="small"
                              checked={rule.enabled}
                              onChange={(checked) => {
                                const updated = [...cleaningRules];
                                updated[index].enabled = checked;
                                setCleaningRules(updated);
                              }}
                            />
                          </div>
                          
                          <div>
                            <Text type="secondary">类型:</Text>
                            <Input 
                              size="small"
                              value={rule.type}
                              onChange={(e) => {
                                const updated = [...cleaningRules];
                                updated[index].type = e.target.value;
                                setCleaningRules(updated);
                              }}
                              style={{ marginTop: 4 }}
                            />
                          </div>
                          
                          <div>
                            <Text type="secondary">字段:</Text>
                            <Input 
                              size="small"
                              value={rule.field}
                              onChange={(e) => {
                                const updated = [...cleaningRules];
                                updated[index].field = e.target.value;
                                setCleaningRules(updated);
                              }}
                              style={{ marginTop: 4 }}
                            />
                          </div>
                          
                          {rule.type === 'smart_deduplication' && (
                            <>
                              <div>
                                <Text type="secondary">去重策略:</Text>
                                <Input 
                                  size="small"
                                  value={rule.strategy || 'keep_highest_score'}
                                  onChange={(e) => {
                                    const updated = [...cleaningRules];
                                    updated[index].strategy = e.target.value;
                                    setCleaningRules(updated);
                                  }}
                                  placeholder="keep_first, keep_last, keep_highest_score, fuzzy_matching"
                                  style={{ marginTop: 4 }}
                                />
                              </div>
                              
                              {rule.strategy === 'fuzzy_matching' && (
                                <div>
                                  <Text type="secondary">相似度阈值:</Text>
                                  <Input 
                                    size="small"
                                    type="number"
                                    value={rule.similarity_threshold || 0.9}
                                    onChange={(e) => {
                                      const updated = [...cleaningRules];
                                      updated[index].similarity_threshold = parseFloat(e.target.value);
                                      setCleaningRules(updated);
                                    }}
                                    min={0}
                                    max={1}
                                    step={0.1}
                                    style={{ marginTop: 4 }}
                                  />
                                </div>
                              )}
                            </>
                          )}
                          
                          <Button 
                            size="small" 
                            danger 
                            block
                            onClick={() => {
                              setCleaningRules(prev => prev.filter((_, i) => i !== index));
                            }}
                          >
                            删除规则
                          </Button>
                        </Space>
                      </Card>
                    ))}
                    
                    {cleaningRules.length === 0 && (
                      <Alert 
                        message="暂无清洗规则" 
                        description="点击上方按钮添加智能数据清洗规则"
                        type="info"
                        showIcon
                      />
                    )}
                    
                    <Button 
                      type="primary" 
                      block
                      icon={<ClearOutlined />}
                      onClick={executeDataCleaning}
                      loading={loading}
                      disabled={searchResults.length === 0 || cleaningRules.length === 0}
                    >
                      执行清洗
                    </Button>
                  </Space>
                </Card>
                
                {processingLog.length > 0 && (
                  <Card title="处理日志" size="small">
                    <Space direction="vertical" style={{ width: '100%' }}>
                      {processingLog.map((log, index) => (
                        <div key={index} style={{ 
                          padding: '8px', 
                          border: '1px solid #f0f0f0', 
                          borderRadius: '4px',
                          fontSize: '12px'
                        }}>
                          <Text strong>{log.rule}</Text>
                          <br />
                          <Text type="secondary">{log.action}</Text>
                          <br />
                          <Text type="secondary">
                            处理前: {log.before_count} 条 → 处理后: {log.after_count} 条
                            {log.removed_count && ` (移除: ${log.removed_count})`}
                          </Text>
                        </div>
                      ))}
                    </Space>
                  </Card>
                )}
              </Space>
            </Col>
            
            <Col span={16}>
              <Card title={`清洗结果 ${cleanedData.length > 0 ? `(${cleanedData.length} 条)` : ''}`}>
                {cleanedData.length > 0 ? (
                  <Table
                    columns={generateDynamicColumns(cleanedData)}
                    dataSource={cleanedData}
                    rowKey="_id"
                    pagination={{
                      pageSize: 8,
                      showSizeChanger: true,
                      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                    }}
                    scroll={{ x: 'max-content' }}
                    size="small"
                  />
                ) : (
                  <Alert 
                    message="暂无清洗结果" 
                    description="先执行搜索获取数据，然后配置清洗规则进行智能数据清洗"
                    type="info"
                    showIcon
                  />
                )}
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>

      {/* 保存模板模态框 */}
      <Modal
        title="保存搜索模板"
        open={templateModalVisible}
        onCancel={() => setTemplateModalVisible(false)}
        footer={null}
      >
        <Form form={templateForm} onFinish={saveSearchTemplate} layout="vertical">
          <Form.Item name="name" label="模板名称" rules={[{ required: true }]}>
            <Input placeholder="输入模板名称" />
          </Form.Item>
          <Form.Item name="description" label="描述">
            <TextArea rows={3} placeholder="描述这个搜索模板的用途" />
          </Form.Item>
          <Form.Item name="tags" label="标签">
            <Input placeholder="标签，用逗号分隔" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">保存</Button>
              <Button onClick={() => setTemplateModalVisible(false)}>取消</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 清洗规则库模态框 */}
      <Modal
        title="清洗规则库"
        open={cleaningModalVisible}
        onCancel={() => setCleaningModalVisible(false)}
        footer={null}
        width={800}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Alert 
            message="预设清洗规则"
            description="选择适合的规则类型添加到当前清洗配置中"
            type="info"
            showIcon
          />
          
          <Row gutter={16}>
            <Col span={12}>
              <Card title="去重规则" size="small">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Button 
                    block 
                    onClick={() => {
                      const rule = {
                        id: Date.now().toString(),
                        name: '智能去重 - 按评分',
                        type: 'smart_deduplication',
                        field: 'asset_value',
                        strategy: 'keep_highest_score',
                        enabled: true
                      };
                      setCleaningRules([...cleaningRules, rule]);
                      setCleaningModalVisible(false);
                      setQueryStatus('已添加智能去重规则');
                    }}
                  >
                    按评分去重
                  </Button>
                  
                  <Button 
                    block 
                    onClick={() => {
                      const rule = {
                        id: Date.now().toString(),
                        name: '模糊匹配去重',
                        type: 'smart_deduplication',
                        field: 'asset_value',
                        strategy: 'fuzzy_matching',
                        similarity_threshold: 0.9,
                        enabled: true
                      };
                      setCleaningRules([...cleaningRules, rule]);
                      setCleaningModalVisible(false);
                      setQueryStatus('已添加模糊匹配去重规则');
                    }}
                  >
                    模糊匹配去重
                  </Button>
                </Space>
              </Card>
            </Col>
            
            <Col span={12}>
              <Card title="字段映射" size="small">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Button 
                    block 
                    onClick={() => {
                      const rule = {
                        id: Date.now().toString(),
                        name: '标准字段映射',
                        type: 'field_mapping',
                        mapping_rules: {
                          'asset_value': 'metadata.domain',
                          'asset_type': 'metadata.asset_type',
                          'confidence': 'score'
                        },
                        enabled: true
                      };
                      setCleaningRules([...cleaningRules, rule]);
                      setCleaningModalVisible(false);
                      setQueryStatus('已添加字段映射规则');
                    }}
                  >
                    标准字段映射
                  </Button>
                  
                  <Button 
                    block 
                    onClick={() => {
                      const rule = {
                        id: Date.now().toString(),
                        name: '多源数据统一',
                        type: 'field_mapping',
                        mapping_rules: {
                          'domain': 'data.value',
                          'ip': 'data.ip_address',
                          'port': 'data.port_number'
                        },
                        enabled: true
                      };
                      setCleaningRules([...cleaningRules, rule]);
                      setCleaningModalVisible(false);
                      setQueryStatus('已添加多源数据统一规则');
                    }}
                  >
                    多源数据统一
                  </Button>
                </Space>
              </Card>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Card title="数据增强" size="small">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Button 
                    block 
                    onClick={() => {
                      const rule = {
                        id: Date.now().toString(),
                        name: '域名信息增强',
                        type: 'data_enrichment',
                        enrichment_rules: [
                          { type: 'domain_info' }
                        ],
                        enabled: true
                      };
                      setCleaningRules([...cleaningRules, rule]);
                      setCleaningModalVisible(false);
                      setQueryStatus('已添加域名信息增强规则');
                    }}
                  >
                    域名信息增强
                  </Button>
                  
                  <Button 
                    block 
                    onClick={() => {
                      const rule = {
                        id: Date.now().toString(),
                        name: '置信度计算',
                        type: 'data_enrichment',
                        enrichment_rules: [
                          { 
                            type: 'confidence_calculation',
                            factors: {
                              verified_bonus: 0.3,
                              manual_bonus: 0.2
                            }
                          }
                        ],
                        enabled: true
                      };
                      setCleaningRules([...cleaningRules, rule]);
                      setCleaningModalVisible(false);
                      setQueryStatus('已添加置信度计算规则');
                    }}
                  >
                    置信度计算
                  </Button>
                </Space>
              </Card>
            </Col>
            
            <Col span={12}>
              <Card title="数据验证" size="small">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Button 
                    block 
                    onClick={() => {
                      const rule = {
                        id: Date.now().toString(),
                        name: '必填字段验证',
                        type: 'data_validation',
                        validation_rules: [
                          { field: 'asset_value', type: 'required' },
                          { field: 'asset_type', type: 'required' }
                        ],
                        enabled: true
                      };
                      setCleaningRules([...cleaningRules, rule]);
                      setCleaningModalVisible(false);
                      setQueryStatus('已添加必填字段验证规则');
                    }}
                  >
                    必填字段验证
                  </Button>
                  
                  <Button 
                    block 
                    onClick={() => {
                      const rule = {
                        id: Date.now().toString(),
                        name: '格式验证',
                        type: 'data_validation',
                        validation_rules: [
                          { 
                            field: 'ip', 
                            type: 'regex', 
                            pattern: '^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}$'
                          },
                          {
                            field: 'confidence',
                            type: 'range',
                            min: 0,
                            max: 1
                          }
                        ],
                        enabled: true
                      };
                      setCleaningRules([...cleaningRules, rule]);
                      setCleaningModalVisible(false);
                      setQueryStatus('已添加格式验证规则');
                    }}
                  >
                    格式验证
                  </Button>
                </Space>
              </Card>
            </Col>
          </Row>
          
          <div style={{ textAlign: 'center', marginTop: 16 }}>
            <Button onClick={() => setCleaningModalVisible(false)}>
              关闭
            </Button>
          </div>
        </Space>
      </Modal>
    </div>
  );
};

export default SearchAnalytics;