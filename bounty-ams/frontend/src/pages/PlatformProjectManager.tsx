import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  message,
  Space,
  Tag,
  Typography,
  Tabs,
  Descriptions,
  Popconfirm,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ApiOutlined,
  GlobalOutlined,
  DatabaseOutlined,
  ProjectOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { assetService } from '../services/api';
import { OptimizedApiService } from '../services/optimizedApi';
import { useAuth } from '../context/AuthContext';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

interface Platform {
  id: string;
  entity_data: {
    name: string;
    display_name: string;
    platform_type: string;
    website_url?: string;
    api_base_url?: string;
    description?: string;
    status: string;
    supported_asset_types?: string[];
  };
}

interface Project {
  id: string;
  entity_data: {
    name: string;
    platform_id: string;
    external_id?: string;
    company_name?: string;
    program_type: string;
    status: string;
    scope?: any;
    reward_range?: string;
    description?: string;
    tags?: string[];
    priority: string;
  };
}

const PlatformProjectManager: React.FC = () => {
  const { user } = useAuth();  // 获取当前用户信息
  const [platforms, setPlatforms] = useState<Platform[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [modalType, setModalType] = useState<'platform' | 'project'>('platform');
  const [activeTab, setActiveTab] = useState('platforms');
  const [form] = Form.useForm();
  const navigate = useNavigate();

  // 获取模型类型ID
  const [platformModelId, setPlatformModelId] = useState<string>('');
  const [projectModelId, setProjectModelId] = useState<string>('');

  // 项目资产统计
  const [projectAssetCounts, setProjectAssetCounts] = useState<{[key: string]: number}>({});
  // 平台资产统计
  const [platformAssetCounts, setPlatformAssetCounts] = useState<{[key: string]: number}>({});

  // 添加缺失的filters状态
  const [filters, setFilters] = useState({
    platform_id: '',
  });

  useEffect(() => {
    loadModelTypes();
  }, []);

  // 移除原有的分别加载逻辑，统一在loadModelTypes中处理

  const loadModelTypes = async () => {
    try {
      // 使用优化的批量加载API
      const { platforms, projects, modelTypes } = await OptimizedApiService.loadPlatformsAndProjects();
      
      setPlatforms(platforms);
      setProjects(projects);
      
      if (modelTypes.platform) setPlatformModelId(modelTypes.platform.id);
      if (modelTypes.project) setProjectModelId(modelTypes.project.id);
      
      // 批量加载平台资产数量
      if (platforms.length > 0) {
        const platformIds = platforms.map((p: any) => p.id);
        const counts = await OptimizedApiService.loadPlatformAssetCounts(platformIds);
        setPlatformAssetCounts(counts);
      }
      
      // 批量加载项目资产数量
      if (projects.length > 0) {
        const projectIds = projects.map((p: any) => p.id);
        const counts = await OptimizedApiService.loadProjectAssetCounts(projectIds);
        setProjectAssetCounts(counts);
      }
    } catch (error) {
      message.error('加载平台和项目数据失败');
    }
  };

  // 强制刷新数据（不使用缓存）
  const forceRefreshData = async () => {
    try {
      // 直接调用API，不使用缓存
      const modelTypesResponse = await assetService.getDynamicModelTypes();
      const modelTypes = modelTypesResponse.data;
      
      const platformType = modelTypes.find((t: any) => t.name === 'platform');
      const projectType = modelTypes.find((t: any) => t.name === 'project');
      
      if (platformType) {
        setPlatformModelId(platformType.id);
        const platformsResponse = await assetService.getDynamicEntities(platformType.id);
        const platforms = platformsResponse.data || [];
        setPlatforms(platforms);
        
        // 重新加载平台资产数量
        if (platforms.length > 0) {
          const platformIds = platforms.map((p: any) => p.id);
          const counts = await OptimizedApiService.loadPlatformAssetCounts(platformIds);
          setPlatformAssetCounts(counts);
        }
      }
      
      if (projectType) {
        setProjectModelId(projectType.id);
        const projectsResponse = await assetService.getDynamicEntities(projectType.id);
        const projects = projectsResponse.data || [];
        setProjects(projects);
        
        // 重新加载项目资产数量
        if (projects.length > 0) {
          const projectIds = projects.map((p: any) => p.id);
          const counts = await OptimizedApiService.loadProjectAssetCounts(projectIds);
          setProjectAssetCounts(counts);
        }
      }
    } catch (error) {
      message.error('刷新数据失败');
    }
  };

  // 刷新数据函数
  const refreshData = async () => {
    setLoading(true);
    try {
      await loadModelTypes();
    } finally {
      setLoading(false);
    }
  };

  const handleViewProjectAssets = (project: Project) => {
    // 跳转到资产页面，并设置项目过滤器
    navigate(`/assets?platform_id=${project.entity_data.platform_id}&project_id=${project.id}`);
  };

  const handleViewPlatformAssets = (platform: Platform) => {
    // 跳转到资产页面，并设置平台过滤器
    navigate(`/assets?platform_id=${platform.id}`);
  };

  const loadProjectsByPlatform = async (platformId: string) => {
    try {
      setLoading(true);
      // 使用优化的API获取所有项目数据，然后在前端筛选
      const { projects: allProjects } = await OptimizedApiService.loadPlatformsAndProjects();
      const filteredProjects = allProjects.filter((p: any) => p.entity_data.platform_id === platformId);
      setProjects(filteredProjects);
    } catch (error) {
      message.error('加载项目列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleViewPlatformProjects = (platform: Platform) => {
    // 跳转到当前页面的项目Tab，并设置平台过滤器
    setActiveTab('projects');
    setFilters(prev => ({ ...prev, platform_id: platform.id }));
    // 重新加载项目列表，过滤该平台的项目
    loadProjectsByPlatform(platform.id);
  };

  const handleAdd = (type: 'platform' | 'project') => {
    setModalType(type);
    setEditingItem(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (item: any, type: 'platform' | 'project') => {
    setModalType(type);
    setEditingItem(item);
    form.setFieldsValue(item.entity_data);
    setModalVisible(true);
  };

  const handleDelete = async (id: string, type: 'platform' | 'project') => {
    try {
      console.log(`正在删除${type}，ID: ${id}`);
      await assetService.deleteDynamicEntity(id);
      message.success(`${type === 'platform' ? '平台' : '项目'}删除成功`);
      
      // 清除平台项目相关缓存
      OptimizedApiService.clearCache('platforms');
      
      // 强制刷新数据，不使用缓存
      setLoading(true);
      await forceRefreshData();
      setLoading(false);
    } catch (error: any) {
      console.error('删除失败:', error);
      const errorMessage = error?.response?.data?.detail || error?.message || '删除失败';
      message.error(`${type === 'platform' ? '平台' : '项目'}删除失败: ${errorMessage}`);
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      const modelTypeId = modalType === 'platform' ? platformModelId : projectModelId;
      
      if (editingItem) {
        // 更新
        await assetService.updateDynamicEntity(editingItem.id, {
          entity_data: values
        });
        message.success('更新成功');
      } else {
        // 创建
        await assetService.createDynamicEntity({
          model_type_id: modelTypeId,
          entity_data: values
        });
        message.success('创建成功');
      }

      setModalVisible(false);
      
      // 清除平台项目相关缓存
      OptimizedApiService.clearCache('platforms');
      
      // 强制刷新数据，不使用缓存
      setLoading(true);
      await forceRefreshData();
      setLoading(false);
    } catch (error) {
      message.error(editingItem ? '更新失败' : '创建失败');
    }
  };

  const platformColumns = [
    {
      title: '平台名称',
      dataIndex: ['entity_data', 'display_name'],
      key: 'display_name',
      render: (text: string, record: Platform) => (
        <Space>
          <GlobalOutlined />
          <strong>{text}</strong>
          <Tag color="blue">{record.entity_data.name}</Tag>
        </Space>
      ),
    },
    {
      title: '平台类型',
      dataIndex: ['entity_data', 'platform_type'],
      key: 'platform_type',
      render: (type: string) => {
        const typeMap: { [key: string]: { color: string; text: string } } = {
          bug_bounty: { color: 'red', text: '漏洞赏金' },
          vendor_vdp: { color: 'green', text: '厂商VDP' },
          government: { color: 'blue', text: '政府部门' },
          private: { color: 'purple', text: '私人项目' },
        };
        const config = typeMap[type] || { color: 'default', text: type };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '项目数量',
      key: 'project_count',
      render: (record: Platform) => {
        const projectCount = projects.filter(p => p.entity_data.platform_id === record.id).length;
        return (
          <Tag 
            color="cyan" 
            style={{ cursor: 'pointer' }}
            onClick={() => handleViewPlatformProjects(record)}
            title="点击查看项目"
          >
            {projectCount} 个项目
          </Tag>
        );
      },
    },
    {
      title: '资产数量',
      key: 'asset_count',
      render: (record: Platform) => {
        const assetCount = platformAssetCounts[record.id] || 0;
        return (
          <Tag 
            color={assetCount > 0 ? 'green' : 'default'}
            style={{ cursor: assetCount > 0 ? 'pointer' : 'default' }}
            onClick={() => assetCount > 0 && handleViewPlatformAssets(record)}
            title={assetCount > 0 ? "点击查看资产" : "暂无资产"}
          >
            {assetCount} 个资产
          </Tag>
        );
      },
    },
    {
      title: '状态',
      dataIndex: ['entity_data', 'status'],
      key: 'status',
      render: (status: string) => {
        const statusMap: { [key: string]: string } = {
          active: 'success',
          inactive: 'default',
          maintenance: 'warning',
          deprecated: 'error',
        };
        return <Tag color={statusMap[status] || 'default'}>{status}</Tag>;
      },
    },
    {
      title: '网站',
      dataIndex: ['entity_data', 'website_url'],
      key: 'website_url',
      render: (url: string) => 
        url ? (
          <a href={url} target="_blank" rel="noopener noreferrer">
            {url}
          </a>
        ) : '-',
    },
    {
      title: '操作',
      key: 'action',
      render: (record: Platform) => (
        <Space>
          <Button
            type="text"
            icon={<ProjectOutlined />}
            onClick={() => handleViewPlatformProjects(record)}
            title="查看项目"
          >
            项目
          </Button>
          <Button
            type="text"
            icon={<DatabaseOutlined />}
            onClick={() => handleViewPlatformAssets(record)}
            title="查看资产"
          >
            资产
          </Button>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => {/* 查看详情 */}}
            title="查看详情"
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record, 'platform')}
            title="编辑"
            disabled={!user?.is_admin}
          />
          {user?.is_admin && (
            <Popconfirm
              title="确定删除此平台吗？"
              onConfirm={() => handleDelete(record.id, 'platform')}
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                title="删除"
              />
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  const projectColumns = [
    {
      title: '项目名称',
      dataIndex: ['entity_data', 'name'],
      key: 'name',
      render: (text: string, record: Project) => (
        <Space>
          <ApiOutlined />
          <strong 
            style={{ cursor: 'pointer', color: '#1890ff' }}
            onClick={() => handleViewProjectAssets(record)}
            title="点击查看项目资产"
          >
            {text}
          </strong>
          {record.entity_data.external_id && (
            <Tag>{record.entity_data.external_id}</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '公司',
      dataIndex: ['entity_data', 'company_name'],
      key: 'company_name',
      render: (text: string) => text || '-',
    },
    {
      title: '所属平台',
      dataIndex: ['entity_data', 'platform_id'],
      key: 'platform_id',
      render: (platformId: string) => {
        const platform = platforms.find(p => p.id === platformId);
        return platform ? (
          <Tag color="blue">{platform.entity_data.display_name}</Tag>
        ) : (
          <Tag color="default">未知平台</Tag>
        );
      },
    },
    {
      title: '资产数量',
      key: 'asset_count',
      render: (record: Project) => {
        const assetCount = projectAssetCounts[record.id] || 0;
        return (
          <Tag 
            color={assetCount > 0 ? 'green' : 'default'}
            style={{ cursor: assetCount > 0 ? 'pointer' : 'default' }}
            onClick={() => assetCount > 0 && handleViewProjectAssets(record)}
            title={assetCount > 0 ? "点击查看资产" : "暂无资产"}
          >
            {assetCount} 个资产
          </Tag>
        );
      },
    },
    {
      title: '项目类型',
      dataIndex: ['entity_data', 'program_type'],
      key: 'program_type',
      render: (type: string) => {
        const typeMap: { [key: string]: { color: string; text: string } } = {
          public: { color: 'green', text: '公开项目' },
          private: { color: 'orange', text: '私有项目' },
          invite_only: { color: 'purple', text: '邀请制' },
          vdp: { color: 'blue', text: '漏洞披露' },
          bounty: { color: 'red', text: '赏金项目' },
        };
        const config = typeMap[type] || { color: 'default', text: type };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '优先级',
      dataIndex: ['entity_data', 'priority'],
      key: 'priority',
      render: (priority: string) => {
        const priorityMap: { [key: string]: string } = {
          high: 'red',
          medium: 'orange',
          low: 'green',
        };
        return <Tag color={priorityMap[priority] || 'default'}>{priority}</Tag>;
      },
    },
    {
      title: '状态',
      dataIndex: ['entity_data', 'status'],
      key: 'status',
      render: (status: string) => {
        const statusMap: { [key: string]: string } = {
          active: 'success',
          paused: 'warning',
          closed: 'default',
          archived: 'error',
        };
        return <Tag color={statusMap[status] || 'default'}>{status}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (record: Project) => (
        <Space>
          <Button
            type="text"
            icon={<DatabaseOutlined />}
            onClick={() => handleViewProjectAssets(record)}
            title="查看资产"
          >
            资产
          </Button>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => {/* 查看详情 */}}
            title="查看详情"
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record, 'project')}
            title="编辑"
            disabled={!user?.is_admin}
          />
          {user?.is_admin && (
            <Popconfirm
              title="确定删除此项目吗？"
              onConfirm={() => handleDelete(record.id, 'project')}
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                title="删除"
              />
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  const renderPlatformForm = () => (
    <>
      <Form.Item name="name" label="平台标识" rules={[{ required: true }]}>
        <Input placeholder="如: hackerone, msrc" />
      </Form.Item>
      <Form.Item name="display_name" label="显示名称" rules={[{ required: true }]}>
        <Input placeholder="如: HackerOne, Microsoft MSRC" />
      </Form.Item>
      <Form.Item name="platform_type" label="平台类型" rules={[{ required: true }]}>
        <Select>
          <Option value="bug_bounty">漏洞赏金</Option>
          <Option value="vendor_vdp">厂商VDP</Option>
          <Option value="government">政府部门</Option>
          <Option value="private">私人项目</Option>
          <Option value="crowdsourced">众包平台</Option>
          <Option value="other">其他</Option>
        </Select>
      </Form.Item>
      <Form.Item name="website_url" label="官网地址">
        <Input placeholder="https://..." />
      </Form.Item>
      <Form.Item name="api_base_url" label="API地址">
        <Input placeholder="https://api..." />
      </Form.Item>
      <Form.Item name="description" label="平台描述">
        <Input.TextArea rows={3} />
      </Form.Item>
      <Form.Item name="status" label="状态" initialValue="active">
        <Select>
          <Option value="active">活跃</Option>
          <Option value="inactive">不活跃</Option>
          <Option value="maintenance">维护中</Option>
          <Option value="deprecated">已废弃</Option>
        </Select>
      </Form.Item>
    </>
  );

  const renderProjectForm = () => (
    <>
      <Form.Item name="name" label="项目名称" rules={[{ required: true }]}>
        <Input placeholder="如: Shopify Bug Bounty" />
      </Form.Item>
      <Form.Item name="platform_id" label="所属平台" rules={[{ required: true }]}>
        <Select placeholder="选择平台">
          {platforms.map(platform => (
            <Option key={platform.id} value={platform.id}>
              {platform.entity_data.display_name}
            </Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item name="external_id" label="外部项目ID">
        <Input placeholder="在外部平台的项目ID" />
      </Form.Item>
      <Form.Item name="company_name" label="公司名称">
        <Input placeholder="如: Shopify" />
      </Form.Item>
      <Form.Item name="program_type" label="项目类型" rules={[{ required: true }]}>
        <Select>
          <Option value="public">公开项目</Option>
          <Option value="private">私有项目</Option>
          <Option value="invite_only">邀请制</Option>
          <Option value="vdp">漏洞披露</Option>
          <Option value="bounty">赏金项目</Option>
        </Select>
      </Form.Item>
      <Form.Item name="reward_range" label="奖励范围">
        <Input placeholder="如: $100 - $25,000" />
      </Form.Item>
      <Form.Item name="description" label="项目描述">
        <Input.TextArea rows={3} />
      </Form.Item>
      <Form.Item name="priority" label="优先级" initialValue="medium">
        <Select>
          <Option value="high">高</Option>
          <Option value="medium">中</Option>
          <Option value="low">低</Option>
        </Select>
      </Form.Item>
      <Form.Item name="status" label="状态" initialValue="active">
        <Select>
          <Option value="active">活跃</Option>
          <Option value="paused">暂停</Option>
          <Option value="closed">关闭</Option>
          <Option value="archived">归档</Option>
        </Select>
      </Form.Item>
    </>
  );

  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Title level={3}>平台项目管理</Title>
          <Text type="secondary">
            管理漏洞赏金平台和项目信息，为资产管理提供分类基础
          </Text>
        </Col>
      </Row>

      <Card style={{ marginTop: 16 }}>
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          tabBarExtraContent={
            user?.is_admin && (
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => handleAdd(activeTab === 'platforms' ? 'platform' : 'project')}
              >
                添加{activeTab === 'platforms' ? '平台' : '项目'}
              </Button>
            )
          }
        >
          <TabPane tab={`平台管理 (${platforms.length})`} key="platforms">
            <Table
              columns={platformColumns}
              dataSource={platforms}
              rowKey="id"
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              }}
            />
          </TabPane>
          <TabPane tab={`项目管理 (${projects.length})`} key="projects">
            {/* 显示筛选状态 */}
            {filters.platform_id && (
              <div style={{ marginBottom: 16 }}>
                <Tag color="blue" closable onClose={() => {
                  setFilters(prev => ({ ...prev, platform_id: '' }));
                  refreshData(); // 重新加载所有项目
                }}>
                  筛选平台: {platforms.find(p => p.id === filters.platform_id)?.entity_data?.display_name || '未知平台'}
                </Tag>
              </div>
            )}
            <Table
              columns={projectColumns}
              dataSource={projects}
              rowKey="id"
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      <Modal
        title={`${editingItem ? '编辑' : '添加'}${modalType === 'platform' ? '平台' : '项目'}`}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          {modalType === 'platform' ? renderPlatformForm() : renderProjectForm()}
        </Form>
      </Modal>
    </div>
  );
};

export default PlatformProjectManager;