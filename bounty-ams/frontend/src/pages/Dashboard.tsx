import React, { useState, useEffect, useRef } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Tag,
  Typography,
  Space,
  Button,
  Progress,
  List,
  Avatar,
  Spin,
} from 'antd';
import {
  DatabaseOutlined,
  SecurityScanOutlined,
  RobotOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  SyncOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import { assetService, agentService, taskService, dashboardService } from '../services/api';
import LoadingSkeleton from '../components/LoadingSkeleton';

const { Title, Text } = Typography;

// 简单的内存缓存
const cache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

const getCachedData = (key: string) => {
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }
  return null;
};

const setCachedData = (key: string, data: any) => {
  cache.set(key, {
    data,
    timestamp: Date.now()
  });
};

const Dashboard: React.FC = () => {
  const [assetStats, setAssetStats] = useState<any>({});
  const [agentStats, setAgentStats] = useState<any>({});
  const [taskStats, setTaskStats] = useState<any>({});
  const [recentAssets, setRecentAssets] = useState<any[]>([]);
  const [recentTasks, setRecentTasks] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async (forceRefresh = false) => {
    try {
      setLoading(true);
      console.log('开始加载仪表板数据...');
      
      // 检查缓存（除非强制刷新）
      if (!forceRefresh) {
        const cachedStats = getCachedData('dashboard_stats');
        if (cachedStats) {
          console.log('使用缓存的仪表板数据');
          setAssetStats(cachedStats.assetStats);
          setAgentStats(cachedStats.agentStats);
          setTaskStats(cachedStats.taskStats);
          setRecentAssets(cachedStats.recentAssets);
          setRecentTasks(cachedStats.recentTasks);
          setLoading(false);
          return;
        }
      }
      
      // 并行加载所有仪表盘数据
      const [
        assetStatsResponse,
        taskStatsResponse,
        agentResponse,
        recentAssetsResponse,
        recentTasksResponse
      ] = await Promise.allSettled([
        dashboardService.getAssetStats(),
        dashboardService.getTaskStats(),
        agentService.getAgents(),
        dashboardService.getRecentAssets(10),
        dashboardService.getRecentTasks(10)
      ]);
      
      let newAssetStats = {};
      let newAgentStats = { total: 0, active: 0, idle: 0, offline: 0, online: 0 };
      let newTaskStats = {};
      let newRecentAssets: any[] = [];
      let newRecentTasks: any[] = [];
      
      // 处理资产统计
      if (assetStatsResponse.status === 'fulfilled') {
        console.log('资产统计数据:', assetStatsResponse.value.data);
        newAssetStats = assetStatsResponse.value.data || {};
      } else {
        console.warn('资产统计加载失败:', assetStatsResponse.reason);
      }
      
      // 处理任务统计
      if (taskStatsResponse.status === 'fulfilled') {
        console.log('任务统计数据:', taskStatsResponse.value.data);
        newTaskStats = taskStatsResponse.value.data || {};
      } else {
        console.warn('任务统计加载失败:', taskStatsResponse.reason);
      }
      
      // 处理代理统计
      if (agentResponse.status === 'fulfilled') {
        const agents = agentResponse.value.data?.agents || [];
        console.log('代理数据:', agents);
        
        // 计算在线状态 - 检查 last_seen_at 时间判断是否真正在线
        const now = new Date().getTime();
        const onlineThreshold = 5 * 60 * 1000; // 5分钟内有心跳认为在线
        
        const onlineAgents = agents.filter((agent: any) => {
          if (agent.status === 'offline') return false;
          
          const lastSeen = new Date(agent.last_seen_at).getTime();
          return (now - lastSeen) < onlineThreshold;
        });
        
        const offlineAgents = agents.filter((agent: any) => {
          if (agent.status === 'offline') return true;
          
          const lastSeen = new Date(agent.last_seen_at).getTime();
          return (now - lastSeen) >= onlineThreshold;
        });
        
        // 根据任务负载判断状态
        const activeAgents = onlineAgents.filter((agent: any) => agent.current_tasks > 0);
        const idleAgents = onlineAgents.filter((agent: any) => agent.current_tasks === 0);
        
        newAgentStats = {
          total: agents.length,
          active: activeAgents.length,
          idle: idleAgents.length, 
          offline: offlineAgents.length,
          online: onlineAgents.length
        };
        
        console.log('代理统计:', newAgentStats);
      } else {
        console.warn('代理统计加载失败:', agentResponse.reason);
      }
      
      // 处理最近资产
      if (recentAssetsResponse.status === 'fulfilled') {
        const assets = recentAssetsResponse.value.data?.assets || [];
        console.log('最近资产数据:', assets);
        newRecentAssets = assets.slice(0, 5);
      } else {
        console.warn('最近资产加载失败:', recentAssetsResponse.reason);
      }
      
      // 处理最近任务
      if (recentTasksResponse.status === 'fulfilled') {
        const tasks = recentTasksResponse.value.data?.tasks || [];
        console.log('最近任务数据:', tasks);
        newRecentTasks = tasks.slice(0, 5);
      } else {
        console.warn('最近任务加载失败:', recentTasksResponse.reason);
      }
      
      // 缓存数据
      const dashboardData = {
        assetStats: newAssetStats,
        agentStats: newAgentStats,
        taskStats: newTaskStats,
        recentAssets: newRecentAssets,
        recentTasks: newRecentTasks
      };
      setCachedData('dashboard_stats', dashboardData);
      
      // 更新状态
      setAssetStats(newAssetStats);
      setAgentStats(newAgentStats);
      setTaskStats(newTaskStats);
      setRecentAssets(newRecentAssets);
      setRecentTasks(newRecentTasks);
      
    } catch (error) {
      console.error('仪表板数据加载失败:', error);
      // 设置默认空数据，确保页面能正常显示
      setAssetStats({});
      setAgentStats({ total: 0, active: 0, idle: 0, offline: 0, online: 0 });
      setTaskStats({});
      setRecentAssets([]);
      setRecentTasks([]);
    } finally {
      setLoading(false);
      console.log('仪表板数据加载完成');
    }
  };

  const getAssetTypeChartOption = () => {
    // 支持多种数据格式
    const typeData = assetStats.by_type || assetStats.asset_types || {};
    const data = Object.entries(typeData).map(([key, value]) => ({
      name: key,
      value: value,
    }));

    return {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
      },
      series: [
        {
          name: '资产类型',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 16,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: data
        }
      ]
    };
  };

  const getAssetStatusChartOption = () => {
    // 资产状态分布图
    const statusData = assetStats.by_status || {};
    const data = Object.entries(statusData).map(([key, value]) => ({
      name: key,
      value: value,
    }));

    return {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'horizontal',
        bottom: 'bottom',
      },
      series: [
        {
          name: '资产状态',
          type: 'pie',
          radius: '55%',
          center: ['50%', '45%'],
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
  };

  const getTaskTrendChartOption = () => {
    // 支持多种数据格式
    const dateData = assetStats.by_date || assetStats.timeline || {};
    const dates = Object.keys(dateData).sort();
    const data = dates.map(date => dateData[date]);

    // 如果没有时间数据，生成一个简单的示例
    if (dates.length === 0) {
      const now = new Date();
      const days = [];
      const values = [];
      for (let i = 6; i >= 0; i--) {
        const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
        days.push(date.toLocaleDateString());
        values.push(Math.max(0, assetStats.total_assets || 0 - i));
      }
      return {
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: days
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '累计资产',
            type: 'line',
            smooth: true,
            itemStyle: {
              color: '#1890ff'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                  { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
                ]
              }
            },
            data: values
          }
        ]
      };
    }

    return {
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: dates
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '发现资产',
          type: 'line',
          smooth: true,
          itemStyle: {
            color: '#1890ff'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
              ]
            }
          },
          data: data
        }
      ]
    };
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'running':
        return <SyncOutlined spin style={{ color: '#1890ff' }} />;
      case 'pending':
        return <ClockCircleOutlined style={{ color: '#faad14' }} />;
      case 'failed':
        return <ExclamationCircleOutlined style={{ color: '#f5222d' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'running':
        return 'processing';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <div style={{ padding: 24 }}>
        <LoadingSkeleton type="dashboard" loading={loading} />
      </div>
    );
  }

  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Space align="center" style={{ marginBottom: 16 }}>
            <Title level={3} style={{ margin: 0 }}>
              系统概览
            </Title>
            <Button 
              icon={<SyncOutlined />} 
              onClick={() => loadDashboardData(true)}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        </Col>
      </Row>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总资产数量"
              value={assetStats.total_assets || assetStats.total || 0}
              prefix={<DatabaseOutlined />}
              suffix={<ArrowUpOutlined style={{ color: '#3f8600' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="在线Agent"
              value={agentStats.online || 0}
              prefix={<RobotOutlined />}
              suffix={`/${agentStats.total || 0}`}
              valueStyle={{ color: agentStats.online > 0 ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="运行中任务"
              value={taskStats.by_status?.running || taskStats.running || 0}
              prefix={<SecurityScanOutlined />}
              suffix={<SyncOutlined spin />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="完成任务"
              value={taskStats.by_status?.completed || taskStats.completed || 0}
              prefix={<CheckCircleOutlined />}
              suffix={<ArrowUpOutlined style={{ color: '#3f8600' }} />}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col xs={24} lg={8}>
          <Card title="资产类型分布">
            <ReactECharts 
              option={getAssetTypeChartOption()} 
              style={{ height: 300 }}
            />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="资产状态分布">
            <ReactECharts 
              option={getAssetStatusChartOption()} 
              style={{ height: 300 }}
            />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="资产发现趋势">
            <ReactECharts 
              option={getTaskTrendChartOption()} 
              style={{ height: 300 }}
            />
          </Card>
        </Col>
      </Row>

      {/* 最近活动 */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col xs={24} lg={12}>
          <Card title="最近发现的资产" extra={<Button type="link">查看更多</Button>}>
            {recentAssets.length > 0 ? (
              <List
                dataSource={recentAssets.slice(0, 5)}
                renderItem={(item) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<Avatar icon={<DatabaseOutlined />} />}
                      title={
                        <Space>
                          <Text strong>{item.asset_value || item.entity_data?.asset_value || '未知资产'}</Text>
                          <Tag color="blue">{item.asset_type || item.entity_data?.asset_type || 'unknown'}</Tag>
                        </Space>
                      }
                      description={
                        <Space>
                          <Text type="secondary">
                            发现时间: {item.discovered_at || item.entity_data?.discovered_at ? 
                              new Date(item.discovered_at || item.entity_data.discovered_at).toLocaleString() : 
                              '未知'}
                          </Text>
                          <Tag color="green">
                            置信度: {item.confidence || item.entity_data?.confidence || 'unknown'}
                          </Tag>
                        </Space>
                      }
                    />
                  </List.Item>
                )}
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
                暂无最近发现的资产
              </div>
            )}
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="最近任务" extra={<Button type="link">查看更多</Button>}>
            {recentTasks.length > 0 ? (
              <List
                dataSource={recentTasks.slice(0, 5)}
                renderItem={(item) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<Avatar icon={getStatusIcon(item.status)} />}
                      title={
                        <Space>
                          <Text strong>{item.task_id || item.id || '未知任务'}</Text>
                          <Tag color={getStatusColor(item.status)}>{item.status || 'unknown'}</Tag>
                        </Space>
                      }
                      description={
                        <Space>
                          <Text type="secondary">
                            类型: {item.task_type || 'unknown'}
                          </Text>
                          <Text type="secondary">
                            目标: {item.target || '未知'}
                          </Text>
                          <Text type="secondary">
                            创建时间: {item.created_at ? 
                              new Date(item.created_at).toLocaleString() : 
                              '未知'}
                          </Text>
                        </Space>
                      }
                    />
                  </List.Item>
                )}
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
                暂无最近任务
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* Agent状态 */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col xs={24} lg={16}>
          <Card title="Agent状态">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={8}>
                <div style={{ textAlign: 'center' }}>
                  <Progress 
                    type="circle" 
                    percent={agentStats.total > 0 ? Math.round((agentStats.online / agentStats.total) * 100) : 0}
                    format={(percent) => `${agentStats.online}/${agentStats.total}`}
                    strokeColor="#52c41a"
                  />
                  <div style={{ marginTop: 8 }}>
                    <Text>在线Agent</Text>
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={8}>
                <div style={{ textAlign: 'center' }}>
                  <Progress 
                    type="circle" 
                    percent={agentStats.online > 0 ? Math.round((agentStats.active / agentStats.online) * 100) : 0}
                    format={(percent) => `${agentStats.active}/${agentStats.online}`}
                    strokeColor="#1890ff"
                  />
                  <div style={{ marginTop: 8 }}>
                    <Text>执行中Agent</Text>
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={8}>
                <div style={{ textAlign: 'center' }}>
                  <Progress 
                    type="circle" 
                    percent={agentStats.total > 0 ? Math.round((agentStats.offline / agentStats.total) * 100) : 0}
                    format={(percent) => `${agentStats.offline}/${agentStats.total}`}
                    strokeColor="#f5222d"
                  />
                  <div style={{ marginTop: 8 }}>
                    <Text>离线Agent</Text>
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="Agent统计" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text>总数量</Text>
                <Tag color="blue">{agentStats.total || 0}</Tag>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text>在线</Text>
                <Tag color="green">{agentStats.online || 0}</Tag>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text>离线</Text>
                <Tag color="red">{agentStats.offline || 0}</Tag>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text>执行任务</Text>
                <Tag color="processing">{agentStats.active || 0}</Tag>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text>空闲</Text>
                <Tag color="default">{agentStats.idle || 0}</Tag>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;