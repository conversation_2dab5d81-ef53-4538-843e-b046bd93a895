import React, { useState, useEffect } from 'react';
import {
  Card,
  Steps,
  Upload,
  Button,
  Select,
  Table,
  Form,
  Input,
  message,
  Typography,
  Space,
  Divider,
  Row,
  Col,
  Switch,
  Modal,
  Progress,
  Alert,
  Tag,
  Tooltip,
  Empty,
} from 'antd';
import {
  UploadOutlined,
  InboxOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  ArrowRightOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import { dataImportService } from '../services/api';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;
const { Option } = Select;
const { Dragger } = Upload;

interface DetectedField {
  field_name: string;
  display_name: string;
  detected_type: string;
  sample_values: any[];
  null_count: number;
  confidence: number;
}

interface FieldMapping {
  source_field: string;
  target_field: string | null;
  mapping_confidence: number;
  detected_type: string;
  target_type: string | null;
  auto_mapped: boolean;
}

interface AssetType {
  id: string;
  name: string;
  display_name: string;
  description: string;
  field_count: number;
}

interface PlatformProject {
  platform_id: string;
  project_id: string;
  platform_name: string;
  project_name: string;
}

interface ImportPreview {
  file_info: {
    filename: string;
    size: number;
    type: string;
  };
  preview: {
    total_records: number;
    sample_data: any[];
    detected_fields: DetectedField[];
  };
  target_fields: any[];
  suggested_mappings: FieldMapping[];
  can_create_new_type: boolean;
}

const DataImport: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [assetTypes, setAssetTypes] = useState<AssetType[]>([]);
  const [platforms, setPlatforms] = useState<PlatformProject[]>([]);
  const [selectedPlatform, setSelectedPlatform] = useState<string | null>(null);
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const [selectedAssetType, setSelectedAssetType] = useState<string | null>(null);
  const [createNewType, setCreateNewType] = useState(false);
  const [importPreview, setImportPreview] = useState<ImportPreview | null>(null);
  const [fieldMappings, setFieldMappings] = useState<FieldMapping[]>([]);
  const [loading, setLoading] = useState(false);
  const [importResults, setImportResults] = useState<any>(null);
  const [newTypeForm] = Form.useForm();
  const [fieldTypes, setFieldTypes] = useState<any[]>([]);

  useEffect(() => {
    loadAssetTypes();
    loadFieldTypes();
    loadPlatforms();
  }, []);

  const loadAssetTypes = async () => {
    try {
      const response = await dataImportService.getAssetTypes();
      setAssetTypes(Array.isArray(response.data) ? response.data : []);
    } catch (error) {
      console.error('加载资产类型失败:', error);
      message.error('加载资产类型失败');
      setAssetTypes([]);
    }
  };

  const loadFieldTypes = async () => {
    try {
      const response = await dataImportService.getFieldTypes();
      setFieldTypes(Array.isArray(response.data) ? response.data : []);
    } catch (error) {
      console.error('加载字段类型失败:', error);
      message.error('加载字段类型失败');
      setFieldTypes([]);
    }
  };

  const loadPlatforms = async () => {
    try {
      const response = await dataImportService.getPlatforms();
      setPlatforms(Array.isArray(response.data) ? response.data : []);
    } catch (error) {
      console.error('加载平台项目失败:', error);
      message.error('加载平台项目失败');
      setPlatforms([]);
    }
  };

  const uploadProps: UploadProps = {
    beforeUpload: () => false, // 阻止自动上传
    accept: '.csv,.json',
    maxCount: 1,
    fileList,
    onChange: ({ fileList: newFileList }) => {
      setFileList(newFileList);
    },
  };

  const analyzeFile = async () => {
    if (!fileList.length) {
      message.error('请先选择文件');
      return;
    }

    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', fileList[0].originFileObj as File);
      
      if (!createNewType && selectedAssetType) {
        formData.append('asset_type_id', selectedAssetType);
      }
      formData.append('create_new_type', createNewType.toString());

      const response = await dataImportService.analyzeFile(formData);
      setImportPreview(response.data || response);
      setFieldMappings((response.data || response).suggested_mappings || []);
      setCurrentStep(2);
      message.success('文件分析完成');
    } catch (error: any) {
      message.error(error.response?.data?.detail || '文件分析失败');
    }
    setLoading(false);
  };

  const executeImport = async () => {
    if (!importPreview || !fileList.length) {
      message.error('请先完成文件分析');
      return;
    }

    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', fileList[0].originFileObj as File);
      formData.append('mappings', JSON.stringify(fieldMappings));
      
      // 添加平台项目信息
      if (selectedPlatform) {
        formData.append('platform_id', selectedPlatform);
      }
      if (selectedProject) {
        formData.append('project_id', selectedProject);
      }
      
      if (createNewType) {
        const newTypeValues = await newTypeForm.validateFields();
        const newTypeConfig = {
          name: newTypeValues.name,
          display_name: newTypeValues.display_name,
          description: newTypeValues.description || '',
          fields: fieldMappings.map(mapping => ({
            field_name: mapping.target_field,
            field_type: mapping.target_type,
            display_name: mapping.target_field,
            is_required: false,
            is_searchable: true,
            is_filterable: true,
            sort_order: 0
          }))
        };
        formData.append('new_type_config', JSON.stringify(newTypeConfig));
        formData.append('create_new_type', 'true');
      } else {
        formData.append('asset_type_id', selectedAssetType!);
        formData.append('create_new_type', 'false');
      }

      const response = await dataImportService.executeImport(formData);
      const result = response.data || response;
      setImportResults(result);
      setCurrentStep(3);
      message.success(`成功导入 ${result.results?.successful_imports || 0} 条记录`);
    } catch (error: any) {
      message.error(error.response?.data?.detail || '导入失败');
    }
    setLoading(false);
  };

  const updateFieldMapping = (index: number, field: string, value: any) => {
    const newMappings = [...fieldMappings];
    newMappings[index] = { ...newMappings[index], [field]: value };
    setFieldMappings(newMappings);
  };

  const applyUnmappedFields = async () => {
    if (!selectedAssetType || createNewType) return;
    
    const unmappedFields = fieldMappings.filter(mapping => !mapping.target_field);
    if (unmappedFields.length === 0) {
      message.info('没有未映射的字段');
      return;
    }

    try {
      setLoading(true);
      // 调用API为资产类型添加新字段
      const newFields = unmappedFields.map(field => ({
        field_name: field.source_field,
        field_type: field.detected_type,
        display_name: field.source_field,
        is_required: false,
        is_searchable: true,
        is_filterable: true,
        sort_order: 999
      }));

      await dataImportService.addFieldsToAssetType(selectedAssetType, newFields);
      
      // 更新映射
      const updatedMappings = fieldMappings.map(mapping => {
        if (!mapping.target_field && unmappedFields.some(f => f.source_field === mapping.source_field)) {
          return {
            ...mapping,
            target_field: mapping.source_field,
            target_type: mapping.detected_type,
            auto_mapped: true,
            mapping_confidence: 1.0
          };
        }
        return mapping;
      });
      
      setFieldMappings(updatedMappings);
      message.success(`成功添加 ${unmappedFields.length} 个字段到资产类型`);
    } catch (error: any) {
      message.error(error.response?.data?.detail || '添加字段失败');
    }
    setLoading(false);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <Card title="选择文件" className="upload-card">
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <Dragger {...uploadProps}>
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                <p className="ant-upload-hint">
                  支持 CSV 和 JSON 格式文件，单次上传一个文件
                </p>
              </Dragger>
              
              {fileList.length > 0 && (
                <Alert
                  message={`已选择文件: ${fileList[0].name}`}
                  type="info"
                  showIcon
                />
              )}
            </Space>
          </Card>
        );

      case 1:
        return (
          <Card title="配置导入参数">
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <div>
                <Text strong>选择目标平台和项目:</Text>
                <Row gutter={16} style={{ marginTop: 8 }}>
                  <Col span={12}>
                    <Select
                      style={{ width: '100%' }}
                      placeholder="选择平台"
                      value={selectedPlatform}
                      onChange={(value) => {
                        setSelectedPlatform(value);
                        setSelectedProject(null); // 清空项目选择
                      }}
                      showSearch
                      filterOption={(input, option) => {
                        if (!option?.children || typeof option.children !== 'string') return false;
                        return (option.children as string).toLowerCase().indexOf(input.toLowerCase()) >= 0;
                      }}
                    >
                      {Array.from(new Set(platforms.map(p => p.platform_id))).map(platformId => {
                        const platform = platforms.find(p => p.platform_id === platformId);
                        return (
                          <Option key={platformId} value={platformId}>
                            {platform?.platform_name || platformId}
                          </Option>
                        );
                      })}
                    </Select>
                  </Col>
                  <Col span={12}>
                    <Select
                      style={{ width: '100%' }}
                      placeholder="选择项目"
                      value={selectedProject}
                      onChange={setSelectedProject}
                      disabled={!selectedPlatform}
                      showSearch
                      filterOption={(input, option) => {
                        if (!option?.children || typeof option.children !== 'string') return false;
                        return (option.children as string).toLowerCase().indexOf(input.toLowerCase()) >= 0;
                      }}
                    >
                      {platforms
                        .filter(p => p.platform_id === selectedPlatform)
                        .map(project => (
                          <Option key={project.project_id} value={project.project_id}>
                            {project.project_name || project.project_id}
                          </Option>
                        ))}
                    </Select>
                  </Col>
                </Row>
              </div>

              <div>
                <Space>
                  <Text strong>资产类型选择:</Text>
                  <Switch
                    checkedChildren="创建新类型"
                    unCheckedChildren="使用现有类型"
                    checked={createNewType}
                    onChange={setCreateNewType}
                  />
                </Space>
              </div>

              {!createNewType ? (
                <div>
                  <Text strong>选择现有资产类型:</Text>
                  <Select
                    style={{ width: '100%', marginTop: 8 }}
                    placeholder="请选择资产类型"
                    value={selectedAssetType}
                    onChange={setSelectedAssetType}
                    showSearch
                    filterOption={(input, option) => {
                      if (!option?.children || typeof option.children !== 'string') return false;
                      return (option.children as string).toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    }}
                  >
                    {Array.isArray(assetTypes) && assetTypes.map(type => (
                      <Option key={type.id} value={type.id}>
                        <Space>
                          <Text strong>{type.display_name}</Text>
                          <Text type="secondary">({type.field_count} 个字段)</Text>
                        </Space>
                      </Option>
                    ))}
                  </Select>
                </div>
              ) : (
                <Card title="新建资产类型" size="small">
                  <Form form={newTypeForm} layout="vertical">
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="name"
                          label="类型标识符"
                          rules={[{ required: true, message: '请输入类型标识符' }]}
                        >
                          <Input placeholder="例如: imported_assets" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="display_name"
                          label="显示名称"
                          rules={[{ required: true, message: '请输入显示名称' }]}
                        >
                          <Input placeholder="例如: 导入的资产" />
                        </Form.Item>
                      </Col>
                    </Row>
                    <Form.Item name="description" label="描述">
                      <Input.TextArea placeholder="可选的描述信息" />
                    </Form.Item>
                  </Form>
                </Card>
              )}
            </Space>
          </Card>
        );

      case 2:
        return (
          <Card title="字段映射配置">
            {importPreview && (
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <Alert
                  message={`文件信息: ${importPreview.file_info.filename} (${importPreview.preview.total_records} 条记录)`}
                  type="info"
                  showIcon
                />

                <div>
                  <Row justify="space-between" align="middle">
                    <Col>
                      <Title level={4}>字段映射配置</Title>
                    </Col>
                    <Col>
                      <Space>
                        <Button 
                          type="primary" 
                          ghost
                          onClick={applyUnmappedFields}
                          loading={loading}
                          disabled={createNewType || !selectedAssetType}
                        >
                          一键应用未映射字段
                        </Button>
                      </Space>
                    </Col>
                  </Row>
                  <Table
                    dataSource={fieldMappings}
                    rowKey="source_field"
                    pagination={false}
                    size="small"
                    columns={[
                      {
                        title: '源字段',
                        dataIndex: 'source_field',
                        width: 200,
                        render: (text, record: FieldMapping) => (
                          <Space direction="vertical" size="small">
                            <Text strong>{text}</Text>
                            <Tag color={getTypeColor(record.detected_type)}>
                              {record.detected_type}
                            </Tag>
                          </Space>
                        ),
                      },
                      {
                        title: '样本数据',
                        width: 200,
                        render: (_, record: FieldMapping) => {
                          const detectedField = importPreview.preview.detected_fields.find(
                            f => f.field_name === record.source_field
                          );
                          return (
                            <Space direction="vertical" size="small">
                              {detectedField?.sample_values.slice(0, 3).map((value, i) => (
                                <Text key={i} code>{String(value)}</Text>
                              ))}
                            </Space>
                          );
                        },
                      },
                      {
                        title: '目标字段',
                        width: 200,
                        render: (_, record: FieldMapping, index: number) => (
                          <Input
                            value={record.target_field || ''}
                            placeholder="目标字段名"
                            onChange={e => updateFieldMapping(index, 'target_field', e.target.value)}
                          />
                        ),
                      },
                      {
                        title: '目标类型',
                        width: 150,
                        render: (_, record: FieldMapping, index: number) => (
                          <Select
                            value={record.target_type || record.detected_type}
                            style={{ width: '100%' }}
                            onChange={value => updateFieldMapping(index, 'target_type', value)}
                          >
                            {Array.isArray(fieldTypes) && fieldTypes.map(type => (
                              <Option key={type.type} value={type.type}>
                                {type.display_name}
                              </Option>
                            ))}
                          </Select>
                        ),
                      },
                      {
                        title: '映射状态',
                        width: 120,
                        render: (_, record: FieldMapping) => (
                          <Space>
                            {record.auto_mapped ? (
                              <Tag color="green" icon={<CheckCircleOutlined />}>
                                自动匹配
                              </Tag>
                            ) : (
                              <Tag color="orange" icon={<ExclamationCircleOutlined />}>
                                需要手动配置
                              </Tag>
                            )}
                            {record.mapping_confidence > 0 && (
                              <Tooltip title={`匹配置信度: ${(record.mapping_confidence * 100).toFixed(1)}%`}>
                                <Tag color="blue">{(record.mapping_confidence * 100).toFixed(0)}%</Tag>
                              </Tooltip>
                            )}
                          </Space>
                        ),
                      },
                    ]}
                  />
                </div>

                <div>
                  <Title level={4}>数据预览</Title>
                  <Table
                    dataSource={importPreview.preview.sample_data}
                    rowKey={(_, index) => index!}
                    pagination={false}
                    size="small"
                    scroll={{ x: true }}
                    columns={importPreview.preview.detected_fields.map(field => ({
                      title: field.field_name,
                      dataIndex: field.field_name,
                      width: 150,
                      render: (text) => (
                        <Text ellipsis style={{ maxWidth: 100 }}>
                          {String(text || '-')}
                        </Text>
                      ),
                    }))}
                  />
                </div>
              </Space>
            )}
          </Card>
        );

      case 3:
        return (
          <Card title="导入结果">
            {importResults && (
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <Alert
                  message="导入完成"
                  description={`成功导入 ${importResults.results.successful_imports} 条记录，失败 ${importResults.results.failed_imports} 条`}
                  type={importResults.results.failed_imports > 0 ? 'warning' : 'success'}
                  showIcon
                />

                <Row gutter={16}>
                  <Col span={8}>
                    <Card size="small">
                      <div style={{ textAlign: 'center' }}>
                        <Title level={2} style={{ color: '#52c41a', margin: 0 }}>
                          {importResults.results.successful_imports}
                        </Title>
                        <Text>成功导入</Text>
                      </div>
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card size="small">
                      <div style={{ textAlign: 'center' }}>
                        <Title level={2} style={{ color: '#faad14', margin: 0 }}>
                          {importResults.results.failed_imports}
                        </Title>
                        <Text>导入失败</Text>
                      </div>
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card size="small">
                      <div style={{ textAlign: 'center' }}>
                        <Title level={2} style={{ color: '#1890ff', margin: 0 }}>
                          {importResults.results.total_records}
                        </Title>
                        <Text>总记录数</Text>
                      </div>
                    </Card>
                  </Col>
                </Row>

                {importResults.results.errors && importResults.results.errors.length > 0 && (
                  <div>
                    <Title level={4}>错误详情</Title>
                    <Table
                      dataSource={importResults.results.errors}
                      rowKey="record_index"
                      pagination={{ pageSize: 10 }}
                      size="small"
                      columns={[
                        {
                          title: '记录序号',
                          dataIndex: 'record_index',
                          width: 100,
                        },
                        {
                          title: '错误信息',
                          dataIndex: 'error',
                          ellipsis: true,
                        },
                        {
                          title: '原始数据',
                          dataIndex: 'record_data',
                          width: 200,
                          render: (data) => (
                            <Text code ellipsis>
                              {JSON.stringify(data)}
                            </Text>
                          ),
                        },
                      ]}
                    />
                  </div>
                )}

                <div style={{ textAlign: 'center' }}>
                  <Space>
                    <Button type="primary" onClick={() => window.location.href = '/assets'}>
                      查看导入的资产
                    </Button>
                    <Button onClick={() => {
                      setCurrentStep(0);
                      setFileList([]);
                      setImportPreview(null);
                      setImportResults(null);
                      setFieldMappings([]);
                      setSelectedAssetType(null);
                      setCreateNewType(false);
                    }}>
                      重新导入
                    </Button>
                  </Space>
                </div>
              </Space>
            )}
          </Card>
        );

      default:
        return null;
    }
  };

  const getTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
      text: 'blue',
      number: 'green',
      email: 'purple',
      date: 'orange',
      datetime: 'orange',
      boolean: 'red',
      select: 'cyan',
    };
    return colorMap[type] || 'default';
  };

  const getStepIcon = (step: number) => {
    if (step < currentStep) return <CheckCircleOutlined />;
    if (step === currentStep) return <InfoCircleOutlined />;
    return step + 1;
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0:
        return fileList.length > 0;
      case 1:
        return (createNewType || selectedAssetType) && selectedPlatform && selectedProject;
      case 2:
        return fieldMappings.every(mapping => mapping.target_field);
      default:
        return false;
    }
  };

  const getNextStepText = () => {
    switch (currentStep) {
      case 0:
        return '分析文件';
      case 1:
        return '分析文件';
      case 2:
        return '开始导入';
      default:
        return '下一步';
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <FileTextOutlined /> 数据导入
      </Title>
      <Paragraph type="secondary">
        支持CSV和JSON格式文件的资产数据导入，自动字段类型检测和智能映射。
      </Paragraph>

      <Card style={{ marginBottom: 24 }}>
        <Steps current={currentStep} style={{ marginBottom: 32 }}>
          <Step title="选择文件" icon={getStepIcon(0)} />
          <Step title="配置类型" icon={getStepIcon(1)} />
          <Step title="字段映射" icon={getStepIcon(2)} />
          <Step title="导入完成" icon={getStepIcon(3)} />
        </Steps>

        {renderStepContent()}

        <Divider />

        <div style={{ textAlign: 'center' }}>
          <Space>
            {currentStep > 0 && currentStep < 3 && (
              <Button onClick={() => setCurrentStep(currentStep - 1)}>
                上一步
              </Button>
            )}
            
            {currentStep < 2 && (
              <Button
                type="primary"
                loading={loading}
                disabled={!canProceed()}
                onClick={() => {
                  if (currentStep === 0) {
                    setCurrentStep(1);
                  } else if (currentStep === 1) {
                    analyzeFile();
                  }
                }}
              >
                {getNextStepText()}
              </Button>
            )}

            {currentStep === 2 && (
              <Button
                type="primary"
                loading={loading}
                disabled={!canProceed()}
                onClick={executeImport}
              >
                开始导入
              </Button>
            )}
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default DataImport;