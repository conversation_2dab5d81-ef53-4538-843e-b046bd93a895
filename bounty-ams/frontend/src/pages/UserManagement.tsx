import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Avatar,
  Dropdown,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Popconfirm,
  Tabs,
  Badge,
  Typography,
  Row,
  Col,
  Statistic,
  DatePicker,
  Tooltip,
} from 'antd';
import {
  UserOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  KeyOutlined,
  EyeOutlined,
  SettingOutlined,
  TeamOutlined,
  SafetyOutlined,
  HistoryOutlined,
  MoreOutlined,
  LockOutlined,
  UnlockOutlined,
} from '@ant-design/icons';
import { api } from '../services/api';
import { showErrorMessage } from '../utils/errorHandler';
import dayjs from 'dayjs';

const { TabPane } = Tabs;
const { Text, Title } = Typography;
const { Option } = Select;

interface User {
  id: string;
  username: string;
  email: string;
  full_name?: string;
  phone?: string;
  department?: string;
  position?: string;
  avatar_url?: string;
  is_admin: boolean;
  is_active: boolean;
  last_login_at?: string;
  last_login_ip?: string;
  login_count: number;
  password_changed_at?: string;
  must_change_password: boolean;
  account_locked_until?: string;
  failed_login_attempts: number;
  notes?: string;
  created_at: string;
  updated_at?: string;
  created_by_user_id?: string;
  roles: Role[];
}

interface Role {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  permissions: string[];
  is_system: boolean;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

interface UserActivityLog {
  id: string;
  user_id?: string;
  action: string;
  resource_type?: string;
  resource_id?: string;
  details?: any;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
  session_id?: string;
}

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [activityLogs, setActivityLogs] = useState<UserActivityLog[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('users');

  // 模态框状态
  const [userModalVisible, setUserModalVisible] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [roleAssignModalVisible, setRoleAssignModalVisible] = useState(false);
  
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const [userForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [roleAssignForm] = Form.useForm();

  // 统计数据
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    adminUsers: 0,
    lockedUsers: 0,
  });

  useEffect(() => {
    loadUsers();
    loadRoles();
  }, []);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const response = await api.get('/users');
      const usersData = response.data;
      setUsers(usersData);
      
      // 计算统计数据
      setStats({
        totalUsers: usersData.length,
        activeUsers: usersData.filter((u: User) => u.is_active).length,
        adminUsers: usersData.filter((u: User) => u.is_admin).length,
        lockedUsers: usersData.filter((u: User) => u.account_locked_until && new Date(u.account_locked_until) > new Date()).length,
      });
    } catch (error) {
      showErrorMessage(error, '加载用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  const loadRoles = async () => {
    try {
      const response = await api.get('/roles');
      setRoles(response.data);
    } catch (error) {
      showErrorMessage(error, '加载角色列表失败');
    }
  };



  const handleCreateUser = () => {
    setEditingUser(null);
    userForm.resetFields();
    setUserModalVisible(true);
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    userForm.setFieldsValue({
      ...user,
      password: undefined, // 不显示密码
    });
    setUserModalVisible(true);
  };

  const handleDeleteUser = async (userId: string) => {
    try {
      await api.delete(`/users/${userId}`);
      message.success('用户删除成功');
      loadUsers();
    } catch (error) {
      showErrorMessage(error, '删除用户失败');
    }
  };

  const handleUserSubmit = async (values: any) => {
    try {
      if (editingUser) {
        await api.put(`/users/${editingUser.id}`, values);
        message.success('用户更新成功');
      } else {
        await api.post('/users', values);
        message.success('用户创建成功');
      }
      setUserModalVisible(false);
      loadUsers();
    } catch (error) {
      showErrorMessage(error, editingUser ? '更新用户失败' : '创建用户失败');
    }
  };

  const handleResetPassword = async (values: any) => {
    try {
      await api.post(`/users/${selectedUser?.id}/password-reset`, values);
      message.success('密码重置成功');
      setPasswordModalVisible(false);
      loadUsers();
    } catch (error) {
      showErrorMessage(error, '密码重置失败');
    }
  };

  const handleAssignRoles = async (values: any) => {
    try {
      await api.post(`/users/${selectedUser?.id}/roles`, {
        user_id: selectedUser?.id,
        role_ids: values.role_ids,
      });
      message.success('角色分配成功');
      setRoleAssignModalVisible(false);
      loadUsers();
    } catch (error) {
      showErrorMessage(error, '角色分配失败');
    }
  };



  const userColumns = [
    {
      title: '用户',
      key: 'user',
      width: 200,
      render: (record: User) => (
        <Space>
          <Avatar 
            src={record.avatar_url} 
            icon={<UserOutlined />}
            style={{ backgroundColor: record.is_admin ? '#52c41a' : '#1890ff' }}
          />
          <div>
            <div>
              <Text strong>{record.full_name || record.username}</Text>
              {record.is_admin && <Tag color="red" size="small" style={{ marginLeft: 4 }}>管理员</Tag>}
            </div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.username} • {record.email}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: '部门/职位',
      key: 'department',
      width: 150,
      render: (record: User) => (
        <div>
          <div>{record.department || '-'}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.position || '-'}
          </Text>
        </div>
      ),
    },
    {
      title: '角色',
      dataIndex: 'roles',
      key: 'roles',
      width: 200,
      render: (roles: Role[]) => (
        <Space wrap>
          {roles.map(role => (
            <Tag key={role.id} color={role.is_system ? 'blue' : 'green'}>
              {role.display_name}
            </Tag>
          ))}
        </Space>
      ),
    },
    {
      title: '状态',
      key: 'status',
      width: 120,
      render: (record: User) => {
        const isLocked = record.account_locked_until && new Date(record.account_locked_until) > new Date();
        return (
          <Space direction="vertical" size="small">
            <Badge 
              status={record.is_active ? 'success' : 'error'} 
              text={record.is_active ? '活跃' : '禁用'}
            />
            {isLocked && <Badge status="warning" text="锁定" />}
            {record.must_change_password && <Badge status="processing" text="需改密" />}
          </Space>
        );
      },
    },
    {
      title: '最后登录',
      key: 'last_login',
      width: 150,
      render: (record: User) => (
        <div>
          <div>{record.last_login_at ? dayjs(record.last_login_at).format('MM-DD HH:mm') : '从未登录'}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            登录 {record.login_count} 次
          </Text>
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (record: User) => (
        <Dropdown
          menu={{
            items: [
              {
                key: 'edit',
                icon: <EditOutlined />,
                label: '编辑',
                onClick: () => handleEditUser(record),
              },
              {
                key: 'roles',
                icon: <TeamOutlined />,
                label: '分配角色',
                onClick: () => {
                  setSelectedUser(record);
                  roleAssignForm.setFieldsValue({
                    role_ids: record.roles.map(r => r.id),
                  });
                  setRoleAssignModalVisible(true);
                },
              },
              {
                key: 'password',
                icon: <KeyOutlined />,
                label: '重置密码',
                onClick: () => {
                  setSelectedUser(record);
                  passwordForm.resetFields();
                  setPasswordModalVisible(true);
                },
              },

              {
                type: 'divider',
              },
              {
                key: 'delete',
                icon: <DeleteOutlined />,
                label: '删除',
                danger: true,
                onClick: () => {
                  Modal.confirm({
                    title: '确认删除',
                    content: `确定要删除用户 "${record.username}" 吗？`,
                    onOk: () => handleDeleteUser(record.id),
                  });
                },
              },
            ],
          }}
          trigger={['click']}
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Title level={3}>用户管理</Title>
        
        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总用户数"
                value={stats.totalUsers}
                prefix={<UserOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="活跃用户"
                value={stats.activeUsers}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="管理员"
                value={stats.adminUsers}
                prefix={<SafetyOutlined />}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="锁定用户"
                value={stats.lockedUsers}
                prefix={<LockOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
        </Row>
      </div>

      <Card>
        <div>
          <div style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateUser}
            >
              创建用户
            </Button>
          </div>

          <Table
            columns={userColumns}
            dataSource={users}
            rowKey="id"
            loading={loading}
            size="small"
            scroll={{ x: 1200 }}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 个用户`,
            }}
          />
        </div>
      </Card>

      {/* 用户创建/编辑模态框 */}
      <Modal
        title={editingUser ? '编辑用户' : '创建用户'}
        open={userModalVisible}
        onCancel={() => setUserModalVisible(false)}
        onOk={() => userForm.submit()}
        width={600}
      >
        <Form
          form={userForm}
          layout="vertical"
          onFinish={handleUserSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="username"
                label="用户名"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, message: '用户名至少3个字符' },
                ]}
              >
                <Input placeholder="用户名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' },
                ]}
              >
                <Input placeholder="邮箱地址" />
              </Form.Item>
            </Col>
          </Row>

          {!editingUser && (
            <Form.Item
              name="password"
              label="密码"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6个字符' },
              ]}
            >
              <Input.Password placeholder="密码" />
            </Form.Item>
          )}

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="full_name" label="姓名">
                <Input placeholder="真实姓名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="phone" label="手机号">
                <Input placeholder="手机号码" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="department" label="部门">
                <Input placeholder="所属部门" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="position" label="职位">
                <Input placeholder="职位" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="avatar_url" label="头像URL">
            <Input placeholder="头像图片URL" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="is_admin" label="管理员权限" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="is_active" label="账户状态" valuePropName="checked" initialValue={true}>
                <Switch checkedChildren="启用" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="notes" label="备注">
            <Input.TextArea rows={3} placeholder="用户备注信息" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 密码重置模态框 */}
      <Modal
        title="重置密码"
        open={passwordModalVisible}
        onCancel={() => setPasswordModalVisible(false)}
        onOk={() => passwordForm.submit()}
      >
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={handleResetPassword}
        >
          <Form.Item
            name="new_password"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码至少6个字符' },
            ]}
          >
            <Input.Password placeholder="新密码" />
          </Form.Item>

          <Form.Item
            name="must_change_password"
            label="强制用户下次登录时修改密码"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>
        </Form>
      </Modal>

      {/* 角色分配模态框 */}
      <Modal
        title="分配角色"
        open={roleAssignModalVisible}
        onCancel={() => setRoleAssignModalVisible(false)}
        onOk={() => roleAssignForm.submit()}
      >
        <Form
          form={roleAssignForm}
          layout="vertical"
          onFinish={handleAssignRoles}
        >
          <Form.Item
            name="role_ids"
            label="选择角色"
            rules={[{ required: true, message: '请选择至少一个角色' }]}
          >
            <Select
              mode="multiple"
              placeholder="选择角色"
              style={{ width: '100%' }}
            >
              {roles.filter(role => role.is_active).map(role => (
                <Option key={role.id} value={role.id}>
                  {role.display_name}
                  {role.is_system && <Tag color="blue" style={{ marginLeft: 8 }}>系统</Tag>}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>


    </div>
  );
};

export default UserManagement;
