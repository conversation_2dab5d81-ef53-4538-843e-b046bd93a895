import React, { useState } from 'react';
import { Card, Button, Space, Typography, Alert, Divider } from 'antd';
import { taskService, workflowService } from '../services/api';

const { Title, Text, Paragraph } = Typography;

const DebugAPI: React.FC = () => {
  const [tasksResult, setTasksResult] = useState<any>(null);
  const [workflowsResult, setWorkflowsResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testTasksAPI = async () => {
    setLoading(true);
    try {
      console.log('Testing tasks API...');
      const response = await taskService.getTasks({ limit: 10 });
      console.log('Tasks API response:', response);
      setTasksResult({
        success: true,
        data: response.data,
        status: response.status
      });
    } catch (error: any) {
      console.error('Tasks API error:', error);
      setTasksResult({
        success: false,
        error: error.message,
        response: error.response?.data,
        status: error.response?.status
      });
    } finally {
      setLoading(false);
    }
  };

  const testWorkflowsAPI = async () => {
    setLoading(true);
    try {
      console.log('Testing workflows API...');
      const response = await workflowService.getWorkflows();
      console.log('Workflows API response:', response);
      setWorkflowsResult({
        success: true,
        data: response.data,
        status: response.status
      });
    } catch (error: any) {
      console.error('Workflows API error:', error);
      setWorkflowsResult({
        success: false,
        error: error.message,
        response: error.response?.data,
        status: error.response?.status
      });
    } finally {
      setLoading(false);
    }
  };

  const checkAuth = () => {
    const token = localStorage.getItem('token');
    console.log('Current token:', token);
    return token;
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>API调试页面</Title>
      
      <Card title="认证状态" style={{ marginBottom: 16 }}>
        <Paragraph>
          当前Token: {checkAuth() ? '✅ 已设置' : '❌ 未设置'}
        </Paragraph>
        <Text code>{checkAuth()}</Text>
      </Card>

      <Card title="API测试" style={{ marginBottom: 16 }}>
        <Space>
          <Button 
            type="primary" 
            onClick={testTasksAPI}
            loading={loading}
          >
            测试 Tasks API
          </Button>
          <Button 
            type="primary" 
            onClick={testWorkflowsAPI}
            loading={loading}
          >
            测试 Workflows API
          </Button>
        </Space>
      </Card>

      {tasksResult && (
        <Card title="Tasks API 结果" style={{ marginBottom: 16 }}>
          {tasksResult.success ? (
            <Alert
              message="API调用成功"
              description={
                <div>
                  <p>状态码: {tasksResult.status}</p>
                  <p>任务数量: {tasksResult.data?.tasks?.length || 0}</p>
                  <details>
                    <summary>完整响应数据</summary>
                    <pre style={{ background: '#f5f5f5', padding: '8px', marginTop: '8px' }}>
                      {JSON.stringify(tasksResult.data, null, 2)}
                    </pre>
                  </details>
                </div>
              }
              type="success"
            />
          ) : (
            <Alert
              message="API调用失败"
              description={
                <div>
                  <p>错误: {tasksResult.error}</p>
                  <p>状态码: {tasksResult.status}</p>
                  <details>
                    <summary>错误详情</summary>
                    <pre style={{ background: '#f5f5f5', padding: '8px', marginTop: '8px' }}>
                      {JSON.stringify(tasksResult.response, null, 2)}
                    </pre>
                  </details>
                </div>
              }
              type="error"
            />
          )}
        </Card>
      )}

      {workflowsResult && (
        <Card title="Workflows API 结果" style={{ marginBottom: 16 }}>
          {workflowsResult.success ? (
            <Alert
              message="API调用成功"
              description={
                <div>
                  <p>状态码: {workflowsResult.status}</p>
                  <p>工作流数量: {workflowsResult.data?.workflows?.length || 0}</p>
                  <details>
                    <summary>完整响应数据</summary>
                    <pre style={{ background: '#f5f5f5', padding: '8px', marginTop: '8px' }}>
                      {JSON.stringify(workflowsResult.data, null, 2)}
                    </pre>
                  </details>
                </div>
              }
              type="success"
            />
          ) : (
            <Alert
              message="API调用失败"
              description={
                <div>
                  <p>错误: {workflowsResult.error}</p>
                  <p>状态码: {workflowsResult.status}</p>
                  <details>
                    <summary>错误详情</summary>
                    <pre style={{ background: '#f5f5f5', padding: '8px', marginTop: '8px' }}>
                      {JSON.stringify(workflowsResult.response, null, 2)}
                    </pre>
                  </details>
                </div>
              }
              type="error"
            />
          )}
        </Card>
      )}
    </div>
  );
};

export default DebugAPI;
