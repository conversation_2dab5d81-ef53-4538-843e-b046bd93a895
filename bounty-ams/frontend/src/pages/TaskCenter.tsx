import React, { useState } from 'react';
import { Tabs, Card, Space, Typography } from 'antd';
import {
  ThunderboltOutlined,
  MonitorOutlined,
  BranchesOutlined,
  RobotOutlined,
  DashboardOutlined
} from '@ant-design/icons';

// 导入各个功能组件
import TaskTemplateManager from '../components/TaskTemplateManager';
import TaskExecutionMonitor from '../components/TaskExecutionMonitor';
import WorkflowVisualEditor from '../components/WorkflowVisualEditor';
import TaskAgentManager from '../components/TaskAgentManager';
import TaskSystemMonitor from '../components/TaskSystemMonitor';

const { Title } = Typography;





const TaskCenter: React.FC = () => {
  const [activeTab, setActiveTab] = useState('templates');

  const tabItems = [
    {
      key: 'templates',
      label: (
        <Space>
          <ThunderboltOutlined />
          任务模板
        </Space>
      ),
      children: <TaskTemplateManager />
    },
    {
      key: 'execution',
      label: (
        <Space>
          <MonitorOutlined />
          任务执行
        </Space>
      ),
      children: <TaskExecutionMonitor />
    },
    {
      key: 'workflows',
      label: (
        <Space>
          <BranchesOutlined />
          工作流管理
        </Space>
      ),
      children: <WorkflowVisualEditor />
    },
    {
      key: 'agents',
      label: (
        <Space>
          <RobotOutlined />
          Agent管理
        </Space>
      ),
      children: <TaskAgentManager />
    },
    {
      key: 'monitor',
      label: (
        <Space>
          <DashboardOutlined />
          系统监控
        </Space>
      ),
      children: <TaskSystemMonitor />
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          <Space>
            <ThunderboltOutlined />
            任务管理中心
          </Space>
        </Title>
        <p style={{ color: '#666', marginTop: 8 }}>
          统一管理任务模板、执行监控、工作流编排和Agent资源
        </p>
      </div>

      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
          tabBarStyle={{ marginBottom: 24 }}
        />
      </Card>
    </div>
  );
};

export default TaskCenter;
