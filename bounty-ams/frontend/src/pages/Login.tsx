import React, { useState, useRef } from 'react';
import { Navigate, useNavigate } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  Button,
  Typography,
  Row,
  Col,
  Space,
  Divider,
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  SecurityScanOutlined,
} from '@ant-design/icons';
import { useAuth } from '../context/AuthContext';

const { Title, Text } = Typography;

const Login: React.FC = () => {
  const { login, user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const loginInProgress = useRef(false);

  if (user) {
    return <Navigate to="/" replace />;
  }

  const onFinish = async (values: { username: string; password: string }) => {
    if (loading || loginInProgress.current) {
      console.log('登录请求正在进行中，忽略重复提交');
      return;
    }

    console.log('开始登录流程...', { username: values.username });
    setLoading(true);
    loginInProgress.current = true;
    
    try {
      const success = await login(values.username, values.password);
      console.log('登录结果:', success);
      
      if (success) {
        // 登录成功，使用 React Router 导航
        console.log('登录成功，准备跳转到首页');
        navigate('/', { replace: true });
      } else {
        console.log('登录失败，重置loading状态');
        setLoading(false);
        loginInProgress.current = false;
      }
    } catch (error) {
      console.error('登录过程中发生错误:', error);
      setLoading(false);
      loginInProgress.current = false;
    }
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('表单验证失败:', errorInfo);
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <Card
        style={{
          width: '100%',
          maxWidth: 400,
          boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
          borderRadius: '12px'
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <Space direction="vertical" size="middle">
            <div style={{
              width: 64,
              height: 64,
              borderRadius: '50%',
              background: 'linear-gradient(135deg, #1890ff, #36cfc9)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto',
            }}>
              <SecurityScanOutlined style={{ fontSize: '32px', color: 'white' }} />
            </div>
            <Title level={2} style={{ margin: 0 }}>
              Bounty AMS
            </Title>
            <Text type="secondary">
              漏洞赏金资产管理系统
            </Text>
          </Space>
        </div>

        <Form
          name="login"
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          layout="vertical"
          size="large"
          form={form}
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' }
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="用户名"
              autoComplete="username"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少6个字符' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
              autoComplete="current-password"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              style={{
                width: '100%',
                height: '44px',
                borderRadius: '8px',
                fontSize: '16px',
                fontWeight: 500,
              }}
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        <Divider style={{ margin: '24px 0' }} />
        
        <div style={{ textAlign: 'center' }}>
          <Text type="secondary" style={{ fontSize: '13px' }}>
            默认账户：admin / password
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default Login;