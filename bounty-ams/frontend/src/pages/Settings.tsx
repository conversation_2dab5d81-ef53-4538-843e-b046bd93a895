import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Switch,
  Select,
  Button,
  message,
  Divider,
  Typography,
  Space,
  Row,
  Col,
  Alert,
} from 'antd';
import {
  SettingOutlined,
  SaveOutlined,
  BellOutlined,
  EyeOutlined,
  GlobalOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;

interface SettingsData {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  notifications: {
    email: boolean;
    browser: boolean;
    taskUpdates: boolean;
    systemAlerts: boolean;
  };
  display: {
    pageSize: number;
    showAdvancedFeatures: boolean;
    compactMode: boolean;
  };
  privacy: {
    showOnlineStatus: boolean;
    allowDataCollection: boolean;
  };
}

const Settings: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState<SettingsData>({
    theme: 'light',
    language: 'zh-CN',
    notifications: {
      email: true,
      browser: true,
      taskUpdates: true,
      systemAlerts: true,
    },
    display: {
      pageSize: 20,
      showAdvancedFeatures: false,
      compactMode: false,
    },
    privacy: {
      showOnlineStatus: true,
      allowDataCollection: false,
    },
  });

  const [form] = Form.useForm();

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      // 从localStorage加载设置
      const savedSettings = localStorage.getItem('userSettings');
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings);
        setSettings(parsedSettings);
        form.setFieldsValue(parsedSettings);
      }
    } catch (error) {
      console.error('加载设置失败:', error);
    }
  };

  const handleSaveSettings = async (values: any) => {
    try {
      setLoading(true);
      
      // 保存到localStorage
      localStorage.setItem('userSettings', JSON.stringify(values));
      setSettings(values);
      
      // 应用主题设置
      if (values.theme === 'dark') {
        document.body.classList.add('dark-theme');
      } else {
        document.body.classList.remove('dark-theme');
      }
      
      message.success('设置保存成功');
    } catch (error) {
      message.error('保存设置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleResetSettings = () => {
    const defaultSettings: SettingsData = {
      theme: 'light',
      language: 'zh-CN',
      notifications: {
        email: true,
        browser: true,
        taskUpdates: true,
        systemAlerts: true,
      },
      display: {
        pageSize: 20,
        showAdvancedFeatures: false,
        compactMode: false,
      },
      privacy: {
        showOnlineStatus: true,
        allowDataCollection: false,
      },
    };
    
    form.setFieldsValue(defaultSettings);
    setSettings(defaultSettings);
    message.info('设置已重置为默认值');
  };

  return (
    <div style={{ maxWidth: 800, margin: '0 auto', padding: '24px' }}>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          <SettingOutlined /> 系统设置
        </Title>
        <Text type="secondary">
          个性化您的使用体验
        </Text>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSaveSettings}
        initialValues={settings}
      >
        {/* 外观设置 */}
        <Card title="外观设置" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="theme"
                label="主题模式"
              >
                <Select>
                  <Option value="light">浅色模式</Option>
                  <Option value="dark">深色模式</Option>
                  <Option value="auto">跟随系统</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="language"
                label="语言"
              >
                <Select>
                  <Option value="zh-CN">简体中文</Option>
                  <Option value="en-US">English</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 通知设置 */}
        <Card title={<><BellOutlined /> 通知设置</>} style={{ marginBottom: 16 }}>
          <Form.Item
            name={['notifications', 'email']}
            label="邮件通知"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          
          <Form.Item
            name={['notifications', 'browser']}
            label="浏览器通知"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          
          <Form.Item
            name={['notifications', 'taskUpdates']}
            label="任务更新通知"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          
          <Form.Item
            name={['notifications', 'systemAlerts']}
            label="系统警报"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Card>

        {/* 显示设置 */}
        <Card title={<><EyeOutlined /> 显示设置</>} style={{ marginBottom: 16 }}>
          <Form.Item
            name={['display', 'pageSize']}
            label="每页显示条数"
          >
            <Select>
              <Option value={10}>10条</Option>
              <Option value={20}>20条</Option>
              <Option value={50}>50条</Option>
              <Option value={100}>100条</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name={['display', 'showAdvancedFeatures']}
            label="显示高级功能"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          
          <Form.Item
            name={['display', 'compactMode']}
            label="紧凑模式"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Card>

        {/* 隐私设置 */}
        <Card title={<><GlobalOutlined /> 隐私设置</>} style={{ marginBottom: 16 }}>
          <Form.Item
            name={['privacy', 'showOnlineStatus']}
            label="显示在线状态"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          
          <Form.Item
            name={['privacy', 'allowDataCollection']}
            label="允许数据收集用于改进服务"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          
          <Alert
            message="隐私说明"
            description="我们重视您的隐私，所有数据收集都是匿名的，仅用于改进产品体验。"
            type="info"
            showIcon
            style={{ marginTop: 16 }}
          />
        </Card>

        {/* 操作按钮 */}
        <Card>
          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={handleResetSettings}>
                重置为默认
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                icon={<SaveOutlined />}
              >
                保存设置
              </Button>
            </Space>
          </div>
        </Card>
      </Form>
    </div>
  );
};

export default Settings;
