import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Space, message, Typography, Tag, Input, Row, Col, Select, Checkbox, Popover, Tooltip } from 'antd';
import { ReloadOutlined, EyeOutlined, SearchOutlined, SettingOutlined, GlobalOutlined, ApiOutlined } from '@ant-design/icons';
import { assetService } from '../services/api';

const { Title, Text } = Typography;
const { Option } = Select;

const BasicAssets: React.FC = () => {
  const [assets, setAssets] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [searchKeyword, setSearchKeyword] = useState('');
  
  // 筛选器状态
  const [filters, setFilters] = useState({
    asset_type: '',
    status: '',
    confidence: '',
    platform_id: '',
    project_id: '',
  });
  
  // 筛选选项数据
  const [assetTypes, setAssetTypes] = useState<any[]>([]);
  const [platforms, setPlatforms] = useState<any[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [platformsLoaded, setPlatformsLoaded] = useState(false);
  const [projectsLoaded, setProjectsLoaded] = useState(false);
  
  // 模型类型ID
  const [platformModelId, setPlatformModelId] = useState<string>('');
  const [projectModelId, setProjectModelId] = useState<string>('');
  
  // 动态字段管理
  const [availableFields, setAvailableFields] = useState<any[]>([]);
  const [visibleColumns, setVisibleColumns] = useState<string[]>([
    'platform_project', 'asset_type', 'asset_value', 'status', 'confidence', 'discovered_at', 'action'
  ]);

  useEffect(() => {
    // 只有当平台和项目数据都加载完成后才加载资产
    if (platformsLoaded && projectsLoaded) {
      loadAssets();
      loadAssetTypes();
    }
    loadAvailableFields();
    loadModelTypes();
  }, [currentPage, pageSize, searchKeyword, filters, platformsLoaded, projectsLoaded]);

  useEffect(() => {
    if (platformModelId) {
      loadPlatforms();
    }
    if (projectModelId) {
      loadProjects();
    }
  }, [platformModelId, projectModelId]);

  const loadAssets = async () => {
    try {
      setLoading(true);
      
      const filterParams: any = {
        skip: (currentPage - 1) * pageSize,
        limit: pageSize,
      };
      
      // 添加搜索关键词
      if (searchKeyword) {
        filterParams.q = searchKeyword;
      }
      
      // 添加筛选条件
      if (filters.asset_type) {
        filterParams.asset_type = filters.asset_type;
      }
      if (filters.status) {
        filterParams.status = filters.status;
      }
      if (filters.confidence) {
        filterParams.confidence = filters.confidence;
      }
      if (filters.platform_id) {
        filterParams.platform_id = filters.platform_id;
      }
      if (filters.project_id) {
        filterParams.project_id = filters.project_id;
      }
      
      const response = await assetService.searchAssets(filterParams);
      const assets = response.data.assets || [];
      const total = response.data.total || 0;
      
      const transformedAssets = assets.map((asset: any) => ({
        _id: asset.asset_id,
        ...asset,
        _created_at: asset.created_at,
        _updated_at: asset.updated_at,
      }));
      
      setAssets(transformedAssets);
      setTotal(total);
      
    } catch (error) {
      console.error('加载资产失败:', error);
      message.error('加载资产失败');
    } finally {
      setLoading(false);
    }
  };

  const loadAssetTypes = async () => {
    try {
      const response = await assetService.getAssetTypes();
      setAssetTypes(response.data.types || []);
    } catch (error) {
      console.error('加载资产类型失败:', error);
    }
  };

  const loadAvailableFields = async () => {
    try {
      const response = await assetService.getAvailableFields();
      setAvailableFields(response.data.fields || []);
    } catch (error) {
      console.error('加载字段失败:', error);
    }
  };

  const loadModelTypes = async () => {
    try {
      const response = await assetService.getDynamicModelTypes();
      const types = response.data;
      
      const platformType = types.find((t: any) => t.name === 'platform');
      const projectType = types.find((t: any) => t.name === 'project');
      
      if (platformType) setPlatformModelId(platformType.id);
      if (projectType) setProjectModelId(projectType.id);
    } catch (error) {
      console.error('加载模型类型失败:', error);
    }
  };

  const loadPlatforms = async () => {
    try {
      const response = await assetService.getDynamicEntities(platformModelId);
      setPlatforms(response.data || []);
      setPlatformsLoaded(true);
    } catch (error) {
      console.error('加载平台列表失败:', error);
      setPlatformsLoaded(true); // 即使失败也标记为已加载
    }
  };

  const loadProjects = async () => {
    try {
      const response = await assetService.getDynamicEntities(projectModelId);
      setProjects(response.data || []);
      setProjectsLoaded(true);
    } catch (error) {
      console.error('加载项目列表失败:', error);
      setProjectsLoaded(true); // 即使失败也标记为已加载
    }
  };

  const loadProjectsByPlatform = async (platformId: string) => {
    try {
      const response = await assetService.getDynamicEntities(projectModelId, {
        platform_id: platformId
      });
      setProjects(response.data || []);
    } catch (error) {
      console.error('加载项目列表失败:', error);
    }
  };

  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    setCurrentPage(1); // 重置到第一页
  };

  const handleReset = () => {
    setSearchKeyword('');
    setFilters({
      asset_type: '',
      status: '',
      confidence: '',
      platform_id: '',
      project_id: '',
    });
    setCurrentPage(1);
    loadProjects(); // 重置项目列表
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
    setCurrentPage(1);
  };

  const handlePlatformChange = (platformId: string) => {
    setFilters(prev => ({ ...prev, platform_id: platformId, project_id: '' }));
    setCurrentPage(1);
    if (platformId) {
      loadProjectsByPlatform(platformId);
    } else {
      loadProjects();
    }
  };

  const handleViewDetails = (asset: any) => {
    message.info(`查看资产: ${asset.asset_value}`);
  };

  // 获取字段渲染函数
  const getFieldRender = (fieldName: string) => {
    switch (fieldName) {
      case 'platform_project':
        return (record: any) => {
          // 从已加载的平台和项目数组中查找名称
          const platform = platforms.find(p => p.id === record.platform_id);
          const project = projects.find(p => p.id === record.project_id);
          
          return (
            <div>
              {platform && (
                <Tag color="blue" icon={<GlobalOutlined />} style={{ marginBottom: 2, fontSize: '11px' }}>
                  {platform.entity_data?.display_name || platform.entity_data?.name || '未知平台'}
                </Tag>
              )}
              {!platform && record.platform_id && (
                <Tag color="blue" icon={<GlobalOutlined />} style={{ marginBottom: 2, fontSize: '11px' }}>
                  {record.platform_id}
                </Tag>
              )}
              {project && (
                <Tag color="green" icon={<ApiOutlined />} style={{ fontSize: '11px' }}>
                  {project.entity_data?.name || '未知项目'}
                </Tag>
              )}
              {!project && record.project_id && (
                <Tag color="green" icon={<ApiOutlined />} style={{ fontSize: '11px' }}>
                  {record.project_id}
                </Tag>
              )}
            </div>
          );
        };
      case 'asset_type':
        return (type: string) => type ? <Tag color="blue">{type}</Tag> : '-';
      case 'asset_value':
        return (value: string) => value ? <Text code>{value}</Text> : '-';
      case 'status':
        return (status: string) => status ? <Tag color="green">{status}</Tag> : '-';
      case 'confidence':
        return (confidence: string) => confidence ? <Tag color="orange">{confidence}</Tag> : '-';
      case 'discovered_at':
        return (date: string) => date ? (
          <Text type="secondary">{new Date(date).toLocaleString()}</Text>
        ) : '-';
      case 'ip_address':
        return (ip: string) => ip ? <Text code>{ip}</Text> : '-';
      case 'asset_host':
        return (host: string) => host || '-';
      case 'asset_port':
        return (port: number) => port ? <Tag color="orange">{port}</Tag> : '-';
      case 'transport_protocol':
        return (protocol: string) => protocol ? <Tag color="purple">{protocol}</Tag> : '-';
      case 'webpage_title':
        return (title: string) => title ? (
          <Text style={{ fontSize: '12px' }} title={title}>
            {title.length > 30 ? `${title.substring(0, 30)}...` : title}
          </Text>
        ) : '-';
      case 'service_name':
        return (name: string) => name || '-';
      case 'country_cn':
        return (country: string) => country || '-';
      case 'province_cn':
        return (province: string) => province || '-';
      case 'city_cn':
        return (city: string) => city || '-';
      case 'isp':
        return (isp: string) => isp ? (
          <Text style={{ fontSize: '12px' }} title={isp}>
            {isp.length > 15 ? `${isp.substring(0, 15)}...` : isp}
          </Text>
        ) : '-';
      case 'action':
        return (record: any) => (
          <Button
            type="text"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetails(record)}
          />
        );
      default:
        return (value: any) => {
          if (value === null || value === undefined) return '-';
          if (typeof value === 'object') return JSON.stringify(value);
          return String(value);
        };
    }
  };

  // 动态生成列配置
  const columns = visibleColumns.map(colKey => {
    const field = availableFields.find(f => f.name === colKey);
    
    if (colKey === 'platform_project') {
      return {
        title: '平台/项目',
        key: 'platform_project',
        width: 180,
        fixed: 'left' as const,
        render: getFieldRender('platform_project'),
      };
    }
    
    if (colKey === 'action') {
      return {
        title: '操作',
        key: 'action',
        width: 80,
        fixed: 'right' as const,
        render: getFieldRender('action'),
      };
    }
    
    return {
      title: field?.label || colKey,
      dataIndex: colKey,
      key: colKey,
      width: colKey === 'asset_value' ? 200 : 120,
      ellipsis: true,
      render: getFieldRender(colKey),
    };
  }).filter(Boolean);

  // 可选择的列配置
  const allColumnOptions = [
    { key: 'platform_project', title: '平台/项目', essential: true },
    { key: 'asset_type', title: '资产类型', essential: true },
    { key: 'asset_value', title: '资产值', essential: true },
    { key: 'action', title: '操作', essential: true },
    { key: 'status', title: '状态', essential: false },
    { key: 'confidence', title: '置信度', essential: false },
    { key: 'discovered_at', title: '发现时间', essential: false },
    { key: 'ip_address', title: 'IP地址', essential: false },
    { key: 'asset_host', title: '主机', essential: false },
    { key: 'asset_port', title: '端口', essential: false },
    { key: 'transport_protocol', title: '传输协议', essential: false },
    { key: 'webpage_title', title: '网页标题', essential: false },
    { key: 'service_name', title: '服务名称', essential: false },
    { key: 'country_cn', title: '国家', essential: false },
    { key: 'province_cn', title: '省份', essential: false },
    { key: 'city_cn', title: '城市', essential: false },
    { key: 'isp', title: '运营商', essential: false },
    ...availableFields
      .filter(field => !['platform_project', 'asset_type', 'asset_value', 'status', 'confidence', 'discovered_at', 'ip_address', 'asset_host', 'asset_port', 'transport_protocol', 'webpage_title', 'service_name', 'country_cn', 'province_cn', 'city_cn', 'isp'].includes(field.name))
      .map(field => ({
        key: field.name,
        title: field.label,
        essential: false,
      }))
  ];

  return (
    <div style={{ padding: '20px' }}>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Space align="center" style={{ marginBottom: 16 }}>
            <Title level={3} style={{ margin: 0 }}>
              资产管理
            </Title>
            <Text type="secondary">
              共 {total} 项资产
            </Text>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadAssets}
              loading={loading}
            >
              刷新
            </Button>
            <Popover
              content={
                <div style={{ width: 300, maxHeight: 400, overflowY: 'auto' }}>
                  <div style={{ marginBottom: 8, fontWeight: 'bold' }}>选择显示列</div>
                  
                  {/* 基础必选列 */}
                  <div style={{ marginBottom: 12 }}>
                    <div style={{ fontSize: '12px', fontWeight: 'bold', color: '#666', marginBottom: 4 }}>
                      基础列 (必选)
                    </div>
                    {allColumnOptions.filter(option => option.essential).map(option => (
                      <div key={option.key} style={{ marginBottom: 4 }}>
                        <Checkbox
                          checked={visibleColumns.includes(option.key)}
                          disabled={option.essential}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setVisibleColumns([...visibleColumns, option.key]);
                            } else {
                              setVisibleColumns(visibleColumns.filter(col => col !== option.key));
                            }
                          }}
                        >
                          <span style={{ fontSize: '12px' }}>
                            {option.title}
                          </span>
                        </Checkbox>
                      </div>
                    ))}
                  </div>
                  
                  {/* 可选列 */}
                  <div style={{ marginBottom: 12 }}>
                    <div style={{ fontSize: '12px', fontWeight: 'bold', color: '#666', marginBottom: 4 }}>
                      可选列
                    </div>
                    {allColumnOptions.filter(option => !option.essential).map(option => (
                      <div key={option.key} style={{ marginBottom: 4 }}>
                        <Checkbox
                          checked={visibleColumns.includes(option.key)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setVisibleColumns([...visibleColumns, option.key]);
                            } else {
                              setVisibleColumns(visibleColumns.filter(col => col !== option.key));
                            }
                          }}
                        >
                          <span style={{ fontSize: '12px' }}>
                            {option.title}
                          </span>
                        </Checkbox>
                      </div>
                    ))}
                  </div>
                  
                  <div style={{ marginTop: 8, paddingTop: 8, borderTop: '1px solid #f0f0f0' }}>
                    <Button 
                      size="small" 
                      onClick={() => setVisibleColumns(['platform_project', 'asset_type', 'asset_value', 'action'])}
                    >
                      最小化
                    </Button>
                    <Button 
                      size="small" 
                      style={{ marginLeft: 8 }}
                      onClick={() => setVisibleColumns(allColumnOptions.map(col => col.key))}
                    >
                      全部显示
                    </Button>
                  </div>
                </div>
              }
              trigger="click"
              placement="bottomRight"
            >
              <Tooltip title="列设置">
                <Button icon={<SettingOutlined />} size="small" />
              </Tooltip>
            </Popover>
          </Space>
        </Col>
      </Row>

      {/* 搜索和筛选栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 8]}>
          <Col xs={24} sm={12} md={6}>
            <Input.Search
              placeholder="搜索资产值、主机或目标..."
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              onSearch={handleSearch}
              prefix={<SearchOutlined />}
              allowClear
            />
          </Col>
          <Col xs={24} sm={12} md={4}>
            <Select
              placeholder="平台"
              value={filters.platform_id}
              onChange={handlePlatformChange}
              allowClear
              style={{ width: '100%' }}
            >
              {platforms.map(platform => (
                <Option key={platform.id} value={platform.id}>
                  <Space>
                    <GlobalOutlined />
                    {platform.entity_data.display_name || platform.entity_data.name}
                  </Space>
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={12} md={4}>
            <Select
              placeholder="项目"
              value={filters.project_id}
              onChange={(value) => handleFilterChange('project_id', value)}
              allowClear
              disabled={!filters.platform_id}
              style={{ width: '100%' }}
            >
              {projects.map(project => (
                <Option key={project.id} value={project.id}>
                  <Space>
                    <ApiOutlined />
                    {project.entity_data.display_name || project.entity_data.name}
                  </Space>
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={12} md={3}>
            <Select
              placeholder="资产类型"
              value={filters.asset_type}
              onChange={(value) => handleFilterChange('asset_type', value)}
              allowClear
              style={{ width: '100%' }}
            >
              {assetTypes.map(type => (
                <Option key={type.type} value={type.type}>
                  {type.type} ({type.count})
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={12} md={3}>
            <Select
              placeholder="状态"
              value={filters.status}
              onChange={(value) => handleFilterChange('status', value)}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="active">活跃</Option>
              <Option value="inactive">非活跃</Option>
              <Option value="pending">待验证</Option>
              <Option value="verified">已验证</Option>
              <Option value="false_positive">误报</Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={3}>
            <Select
              placeholder="置信度"
              value={filters.confidence}
              onChange={(value) => handleFilterChange('confidence', value)}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="high">高</Option>
              <Option value="medium">中</Option>
              <Option value="low">低</Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={1}>
            <Button 
              onClick={handleReset} 
              disabled={!searchKeyword && !filters.asset_type && !filters.status && !filters.confidence && !filters.platform_id && !filters.project_id}
            >
              重置
            </Button>
          </Col>
        </Row>
      </Card>

      <Card>
        <Table
          columns={columns}
          dataSource={assets}
          loading={loading}
          rowKey="_id"
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size!);
            },
          }}
          scroll={{ 
            x: Math.max(800, visibleColumns.length * 120),
            y: 600
          }}
        />
      </Card>
    </div>
  );
};

export default BasicAssets;