import React, { memo, useMemo } from 'react';
import { Table, TableProps } from 'antd';
import { FixedSizeList as List } from 'react-window';

interface VirtualTableProps extends TableProps<any> {
  height?: number;
  itemHeight?: number;
}

// 虚拟化表格行组件
const VirtualTableRow = memo(({ index, style, data }: any) => {
  const { columns, dataSource, rowKey } = data;
  const record = dataSource[index];
  
  return (
    <div style={style}>
      <div style={{ display: 'flex', alignItems: 'center', padding: '8px 16px', borderBottom: '1px solid #f0f0f0' }}>
        {columns.map((column: any, colIndex: number) => (
          <div
            key={colIndex}
            style={{
              flex: column.width ? `0 0 ${column.width}px` : 1,
              padding: '0 8px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}
          >
            {column.render ? column.render(record[column.dataIndex], record, index) : record[column.dataIndex]}
          </div>
        ))}
      </div>
    </div>
  );
});

VirtualTableRow.displayName = 'VirtualTableRow';

// 虚拟化表格组件
const VirtualTable: React.FC<VirtualTableProps> = memo(({
  dataSource = [],
  columns = [],
  height = 400,
  itemHeight = 54,
  ...props
}) => {
  const memoizedData = useMemo(() => ({
    columns,
    dataSource,
    rowKey: props.rowKey
  }), [columns, dataSource, props.rowKey]);

  // 如果数据量小于100条，使用普通表格
  if (dataSource.length < 100) {
    return <Table {...props} dataSource={dataSource} columns={columns} />;
  }

  return (
    <div>
      {/* 表头 */}
      <div style={{ display: 'flex', background: '#fafafa', padding: '8px 16px', borderBottom: '2px solid #f0f0f0' }}>
        {columns.map((column: any, index: number) => (
          <div
            key={index}
            style={{
              flex: column.width ? `0 0 ${column.width}px` : 1,
              padding: '0 8px',
              fontWeight: 'bold',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}
          >
            {column.title}
          </div>
        ))}
      </div>
      
      {/* 虚拟化内容 */}
      <List
        height={height}
        itemCount={dataSource.length}
        itemSize={itemHeight}
        itemData={memoizedData}
      >
        {VirtualTableRow}
      </List>
    </div>
  );
});

VirtualTable.displayName = 'VirtualTable';

export default VirtualTable;
