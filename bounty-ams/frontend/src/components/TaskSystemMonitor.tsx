import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Typography,
  Space,
  Table,
  Tag,
  Alert,
  Divider,
  But<PERSON>
} from 'antd';
import {
  DashboardOutlined,
  ThunderboltOutlined,
  RobotOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  TrophyOutlined,
  FireOutlined
} from '@ant-design/icons';
import { monitoringService, taskExecutionService, newAgentService } from '../services/api';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

const { Title, Text } = Typography;

interface SystemStats {
  task_stats: {
    total: number;
    pending: number;
    running: number;
    success: number;
    failed: number;
  };
  agent_stats: {
    total: number;
    online: number;
    busy: number;
    offline: number;
  };
  queue_size: number;
  avg_execution_time: number;
}

const TaskSystemMonitor: React.FC = () => {
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [recentTasks, setRecentTasks] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadSystemStats();
    loadRecentTasks();
    
    // 定时刷新
    const interval = setInterval(() => {
      loadSystemStats();
      loadRecentTasks();
    }, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const loadSystemStats = async () => {
    try {
      // 并行加载任务执行数据和Agent数据
      const [taskResponse, agentResponse] = await Promise.all([
        taskExecutionService.getExecutions({ limit: 1000 }).catch(() => ({ executions: [] })),
        newAgentService.getAgents().catch(() => [])
      ]);

      const tasks = taskResponse.executions || [];
      const agents = agentResponse || [];

      // 计算任务统计
      const taskStats = {
        total: tasks.length,
        pending: tasks.filter(t => t.status === 'pending').length,
        running: tasks.filter(t => t.status === 'running').length,
        success: tasks.filter(t => t.status === 'success').length,
        failed: tasks.filter(t => t.status === 'failed').length
      };

      // 计算Agent统计
      const agentStats = {
        total: agents.length,
        online: agents.filter(a => a.status === 'online').length,
        busy: agents.filter(a => a.status === 'busy').length,
        offline: agents.filter(a => a.status === 'offline').length
      };

      // 计算平均执行时间
      const completedTasks = tasks.filter(t => t.start_time && t.end_time);
      const avgExecutionTime = completedTasks.length > 0
        ? completedTasks.reduce((sum, task) => {
            const duration = new Date(task.end_time).getTime() - new Date(task.start_time).getTime();
            return sum + duration / 1000; // 转换为秒
          }, 0) / completedTasks.length
        : 0;

      // 计算队列大小（等待中的任务）
      const queueSize = taskStats.pending;

      const realStats: SystemStats = {
        task_stats: taskStats,
        agent_stats: agentStats,
        queue_size: queueSize,
        avg_execution_time: avgExecutionTime
      };

      setStats(realStats);
    } catch (error) {
      console.error('加载系统统计失败:', error);
      // 设置默认值避免页面崩溃
      setStats({
        task_stats: { total: 0, pending: 0, running: 0, success: 0, failed: 0 },
        agent_stats: { total: 0, online: 0, busy: 0, offline: 0 },
        queue_size: 0,
        avg_execution_time: 0
      });
    }
  };

  const loadRecentTasks = async () => {
    setLoading(true);
    try {
      const response = await taskExecutionService.getExecutions({ limit: 10 });
      setRecentTasks(response.executions || []);
    } catch (error) {
      console.error('加载最近任务失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const recentTaskColumns = [
    {
      title: '任务ID',
      dataIndex: 'execution_id',
      key: 'execution_id',
      width: 120,
      render: (text: string) => (
        <Text code style={{ fontSize: '11px' }}>{text.slice(-8)}</Text>
      )
    },
    {
      title: '模板',
      dataIndex: 'template_name',
      key: 'template_name',
      render: (text: string) => text || '未知模板'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          pending: { color: 'default', text: '等待' },
          running: { color: 'processing', text: '运行' },
          success: { color: 'success', text: '成功' },
          failed: { color: 'error', text: '失败' }
        };
        const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '开始时间',
      dataIndex: 'start_time',
      key: 'start_time',
      render: (time: string) => time ? dayjs(time).format('HH:mm:ss') : '-'
    }
  ];

  if (!stats) {
    return <div>加载中...</div>;
  }

  const successRate = stats.task_stats.total > 0 
    ? Math.round((stats.task_stats.success / stats.task_stats.total) * 100) 
    : 0;

  const agentUtilization = stats.agent_stats.total > 0
    ? Math.round((stats.agent_stats.busy / stats.agent_stats.total) * 100)
    : 0;

  return (
    <div>
      {/* 系统健康状态 */}
      <Alert
        message="系统运行状态良好"
        description={`当前有 ${stats.task_stats.running} 个任务正在执行，${stats.agent_stats.online} 个Agent在线`}
        type="success"
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* 核心指标 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="任务成功率"
              value={successRate}
              suffix="%"
              valueStyle={{ 
                color: successRate >= 90 ? '#52c41a' : successRate >= 70 ? '#faad14' : '#ff4d4f' 
              }}
              prefix={<TrophyOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均执行时间"
              value={stats.avg_execution_time}
              suffix="秒"
              precision={1}
              valueStyle={{ color: '#1890ff' }}
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="队列长度"
              value={stats.queue_size}
              valueStyle={{ 
                color: stats.queue_size > 20 ? '#ff4d4f' : stats.queue_size > 10 ? '#faad14' : '#52c41a' 
              }}
              prefix={<FireOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Agent利用率"
              value={agentUtilization}
              suffix="%"
              valueStyle={{ color: '#722ed1' }}
              prefix={<RobotOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 详细统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={12}>
          <Card title="任务执行统计" extra={<ThunderboltOutlined />}>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic title="总任务数" value={stats.task_stats.total} />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="运行中" 
                  value={stats.task_stats.running} 
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="等待中" 
                  value={stats.task_stats.pending} 
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="已完成" 
                  value={stats.task_stats.success} 
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
            </Row>
            
            <Divider style={{ margin: '16px 0' }} />
            
            <div>
              <Text strong>执行进度分布</Text>
              <div style={{ marginTop: 8 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                  <Text style={{ fontSize: 12 }}>成功</Text>
                  <Text style={{ fontSize: 12 }}>{stats.task_stats.success}</Text>
                </div>
                <Progress 
                  percent={Math.round((stats.task_stats.success / stats.task_stats.total) * 100)} 
                  strokeColor="#52c41a"
                  size="small"
                />
                
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4, marginTop: 8 }}>
                  <Text style={{ fontSize: 12 }}>失败</Text>
                  <Text style={{ fontSize: 12 }}>{stats.task_stats.failed}</Text>
                </div>
                <Progress 
                  percent={Math.round((stats.task_stats.failed / stats.task_stats.total) * 100)} 
                  strokeColor="#ff4d4f"
                  size="small"
                />
              </div>
            </div>
          </Card>
        </Col>
        
        <Col span={12}>
          <Card title="Agent集群状态" extra={<RobotOutlined />}>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic title="总Agent数" value={stats.agent_stats.total} />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="在线Agent" 
                  value={stats.agent_stats.online} 
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="忙碌Agent" 
                  value={stats.agent_stats.busy} 
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="离线Agent" 
                  value={stats.agent_stats.offline} 
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Col>
            </Row>
            
            <Divider style={{ margin: '16px 0' }} />
            
            <div>
              <Text strong>集群健康度</Text>
              <div style={{ marginTop: 8 }}>
                <Progress 
                  percent={Math.round((stats.agent_stats.online / stats.agent_stats.total) * 100)}
                  strokeColor="#52c41a"
                  format={(percent) => `${percent}% 健康`}
                />
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 最近任务 */}
      <Card
        title={
          <Space>
            <ClockCircleOutlined />
            最近任务执行
          </Space>
        }
        extra={
          <Button icon={<ReloadOutlined />} onClick={loadRecentTasks}>
            刷新
          </Button>
        }
      >
        <Table
          columns={recentTaskColumns}
          dataSource={recentTasks}
          rowKey="execution_id"
          loading={loading}
          size="small"
          pagination={false}
          scroll={{ y: 300 }}
        />
      </Card>
    </div>
  );
};

export default TaskSystemMonitor;
