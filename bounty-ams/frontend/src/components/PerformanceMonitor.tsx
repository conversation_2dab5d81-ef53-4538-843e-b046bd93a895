import React, { useState, useEffect, memo } from 'react';
import { Card, Statistic, Row, Col, Progress, Button, Space, Divider } from 'antd';
import { MonitorOutlined, ReloadOutlined, DeleteOutlined } from '@ant-design/icons';

interface PerformanceMetrics {
  memoryUsage: number;
  renderTime: number;
  bundleSize: number;
  cacheHitRate: number;
  apiResponseTime: number;
}

const PerformanceMonitor: React.FC = memo(() => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    memoryUsage: 0,
    renderTime: 0,
    bundleSize: 0,
    cacheHitRate: 0,
    apiResponseTime: 0
  });
  const [isVisible, setIsVisible] = useState(false);

  // 获取性能指标
  const getPerformanceMetrics = () => {
    const performance = window.performance;
    const memory = (performance as any).memory;
    
    // 内存使用情况
    const memoryUsage = memory ? Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100) : 0;
    
    // 渲染时间
    const navigationTiming = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const renderTime = navigationTiming ? Math.round(navigationTiming.loadEventEnd - navigationTiming.fetchStart) : 0;
    
    // 估算bundle大小（基于资源加载）
    const resources = performance.getEntriesByType('resource');
    const bundleSize = Math.round(resources.reduce((total, resource) => {
      const resourceEntry = resource as PerformanceResourceTiming;
      return total + (resourceEntry.transferSize || 0);
    }, 0) / 1024); // KB
    
    // 模拟缓存命中率（实际应该从缓存服务获取）
    const cacheHitRate = Math.round(Math.random() * 30 + 70); // 70-100%
    
    // 模拟API响应时间
    const apiResponseTime = Math.round(Math.random() * 500 + 100); // 100-600ms
    
    setMetrics({
      memoryUsage,
      renderTime,
      bundleSize,
      cacheHitRate,
      apiResponseTime
    });
  };

  // 清除缓存
  const clearCache = () => {
    // 清除localStorage
    localStorage.clear();
    
    // 清除sessionStorage
    sessionStorage.clear();
    
    // 如果有自定义缓存，也清除
    if (window.caches) {
      window.caches.keys().then(names => {
        names.forEach(name => {
          window.caches.delete(name);
        });
      });
    }
    
    // 重新获取指标
    setTimeout(getPerformanceMetrics, 100);
  };

  // 刷新指标
  const refreshMetrics = () => {
    getPerformanceMetrics();
  };

  useEffect(() => {
    getPerformanceMetrics();
    
    // 每30秒自动更新一次
    const interval = setInterval(getPerformanceMetrics, 30000);
    
    return () => clearInterval(interval);
  }, []);

  // 获取性能等级颜色
  const getPerformanceColor = (value: number, type: 'memory' | 'time' | 'cache') => {
    switch (type) {
      case 'memory':
        if (value < 50) return '#52c41a'; // 绿色
        if (value < 80) return '#faad14'; // 橙色
        return '#f5222d'; // 红色
      case 'time':
        if (value < 1000) return '#52c41a';
        if (value < 3000) return '#faad14';
        return '#f5222d';
      case 'cache':
        if (value > 80) return '#52c41a';
        if (value > 60) return '#faad14';
        return '#f5222d';
      default:
        return '#1890ff';
    }
  };

  if (!isVisible) {
    return (
      <div
        style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          zIndex: 1000
        }}
      >
        <Button
          type="primary"
          icon={<MonitorOutlined />}
          onClick={() => setIsVisible(true)}
          style={{
            borderRadius: '50%',
            width: '50px',
            height: '50px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
        </Button>
      </div>
    );
  }

  return (
    <div
      style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        width: '400px',
        zIndex: 1000,
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
      }}
    >
      <Card
        title="性能监控"
        size="small"
        extra={
          <Space>
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={refreshMetrics}
            />
            <Button
              size="small"
              icon={<DeleteOutlined />}
              onClick={clearCache}
            />
            <Button
              size="small"
              onClick={() => setIsVisible(false)}
            >
              ×
            </Button>
          </Space>
        }
      >
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Statistic
              title="内存使用"
              value={metrics.memoryUsage}
              suffix="%"
              valueStyle={{ color: getPerformanceColor(metrics.memoryUsage, 'memory') }}
            />
            <Progress
              percent={metrics.memoryUsage}
              strokeColor={getPerformanceColor(metrics.memoryUsage, 'memory')}
              size="small"
              showInfo={false}
            />
          </Col>
          
          <Col span={12}>
            <Statistic
              title="加载时间"
              value={metrics.renderTime}
              suffix="ms"
              valueStyle={{ color: getPerformanceColor(metrics.renderTime, 'time') }}
            />
          </Col>
          
          <Col span={12}>
            <Statistic
              title="Bundle大小"
              value={metrics.bundleSize}
              suffix="KB"
            />
          </Col>
          
          <Col span={12}>
            <Statistic
              title="缓存命中率"
              value={metrics.cacheHitRate}
              suffix="%"
              valueStyle={{ color: getPerformanceColor(metrics.cacheHitRate, 'cache') }}
            />
          </Col>
          
          <Col span={24}>
            <Divider style={{ margin: '8px 0' }} />
            <Statistic
              title="API响应时间"
              value={metrics.apiResponseTime}
              suffix="ms"
              valueStyle={{ fontSize: '14px' }}
            />
          </Col>
        </Row>
      </Card>
    </div>
  );
});

PerformanceMonitor.displayName = 'PerformanceMonitor';

export default PerformanceMonitor;
