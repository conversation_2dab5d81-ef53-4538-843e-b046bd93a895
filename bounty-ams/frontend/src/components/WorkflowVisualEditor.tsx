import React, { useState, useEffect, use<PERSON>allback, useMemo } from 'react';
import {
  Card,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  message,
  Tooltip,
  Typography,
  Row,
  Col,
  Tabs,
  Drawer,
  Steps,
  Timeline,
  Descriptions,
  Badge,
  Divider,
  Alert,
  Switch,
  InputNumber,
  Collapse,
  Tree,
  Popover,
  Tag
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  PlayCircleOutlined,
  SettingOutlined,
  BranchesOutlined,
  ThunderboltOutlined,
  RobotOutlined,
  LinkOutlined,
  NodeIndexOutlined,
  ClusterOutlined,
  ApartmentOutlined,
  SaveOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { OptimizedCard } from './OptimizedComponents';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;
const { TextArea } = Input;
const { Step } = Steps;

interface WorkflowNode {
  id: string;
  type: 'task' | 'stage' | 'condition' | 'parallel' | 'merge';
  name: string;
  template_id?: string;
  parameters?: Record<string, any>;
  condition?: string;
  depends_on: string[];
  position: { x: number; y: number };
  status?: 'pending' | 'running' | 'success' | 'failed';
}

interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  condition?: string;
  type: 'success' | 'failure' | 'always';
}

interface WorkflowGraph {
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
}

const WorkflowVisualEditor: React.FC<{
  workflow?: any;
  visible: boolean;
  onClose: () => void;
  onSave: (workflow: any) => void;
}> = ({ workflow, visible, onClose, onSave }) => {
  const [graph, setGraph] = useState<WorkflowGraph>({ nodes: [], edges: [] });
  const [selectedNode, setSelectedNode] = useState<WorkflowNode | null>(null);
  const [selectedEdge, setSelectedEdge] = useState<WorkflowEdge | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [nodeForm] = Form.useForm();
  const [edgeForm] = Form.useForm();

  // 可用的任务模板
  const availableTemplates = useMemo(() => [
    { id: 'subdomain_discovery_v1', name: '子域名发现', category: 'reconnaissance' },
    { id: 'port_scan_v1', name: '端口扫描', category: 'scanning' },
    { id: 'service_detection_v1', name: '服务识别', category: 'identification' },
    { id: 'web_screenshot_v1', name: 'Web截图', category: 'identification' },
    { id: 'nuclei_scan_v1', name: 'Nuclei扫描', category: 'vulnerability' },
    { id: 'directory_bruteforce_v1', name: '目录爆破', category: 'discovery' },
    { id: 'ssl_check_v1', name: 'SSL检查', category: 'security' }
  ], []);

  useEffect(() => {
    if (workflow) {
      // 将工作流转换为图形表示
      convertWorkflowToGraph(workflow);
    } else {
      // 初始化空图
      setGraph({ nodes: [], edges: [] });
    }
  }, [workflow]);

  const convertWorkflowToGraph = (workflow: any) => {
    const nodes: WorkflowNode[] = [];
    const edges: WorkflowEdge[] = [];
    let yOffset = 0;

    workflow.stages?.forEach((stage: any, stageIndex: number) => {
      // 添加阶段节点
      const stageNode: WorkflowNode = {
        id: stage.stage_id,
        type: 'stage',
        name: stage.name,
        depends_on: [],
        position: { x: 100, y: yOffset },
        condition: stage.condition
      };
      nodes.push(stageNode);

      let xOffset = 300;
      stage.tasks?.forEach((task: any, taskIndex: number) => {
        // 添加任务节点
        const taskNode: WorkflowNode = {
          id: task.task_id,
          type: 'task',
          name: task.task_id,
          template_id: task.template_id,
          parameters: task.parameters,
          condition: task.condition,
          depends_on: task.depends_on || [],
          position: { x: xOffset, y: yOffset }
        };
        nodes.push(taskNode);

        // 添加阶段到任务的边
        edges.push({
          id: `${stage.stage_id}-${task.task_id}`,
          source: stage.stage_id,
          target: task.task_id,
          type: 'always'
        });

        // 添加任务依赖边
        task.depends_on?.forEach((depId: string) => {
          edges.push({
            id: `${depId}-${task.task_id}`,
            source: depId,
            target: task.task_id,
            type: 'success'
          });
        });

        xOffset += 200;
      });

      yOffset += 150;
    });

    setGraph({ nodes, edges });
  };

  const handleAddNode = (type: WorkflowNode['type']) => {
    const newNode: WorkflowNode = {
      id: `node_${Date.now()}`,
      type,
      name: `新${type === 'task' ? '任务' : '阶段'}`,
      depends_on: [],
      position: { x: 200, y: 200 }
    };

    setGraph(prev => ({
      ...prev,
      nodes: [...prev.nodes, newNode]
    }));

    setSelectedNode(newNode);
    setIsEditing(true);
  };

  const handleNodeClick = (node: WorkflowNode) => {
    setSelectedNode(node);
    nodeForm.setFieldsValue(node);
  };

  const handleNodeUpdate = (values: any) => {
    if (!selectedNode) return;

    const updatedNode = { ...selectedNode, ...values };
    setGraph(prev => ({
      ...prev,
      nodes: prev.nodes.map(n => n.id === selectedNode.id ? updatedNode : n)
    }));

    setSelectedNode(updatedNode);
    setIsEditing(false);
    message.success('节点更新成功');
  };

  const handleDeleteNode = (nodeId: string) => {
    setGraph(prev => ({
      nodes: prev.nodes.filter(n => n.id !== nodeId),
      edges: prev.edges.filter(e => e.source !== nodeId && e.target !== nodeId)
    }));

    if (selectedNode?.id === nodeId) {
      setSelectedNode(null);
    }
  };

  const handleAddEdge = (sourceId: string, targetId: string) => {
    const newEdge: WorkflowEdge = {
      id: `${sourceId}-${targetId}`,
      source: sourceId,
      target: targetId,
      type: 'success'
    };

    setGraph(prev => ({
      ...prev,
      edges: [...prev.edges, newEdge]
    }));
  };

  const renderNode = (node: WorkflowNode) => {
    const getNodeColor = () => {
      switch (node.type) {
        case 'task': return '#1890ff';
        case 'stage': return '#52c41a';
        case 'condition': return '#faad14';
        case 'parallel': return '#722ed1';
        case 'merge': return '#eb2f96';
        default: return '#d9d9d9';
      }
    };

    const getNodeIcon = () => {
      switch (node.type) {
        case 'task': return <ThunderboltOutlined />;
        case 'stage': return <ClusterOutlined />;
        case 'condition': return <BranchesOutlined />;
        case 'parallel': return <ApartmentOutlined />;
        case 'merge': return <NodeIndexOutlined />;
        default: return <RobotOutlined />;
      }
    };

    return (
      <div
        key={node.id}
        style={{
          position: 'absolute',
          left: node.position.x,
          top: node.position.y,
          width: 160,
          height: 80,
          border: `2px solid ${getNodeColor()}`,
          borderRadius: 8,
          backgroundColor: 'white',
          cursor: 'pointer',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: selectedNode?.id === node.id ? '0 0 10px rgba(24, 144, 255, 0.5)' : '0 2px 8px rgba(0,0,0,0.1)'
        }}
        onClick={() => handleNodeClick(node)}
      >
        <div style={{ color: getNodeColor(), fontSize: 16, marginBottom: 4 }}>
          {getNodeIcon()}
        </div>
        <Text style={{ fontSize: 12, textAlign: 'center', padding: '0 4px' }} ellipsis>
          {node.name}
        </Text>
        {node.template_id && (
          <Tag size="small" style={{ fontSize: 10, marginTop: 2 }}>
            {availableTemplates.find(t => t.id === node.template_id)?.name || node.template_id}
          </Tag>
        )}
        
        {/* 节点操作按钮 */}
        <div style={{ position: 'absolute', top: -10, right: -10 }}>
          <Space size={2}>
            <Button
              size="small"
              type="text"
              icon={<EditOutlined />}
              style={{ fontSize: 10, padding: 2 }}
              onClick={(e) => {
                e.stopPropagation();
                setSelectedNode(node);
                setIsEditing(true);
              }}
            />
            <Button
              size="small"
              type="text"
              danger
              icon={<DeleteOutlined />}
              style={{ fontSize: 10, padding: 2 }}
              onClick={(e) => {
                e.stopPropagation();
                handleDeleteNode(node.id);
              }}
            />
          </Space>
        </div>
      </div>
    );
  };

  const renderEdge = (edge: WorkflowEdge) => {
    const sourceNode = graph.nodes.find(n => n.id === edge.source);
    const targetNode = graph.nodes.find(n => n.id === edge.target);
    
    if (!sourceNode || !targetNode) return null;

    const getEdgeColor = () => {
      switch (edge.type) {
        case 'success': return '#52c41a';
        case 'failure': return '#ff4d4f';
        case 'always': return '#1890ff';
        default: return '#d9d9d9';
      }
    };

    // 简化的边渲染（实际项目中可能需要使用SVG或Canvas）
    const startX = sourceNode.position.x + 80;
    const startY = sourceNode.position.y + 40;
    const endX = targetNode.position.x + 80;
    const endY = targetNode.position.y + 40;

    return (
      <svg
        key={edge.id}
        style={{
          position: 'absolute',
          left: 0,
          top: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
          zIndex: -1
        }}
      >
        <defs>
          <marker
            id={`arrowhead-${edge.id}`}
            markerWidth="10"
            markerHeight="7"
            refX="9"
            refY="3.5"
            orient="auto"
          >
            <polygon
              points="0 0, 10 3.5, 0 7"
              fill={getEdgeColor()}
            />
          </marker>
        </defs>
        <line
          x1={startX}
          y1={startY}
          x2={endX}
          y2={endY}
          stroke={getEdgeColor()}
          strokeWidth="2"
          markerEnd={`url(#arrowhead-${edge.id})`}
        />
      </svg>
    );
  };

  const handleSaveWorkflow = () => {
    // 将图形转换回工作流格式
    const stages = graph.nodes
      .filter(n => n.type === 'stage')
      .map(stageNode => {
        const stageTasks = graph.nodes
          .filter(n => n.type === 'task' && 
            graph.edges.some(e => e.source === stageNode.id && e.target === n.id))
          .map(taskNode => ({
            task_id: taskNode.id,
            template_id: taskNode.template_id,
            parameters: taskNode.parameters || {},
            condition: taskNode.condition || 'always',
            depends_on: taskNode.depends_on,
            retry_count: 0,
            on_failure: 'stop'
          }));

        return {
          stage_id: stageNode.id,
          name: stageNode.name,
          parallel: false,
          condition: stageNode.condition || 'always',
          on_failure: 'stop',
          tasks: stageTasks
        };
      });

    const workflowData = {
      workflow_id: workflow?.workflow_id || `workflow_${Date.now()}`,
      name: workflow?.name || '新工作流',
      version: '1.0.0',
      description: workflow?.description || '',
      parameters: workflow?.parameters || {},
      stages,
      error_handling: { strategy: 'continue_on_error' },
      output_aggregation: { merge_strategy: 'deep_merge' }
    };

    onSave(workflowData);
    message.success('工作流保存成功');
  };

  return (
    <Modal
      title="工作流可视化编辑器"
      open={visible}
      onCancel={onClose}
      width="90%"
      style={{ top: 20 }}
      footer={
        <Space>
          <Button onClick={onClose}>取消</Button>
          <Button type="primary" icon={<SaveOutlined />} onClick={handleSaveWorkflow}>
            保存工作流
          </Button>
        </Space>
      }
    >
      <Row gutter={16} style={{ height: '70vh' }}>
        {/* 工具栏 */}
        <Col span={4}>
          <Card title="工具箱" size="small" style={{ height: '100%' }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button
                block
                icon={<ClusterOutlined />}
                onClick={() => handleAddNode('stage')}
              >
                添加阶段
              </Button>
              <Button
                block
                icon={<ThunderboltOutlined />}
                onClick={() => handleAddNode('task')}
              >
                添加任务
              </Button>
              <Button
                block
                icon={<BranchesOutlined />}
                onClick={() => handleAddNode('condition')}
              >
                添加条件
              </Button>
              <Button
                block
                icon={<ApartmentOutlined />}
                onClick={() => handleAddNode('parallel')}
              >
                并行节点
              </Button>
              
              <Divider />
              
              <Title level={5}>可用模板</Title>
              <div style={{ maxHeight: 200, overflowY: 'auto' }}>
                {availableTemplates.map(template => (
                  <Tag
                    key={template.id}
                    style={{ margin: '2px', cursor: 'pointer' }}
                    onClick={() => {
                      const newNode: WorkflowNode = {
                        id: `task_${Date.now()}`,
                        type: 'task',
                        name: template.name,
                        template_id: template.id,
                        depends_on: [],
                        position: { x: 200, y: 200 }
                      };
                      setGraph(prev => ({
                        ...prev,
                        nodes: [...prev.nodes, newNode]
                      }));
                    }}
                  >
                    {template.name}
                  </Tag>
                ))}
              </div>
            </Space>
          </Card>
        </Col>

        {/* 画布区域 */}
        <Col span={16}>
          <Card title="工作流画布" size="small" style={{ height: '100%' }}>
            <div
              style={{
                position: 'relative',
                width: '100%',
                height: 'calc(100% - 40px)',
                border: '1px dashed #d9d9d9',
                borderRadius: 4,
                overflow: 'auto',
                backgroundColor: '#fafafa'
              }}
            >
              {/* 渲染边 */}
              {graph.edges.map(renderEdge)}
              
              {/* 渲染节点 */}
              {graph.nodes.map(renderNode)}
              
              {/* 空状态 */}
              {graph.nodes.length === 0 && (
                <div
                  style={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    textAlign: 'center',
                    color: '#999'
                  }}
                >
                  <RobotOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                  <div>从左侧工具箱拖拽组件到此处开始设计工作流</div>
                </div>
              )}
            </div>
          </Card>
        </Col>

        {/* 属性面板 */}
        <Col span={4}>
          <Card title="属性面板" size="small" style={{ height: '100%' }}>
            {selectedNode ? (
              <Form
                form={nodeForm}
                layout="vertical"
                onFinish={handleNodeUpdate}
                initialValues={selectedNode}
              >
                <Form.Item label="节点名称" name="name" rules={[{ required: true }]}>
                  <Input />
                </Form.Item>
                
                {selectedNode.type === 'task' && (
                  <Form.Item label="任务模板" name="template_id">
                    <Select>
                      {availableTemplates.map(template => (
                        <Select.Option key={template.id} value={template.id}>
                          {template.name}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                )}
                
                <Form.Item label="执行条件" name="condition">
                  <Input placeholder="always" />
                </Form.Item>
                
                <Form.Item>
                  <Space>
                    <Button type="primary" htmlType="submit" size="small">
                      更新
                    </Button>
                    <Button size="small" onClick={() => setIsEditing(false)}>
                      取消
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            ) : (
              <div style={{ textAlign: 'center', color: '#999', marginTop: 50 }}>
                <EyeOutlined style={{ fontSize: 32, marginBottom: 16 }} />
                <div>选择一个节点查看属性</div>
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </Modal>
  );
};

export default WorkflowVisualEditor;
