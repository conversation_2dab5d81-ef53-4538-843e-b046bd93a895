import React, { useState, useEffect } from 'react';
import { Outlet, Navigate, useLocation, useNavigate } from 'react-router-dom';
import {
  Layout,
  Menu,
  Avatar,
  Dropdown,
  Space,
  Button,
  theme,
  Typography,
  Badge,
} from 'antd';
import {
  DashboardOutlined,
  DatabaseOutlined,
  RobotOutlined,
  UserOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SettingOutlined,
  SecurityScanOutlined,
  BellOutlined,
  FileTextOutlined,
  MonitorOutlined,
} from '@ant-design/icons';
import { useAuth } from '../../context/AuthContext';
import { apiCache } from '../../services/cache';
import PerformanceMonitor from '../PerformanceMonitor';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

const MainLayout: React.FC = () => {
  const { user, logout } = useAuth();
  const [collapsed, setCollapsed] = useState(false);
  const [prevLocation, setPrevLocation] = useState<string>('');
  const location = useLocation();
  const navigate = useNavigate();
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  // 处理路由切换时的状态清理
  useEffect(() => {
    // 如果路由发生变化，进行智能缓存清理
    if (prevLocation && prevLocation !== location.pathname) {
      console.log('路由切换:', prevLocation, '->', location.pathname);
      
      // 只在特定情况下清理缓存，避免过度清理
      const shouldClearCache = (
        // 从登录页面切换到其他页面时，清理所有缓存
        prevLocation === '/login' ||
        // 在不同的主要功能模块间切换时，清理相关缓存
        (prevLocation.startsWith('/assets') && !location.pathname.startsWith('/assets')) ||
        (prevLocation.startsWith('/agents') && !location.pathname.startsWith('/agents')) ||
        (prevLocation.startsWith('/tasks') && !location.pathname.startsWith('/tasks'))
      );
      
      if (shouldClearCache && apiCache) {
        console.log('清理页面特定缓存');
        
        // 选择性清理缓存，保留共享数据
        const cacheKeysToClean = [
          `${prevLocation}_data`, // 页面特定数据
          'search_results', // 搜索结果
          'recent_data' // 最近数据
        ];
        
        cacheKeysToClean.forEach(key => {
          try {
            apiCache.clear(key);
          } catch (error) {
            console.warn('清理缓存失败:', key, error);
          }
        });
      }
    }
    
    setPrevLocation(location.pathname);
  }, [location.pathname, prevLocation]);

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // 完整菜单项
  const menuItems = [
    {
      key: '/',
      icon: <DashboardOutlined />,
      label: '仪表盘',
    },
    {
      key: '/platform-project',
      icon: <DatabaseOutlined />,
      label: '平台项目管理',
    },
    {
      key: '/assets',
      icon: <DatabaseOutlined />,
      label: '资产管理',
    },
    {
      key: '/data-import',
      icon: <FileTextOutlined />,
      label: '数据导入',
    },
    {
      key: '/search-analytics',
      icon: <SecurityScanOutlined />,
      label: '搜索分析',
    },
    {
      key: '/workflows',
      icon: <SecurityScanOutlined />,
      label: '工作流',
    },
    {
      key: '/agents',
      icon: <RobotOutlined />,
      label: 'Agent管理',
    },
    {
      key: '/tasks',
      icon: <SecurityScanOutlined />,
      label: '任务管理',
    },
    {
      key: '/task-center',
      icon: <SecurityScanOutlined />,
      label: '任务管理中心',
    },
    {
      key: '/models',
      icon: <SettingOutlined />,
      label: '动态模型',
    },
  ];

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
      onClick: () => navigate('/settings'),
    },
    ...(user?.is_admin ? [{
      key: 'user-management',
      icon: <UserOutlined />,
      label: '用户管理',
      onClick: () => navigate('/user-management'),
    }] : []),
    {
      key: 'divider',
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: logout,
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        style={{
          background: colorBgContainer,
          borderRight: '1px solid #f0f0f0',
        }}
      >
        <div style={{ 
          height: 64, 
          padding: '16px', 
          display: 'flex', 
          alignItems: 'center',
          justifyContent: collapsed ? 'center' : 'flex-start',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <SecurityScanOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
          {!collapsed && (
            <Text strong style={{ marginLeft: '8px', fontSize: '16px' }}>
              Bounty AMS
            </Text>
          )}
        </div>
        
        <Menu
          theme="light"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ border: 'none' }}
        />
      </Sider>
      
      <Layout>
        <Header style={{
          padding: '0 16px',
          background: colorBgContainer,
          borderBottom: '1px solid #f0f0f0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{
              fontSize: '16px',
              width: 64,
              height: 64,
            }}
          />
          
          <Space size="middle">
            <Badge count={3} size="small">
              <Button 
                type="text" 
                icon={<BellOutlined />} 
                size="large"
                style={{ color: '#666' }}
              />
            </Badge>
            
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <Space style={{ cursor: 'pointer' }}>
                <Avatar icon={<UserOutlined />} />
                <Text>{user?.username || '未知用户'}</Text>
                <Badge 
                  color={user?.is_admin ? 'green' : 'blue'} 
                  text={user?.is_admin ? '管理员' : '只读用户'}
                />
              </Space>
            </Dropdown>
          </Space>
        </Header>
        
        <Content style={{
          margin: '16px',
          padding: 24,
          background: colorBgContainer,
          borderRadius: borderRadiusLG,
        }}>
          <Outlet />
        </Content>
      </Layout>

      {/* 性能监控组件 */}
      <PerformanceMonitor />
    </Layout>
  );
};

export default MainLayout;