import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Progress,
  Typography,
  Row,
  Col,
  Statistic,
  Badge,
  Tooltip,
  Modal,
  Form,
  Input,
  Select,
  message,
  Divider,
  Drawer,
  Descriptions,
  Dropdown,
  Popconfirm,
  Alert
} from 'antd';
import type { MenuProps } from 'antd';
import {
  RobotOutlined,
  PlusOutlined,
  ReloadOutlined,
  SettingOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
  ClockCircleOutlined,
  EyeOutlined,
  KeyOutlined,
  StopOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  PoweroffOutlined,
  RedoOutlined,
  MinusCircleOutlined,
  DownOutlined,
  WifiOutlined,
  DisconnectOutlined,
  CopyOutlined
} from '@ant-design/icons';
import { agentService, agentKeyService } from '../services/api';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

const { Title, Text } = Typography;

interface Agent {
  agent_id: string;
  name: string;
  status: 'online' | 'offline' | 'busy' | 'maintenance' | 'paused' | 'stopped';
  current_load: number;
  max_capacity: number;
  last_heartbeat: string;
  running_tasks: string[];
  capabilities: string[];
  location?: string;
  tags?: string[];
  version?: string;
  platform?: string;
  ip_address?: string;
  port?: number;
  api_key?: string;
  created_at?: string;
  updated_at?: string;
}

const TaskAgentManager: React.FC = () => {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    loadAgents();
    // 定时刷新Agent状态
    const interval = setInterval(loadAgents, 10000);
    return () => clearInterval(interval);
  }, []);

  const loadAgents = async () => {
    setLoading(true);
    try {
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('请求超时')), 3000)
      );

      const response = await Promise.race([
        agentService.getAgents(),
        timeoutPromise
      ]);

      console.log('API Response:', response);
      console.log('Response data:', (response as any).data);

      // 从响应中提取agents数组
      const responseData = (response as any).data;
      const agentsData = responseData?.agents || [];

      console.log('Parsed agents data:', agentsData);
      console.log('Agents count:', agentsData.length);

      setAgents(agentsData);

      if (agentsData.length === 0) {
        console.log('No agents found');
      }
    } catch (error: any) {
      console.error('Error loading agents:', error);
      console.error('Error response:', error.response);
      console.error('Error status:', error.response?.status);
      console.error('Error data:', error.response?.data);

      if (error.message === '请求超时' || error.code === 'ECONNABORTED') {
        message.warning('连接超时，可能Agent服务未启动');
      } else if (error.response?.status === 401) {
        message.error('认证失败，请重新登录');
      } else {
        message.error(`加载Agent失败: ${error.message || '未知错误'}`);
      }
      // 即使失败也设置空数组，避免页面一直loading
      setAgents([]);
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetails = async (agent: Agent) => {
    try {
      const response = await agentService.getAgentDetails(agent.agent_id);
      setSelectedAgent(response.data);
      setDrawerVisible(true);
    } catch (error) {
      message.error('加载Agent详情失败');
    }
  };

  const handleDeleteAgent = async (agent: Agent) => {
    try {
      await agentService.deleteAgent(agent.agent_id);
      message.success(`Agent "${agent.name}" 已删除`);
      loadAgents(); // 重新加载列表
    } catch (error: any) {
      console.error('Delete agent error:', error);
      message.error(`删除Agent失败: ${error.response?.data?.detail || '未知错误'}`);
    }
  };

  const handleControlAgent = async (agent: Agent, action: string, actionName: string) => {
    try {
      const response = await agentService.sendCommand(agent.agent_id, action);
      const result = response.data;

      message.success(`${actionName}任务已创建 (任务ID: ${result.task_id.substring(0, 8)}...)`);

      // 添加说明信息
      if (action === 'pause' || action === 'stop' || action === 'restart') {
        message.info(`注意: Agent控制命令已发送，但当前Go Agent版本可能需要重启才能处理控制命令`, 5);
      }

      // 短时间后刷新状态
      setTimeout(() => {
        loadAgents();
      }, 2000);
    } catch (error: any) {
      console.error(`${action} agent error:`, error);
      message.error(`${actionName}任务创建失败: ${error.response?.data?.detail || '任务创建失败'}`);
    }
  };

  const handlePauseAgent = (agent: Agent) => handleControlAgent(agent, 'pause', '暂停');
  const handleResumeAgent = (agent: Agent) => handleControlAgent(agent, 'resume', '恢复');
  const handleStopAgent = (agent: Agent) => handleControlAgent(agent, 'stop', '停止');
  const handleRestartAgent = (agent: Agent) => handleControlAgent(agent, 'restart', '重启');
  const handleCancelTasks = (agent: Agent) => handleControlAgent(agent, 'cancel_tasks', '取消任务');

  const handleRegisterAgent = async (values: any) => {
    try {
      await agentService.registerAgent(values);
      message.success('Agent注册成功');
      setModalVisible(false);
      form.resetFields();
      loadAgents();
    } catch (error) {
      console.error('注册Agent失败:', error);
      message.error('注册Agent失败');
    }
  };

  // 统计数据
  const statistics = {
    total: agents.length,
    online: agents.filter(a => a.status === 'online' || a.status === 'busy').length,
    busy: agents.filter(a => a.status === 'busy').length,
    offline: agents.filter(a => a.status === 'offline').length,
    totalCapacity: agents.reduce((sum, a) => sum + a.max_capacity, 0),
    currentLoad: agents.reduce((sum, a) => sum + a.current_load, 0)
  };

  const getActionMenuItems = (agent: Agent): MenuProps['items'] => {
    const items: MenuProps['items'] = [
      {
        key: 'view',
        icon: <EyeOutlined />,
        label: '查看详情',
        onClick: () => handleViewDetails(agent)
      },
      {
        key: 'divider1',
        type: 'divider'
      }
    ];

    // 根据状态添加不同的控制选项
    if (agent.status === 'online' || agent.status === 'busy') {
      items.push(
        {
          key: 'pause',
          icon: <PauseCircleOutlined />,
          label: '暂停Agent',
          onClick: () => handlePauseAgent(agent)
        },
        {
          key: 'stop',
          icon: <StopOutlined />,
          label: '停止Agent',
          onClick: () => handleStopAgent(agent)
        }
      );
    }

    if (agent.status === 'paused') {
      items.push({
        key: 'resume',
        icon: <PlayCircleOutlined />,
        label: '恢复Agent',
        onClick: () => handleResumeAgent(agent)
      });
    }

    if (agent.status === 'offline' || agent.status === 'stopped') {
      items.push({
        key: 'restart',
        icon: <RedoOutlined />,
        label: '重启Agent',
        onClick: () => handleRestartAgent(agent)
      });
    }

    if (agent.running_tasks && agent.running_tasks.length > 0) {
      items.push({
        key: 'cancel_tasks',
        icon: <MinusCircleOutlined />,
        label: '取消所有任务',
        onClick: () => handleCancelTasks(agent)
      });
    }

    items.push(
      {
        key: 'divider2',
        type: 'divider'
      },
      {
        key: 'delete',
        icon: <DeleteOutlined />,
        label: '删除Agent',
        danger: true,
        onClick: () => {
          Modal.confirm({
            title: '确认删除',
            content: `确定要删除Agent "${agent.name}" 吗？`,
            okText: '删除',
            okType: 'danger',
            cancelText: '取消',
            onOk: () => handleDeleteAgent(agent)
          });
        }
      }
    );

    return items;
  };

  const columns = [
    {
      title: 'Agent信息',
      key: 'info',
      width: 200,
      render: (_, record: Agent) => (
        <div>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
            <RobotOutlined style={{ marginRight: 8, color: '#1890ff' }} />
            <Text strong>{record.name}</Text>
          </div>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.agent_id}
          </Text>
          {record.version && (
            <div>
              <Text type="secondary" style={{ fontSize: 11 }}>
                v{record.version}
              </Text>
            </div>
          )}
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string, record: Agent) => {
        const statusConfig = {
          online: { color: 'success', icon: <WifiOutlined />, text: '在线', bgColor: '#f6ffed' },
          busy: { color: 'processing', icon: <SyncOutlined spin />, text: '忙碌', bgColor: '#fff7e6' },
          offline: { color: 'error', icon: <DisconnectOutlined />, text: '离线', bgColor: '#fff2f0' },
          paused: { color: 'warning', icon: <PauseCircleOutlined />, text: '暂停', bgColor: '#fffbe6' },
          stopped: { color: 'default', icon: <StopOutlined />, text: '停止', bgColor: '#fafafa' },
          maintenance: { color: 'processing', icon: <SettingOutlined />, text: '维护', bgColor: '#f0f5ff' }
        };

        const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.offline;

        return (
          <div style={{
            padding: '4px 8px',
            borderRadius: '4px',
            backgroundColor: config.bgColor,
            border: `1px solid ${config.color === 'success' ? '#b7eb8f' :
                                  config.color === 'processing' ? '#91d5ff' :
                                  config.color === 'error' ? '#ffa39e' :
                                  config.color === 'warning' ? '#ffe58f' : '#d9d9d9'}`
          }}>
            <Space size={4}>
              {config.icon}
              <Text style={{ fontSize: 12, fontWeight: 500 }}>{config.text}</Text>
            </Space>
            {record.last_heartbeat && (
              <div style={{ fontSize: 10, color: '#666', marginTop: 2 }}>
                {dayjs(record.last_heartbeat).fromNow()}
              </div>
            )}
          </div>
        );
      }
    },
    {
      title: '负载情况',
      key: 'load',
      render: (_, record: Agent) => (
        <div style={{ width: 120 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
            <Text style={{ fontSize: 12 }}>{record.current_load}/{record.max_capacity}</Text>
            <Text style={{ fontSize: 12 }}>
              {Math.round((record.current_load / record.max_capacity) * 100)}%
            </Text>
          </div>
          <Progress 
            percent={(record.current_load / record.max_capacity) * 100} 
            size="small" 
            status={record.current_load >= record.max_capacity ? 'exception' : 'active'}
            showInfo={false}
          />
        </div>
      )
    },
    {
      title: '能力',
      dataIndex: 'capabilities',
      key: 'capabilities',
      render: (capabilities: string[]) => (
        <div>
          {capabilities.slice(0, 3).map(cap => (
            <Tag key={cap} size="small" style={{ margin: '1px' }}>
              {cap}
            </Tag>
          ))}
          {capabilities.length > 3 && (
            <Tooltip title={capabilities.slice(3).join(', ')}>
              <Tag size="small" style={{ margin: '1px' }}>
                +{capabilities.length - 3}
              </Tag>
            </Tooltip>
          )}
        </div>
      )
    },
    {
      title: '最后心跳',
      dataIndex: 'last_heartbeat',
      key: 'last_heartbeat',
      render: (time: string) => (
        <div>
          <div>{dayjs(time).format('HH:mm:ss')}</div>
          <Text type="secondary" style={{ fontSize: 11 }}>
            {dayjs(time).fromNow()}
          </Text>
        </div>
      )
    },
    {
      title: '网络信息',
      key: 'network',
      width: 150,
      render: (_, record: Agent) => (
        <div>
          {record.ip_address && (
            <div style={{ fontSize: 12 }}>
              <Text type="secondary">IP: </Text>
              <Text code>{record.ip_address}</Text>
            </div>
          )}
          {record.port && (
            <div style={{ fontSize: 12 }}>
              <Text type="secondary">端口: </Text>
              <Text code>{record.port}</Text>
            </div>
          )}
          {record.location && (
            <div style={{ fontSize: 12 }}>
              <Text type="secondary">位置: </Text>
              <Text>{record.location}</Text>
            </div>
          )}
        </div>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record: Agent) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            size="small"
            onClick={() => handleViewDetails(record)}
          />
          <Dropdown
            menu={{ items: getActionMenuItems(record) }}
            trigger={['click']}
            placement="bottomRight"
          >
            <Button type="text" size="small">
              <Space>
                <SettingOutlined />
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
        </Space>
      )
    }
  ];



  return (
    <div>
      {/* 统计概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic 
              title="总Agent数" 
              value={statistics.total} 
              prefix={<RobotOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic 
              title="在线Agent" 
              value={statistics.online} 
              valueStyle={{ color: '#52c41a' }}
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic 
              title="忙碌Agent" 
              value={statistics.busy} 
              valueStyle={{ color: '#faad14' }}
              prefix={<SyncOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic 
              title="总容量利用率" 
              value={statistics.totalCapacity > 0 ? Math.round((statistics.currentLoad / statistics.totalCapacity) * 100) : 0}
              suffix="%" 
              valueStyle={{ 
                color: statistics.currentLoad / statistics.totalCapacity > 0.8 ? '#ff4d4f' : '#1890ff' 
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* Agent列表 */}
      <Card
        title={
          <Space>
            <RobotOutlined />
            Agent管理
          </Space>
        }
        extra={
          <Space>
            <Button icon={<PlusOutlined />} onClick={() => setModalVisible(true)}>
              注册Agent
            </Button>
            <Button icon={<ReloadOutlined />} onClick={loadAgents}>
              刷新
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={agents}
          rowKey="agent_id"
          loading={loading}
          size="small"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个Agent`
          }}
        />
      </Card>

      {/* 注册Agent模态框 */}
      <Modal
        title="注册新Agent"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleRegisterAgent}
        >
          <Form.Item
            name="agent_id"
            label="Agent ID"
            rules={[{ required: true, message: '请输入Agent ID' }]}
          >
            <Input placeholder="例如: scanner-001" />
          </Form.Item>
          
          <Form.Item
            name="name"
            label="Agent名称"
            rules={[{ required: true, message: '请输入Agent名称' }]}
          >
            <Input placeholder="例如: 扫描器-01" />
          </Form.Item>
          
          <Form.Item
            name="capabilities"
            label="能力列表"
            rules={[{ required: true, message: '请选择Agent能力' }]}
          >
            <Select
              mode="tags"
              placeholder="选择或输入Agent能力"
              options={[
                { value: 'subfinder', label: 'subfinder' },
                { value: 'nmap', label: 'nmap' },
                { value: 'nuclei', label: 'nuclei' },
                { value: 'httpx', label: 'httpx' },
                { value: 'assetfinder', label: 'assetfinder' }
              ]}
            />
          </Form.Item>
          
          <Form.Item
            name="max_capacity"
            label="最大容量"
            rules={[{ required: true, message: '请输入最大容量' }]}
          >
            <Input type="number" placeholder="例如: 10" />
          </Form.Item>
          
          <Form.Item
            name="location"
            label="位置"
          >
            <Input placeholder="例如: 北京" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Agent详情抽屉 */}
      <Drawer
        title={
          <Space>
            <RobotOutlined />
            Agent详情
          </Space>
        }
        width={600}
        open={drawerVisible}
        onClose={() => setDrawerVisible(false)}
        extra={
          selectedAgent && (
            <Space>
              <Button
                icon={<CopyOutlined />}
                onClick={() => {
                  navigator.clipboard.writeText(selectedAgent.agent_id);
                  message.success('Agent ID已复制');
                }}
              >
                复制ID
              </Button>
              <Dropdown
                menu={{ items: getActionMenuItems(selectedAgent) }}
                trigger={['click']}
              >
                <Button type="primary">
                  <Space>
                    控制
                    <DownOutlined />
                  </Space>
                </Button>
              </Dropdown>
            </Space>
          )
        }
      >
        {selectedAgent && (
          <div>
            <Descriptions title="基本信息" bordered column={2}>
              <Descriptions.Item label="Agent ID" span={2}>
                <Text code>{selectedAgent.agent_id}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="名称">
                {selectedAgent.name}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Badge
                  status={
                    selectedAgent.status === 'online' ? 'success' :
                    selectedAgent.status === 'busy' ? 'processing' :
                    selectedAgent.status === 'offline' ? 'error' : 'default'
                  }
                  text={selectedAgent.status}
                />
              </Descriptions.Item>
              <Descriptions.Item label="版本">
                {selectedAgent.version || '未知'}
              </Descriptions.Item>
              <Descriptions.Item label="平台">
                {selectedAgent.platform || '未知'}
              </Descriptions.Item>
              <Descriptions.Item label="IP地址">
                {selectedAgent.ip_address || '未知'}
              </Descriptions.Item>
              <Descriptions.Item label="端口">
                {selectedAgent.port || '未知'}
              </Descriptions.Item>
              <Descriptions.Item label="位置">
                {selectedAgent.location || '未设置'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {selectedAgent.created_at ? dayjs(selectedAgent.created_at).format('YYYY-MM-DD HH:mm:ss') : '未知'}
              </Descriptions.Item>
              <Descriptions.Item label="最后心跳">
                {selectedAgent.last_heartbeat ? dayjs(selectedAgent.last_heartbeat).format('YYYY-MM-DD HH:mm:ss') : '无'}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {selectedAgent.updated_at ? dayjs(selectedAgent.updated_at).format('YYYY-MM-DD HH:mm:ss') : '未知'}
              </Descriptions.Item>
            </Descriptions>

            <Divider />

            <Descriptions title="性能信息" bordered column={2}>
              <Descriptions.Item label="当前负载">
                <Progress
                  percent={(selectedAgent.current_load / selectedAgent.max_capacity) * 100}
                  format={() => `${selectedAgent.current_load}/${selectedAgent.max_capacity}`}
                />
              </Descriptions.Item>
              <Descriptions.Item label="最大容量">
                {selectedAgent.max_capacity}
              </Descriptions.Item>
              <Descriptions.Item label="运行任务" span={2}>
                {selectedAgent.running_tasks && selectedAgent.running_tasks.length > 0 ? (
                  <div>
                    {selectedAgent.running_tasks.map(taskId => (
                      <Tag key={taskId} style={{ margin: '2px' }}>
                        {taskId}
                      </Tag>
                    ))}
                  </div>
                ) : (
                  <Text type="secondary">无运行任务</Text>
                )}
              </Descriptions.Item>
            </Descriptions>

            <Divider />

            <Descriptions title="能力信息" bordered>
              <Descriptions.Item label="支持的工具" span={3}>
                {selectedAgent.capabilities && selectedAgent.capabilities.length > 0 ? (
                  <div>
                    {selectedAgent.capabilities.map(cap => (
                      <Tag key={cap} color="blue" style={{ margin: '2px' }}>
                        {cap}
                      </Tag>
                    ))}
                  </div>
                ) : (
                  <Text type="secondary">无能力信息</Text>
                )}
              </Descriptions.Item>
              <Descriptions.Item label="标签" span={3}>
                {selectedAgent.tags && selectedAgent.tags.length > 0 ? (
                  <div>
                    {selectedAgent.tags.map(tag => (
                      <Tag key={tag} style={{ margin: '2px' }}>
                        {tag}
                      </Tag>
                    ))}
                  </div>
                ) : (
                  <Text type="secondary">无标签</Text>
                )}
              </Descriptions.Item>
            </Descriptions>

            {selectedAgent.api_key && (
              <>
                <Divider />
                <Descriptions title="安全信息" bordered>
                  <Descriptions.Item label="API密钥" span={3}>
                    <Input.Password
                      value={selectedAgent.api_key}
                      readOnly
                      style={{ fontFamily: 'monospace' }}
                    />
                  </Descriptions.Item>
                </Descriptions>
              </>
            )}
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default TaskAgentManager;
