import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Progress,
  Typography,
  Row,
  Col,
  Statistic,
  Badge,
  Tooltip,
  Modal,
  Form,
  Input,
  Select,
  message,
  Divider
} from 'antd';
import {
  RobotOutlined,
  PlusOutlined,
  ReloadOutlined,
  SettingOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { newAgentService } from '../services/api';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

interface Agent {
  agent_id: string;
  name: string;
  status: 'online' | 'offline' | 'busy' | 'maintenance';
  current_load: number;
  max_capacity: number;
  last_heartbeat: string;
  running_tasks: string[];
  capabilities: string[];
  location?: string;
  tags?: string[];
}

const TaskAgentManager: React.FC = () => {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    loadAgents();
    // 定时刷新Agent状态
    const interval = setInterval(loadAgents, 10000);
    return () => clearInterval(interval);
  }, []);

  const loadAgents = async () => {
    setLoading(true);
    try {
      const response = await newAgentService.getAgents();
      setAgents(response || []);
    } catch (error) {
      console.error('加载Agent失败:', error);
      message.error('加载Agent失败');
    } finally {
      setLoading(false);
    }
  };

  const handleRegisterAgent = async (values: any) => {
    try {
      await newAgentService.registerAgent(values);
      message.success('Agent注册成功');
      setModalVisible(false);
      form.resetFields();
      loadAgents();
    } catch (error) {
      console.error('注册Agent失败:', error);
      message.error('注册Agent失败');
    }
  };

  // 统计数据
  const statistics = {
    total: agents.length,
    online: agents.filter(a => a.status === 'online' || a.status === 'busy').length,
    busy: agents.filter(a => a.status === 'busy').length,
    offline: agents.filter(a => a.status === 'offline').length,
    totalCapacity: agents.reduce((sum, a) => sum + a.max_capacity, 0),
    currentLoad: agents.reduce((sum, a) => sum + a.current_load, 0)
  };

  const columns = [
    {
      title: 'Agent信息',
      key: 'info',
      render: (_, record: Agent) => (
        <div>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
            <RobotOutlined style={{ marginRight: 8, color: '#1890ff' }} />
            <Text strong>{record.name}</Text>
          </div>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.agent_id}
          </Text>
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          online: { color: 'green', icon: <CheckCircleOutlined />, text: '在线' },
          busy: { color: 'orange', icon: <SyncOutlined spin />, text: '忙碌' },
          offline: { color: 'red', icon: <ExclamationCircleOutlined />, text: '离线' },
          maintenance: { color: 'blue', icon: <SettingOutlined />, text: '维护' }
        };
        
        const config = statusConfig[status as keyof typeof statusConfig];
        return (
          <Badge 
            status={config.color as any} 
            text={
              <Space>
                {config.icon}
                {config.text}
              </Space>
            } 
          />
        );
      }
    },
    {
      title: '负载情况',
      key: 'load',
      render: (_, record: Agent) => (
        <div style={{ width: 120 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
            <Text style={{ fontSize: 12 }}>{record.current_load}/{record.max_capacity}</Text>
            <Text style={{ fontSize: 12 }}>
              {Math.round((record.current_load / record.max_capacity) * 100)}%
            </Text>
          </div>
          <Progress 
            percent={(record.current_load / record.max_capacity) * 100} 
            size="small" 
            status={record.current_load >= record.max_capacity ? 'exception' : 'active'}
            showInfo={false}
          />
        </div>
      )
    },
    {
      title: '能力',
      dataIndex: 'capabilities',
      key: 'capabilities',
      render: (capabilities: string[]) => (
        <div>
          {capabilities.slice(0, 3).map(cap => (
            <Tag key={cap} size="small" style={{ margin: '1px' }}>
              {cap}
            </Tag>
          ))}
          {capabilities.length > 3 && (
            <Tooltip title={capabilities.slice(3).join(', ')}>
              <Tag size="small" style={{ margin: '1px' }}>
                +{capabilities.length - 3}
              </Tag>
            </Tooltip>
          )}
        </div>
      )
    },
    {
      title: '最后心跳',
      dataIndex: 'last_heartbeat',
      key: 'last_heartbeat',
      render: (time: string) => (
        <div>
          <div>{dayjs(time).format('HH:mm:ss')}</div>
          <Text type="secondary" style={{ fontSize: 11 }}>
            {dayjs(time).fromNow()}
          </Text>
        </div>
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: Agent) => (
        <Space>
          <Tooltip title="查看详情">
            <Button type="text" icon={<SettingOutlined />} size="small" />
          </Tooltip>
          <Tooltip title="删除Agent">
            <Button 
              type="text" 
              danger 
              icon={<DeleteOutlined />} 
              size="small"
              onClick={() => handleDeleteAgent(record.agent_id)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  const handleDeleteAgent = (agentId: string) => {
    Modal.confirm({
      title: '确认删除Agent',
      content: '确定要删除这个Agent吗？',
      onOk: async () => {
        try {
          await newAgentService.deleteAgent(agentId);
          message.success('Agent删除成功');
          loadAgents();
        } catch (error) {
          message.error('删除Agent失败');
        }
      }
    });
  };

  return (
    <div>
      {/* 统计概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic 
              title="总Agent数" 
              value={statistics.total} 
              prefix={<RobotOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic 
              title="在线Agent" 
              value={statistics.online} 
              valueStyle={{ color: '#52c41a' }}
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic 
              title="忙碌Agent" 
              value={statistics.busy} 
              valueStyle={{ color: '#faad14' }}
              prefix={<SyncOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic 
              title="总容量利用率" 
              value={statistics.totalCapacity > 0 ? Math.round((statistics.currentLoad / statistics.totalCapacity) * 100) : 0}
              suffix="%" 
              valueStyle={{ 
                color: statistics.currentLoad / statistics.totalCapacity > 0.8 ? '#ff4d4f' : '#1890ff' 
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* Agent列表 */}
      <Card
        title={
          <Space>
            <RobotOutlined />
            Agent管理
          </Space>
        }
        extra={
          <Space>
            <Button icon={<PlusOutlined />} onClick={() => setModalVisible(true)}>
              注册Agent
            </Button>
            <Button icon={<ReloadOutlined />} onClick={loadAgents}>
              刷新
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={agents}
          rowKey="agent_id"
          loading={loading}
          size="small"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个Agent`
          }}
        />
      </Card>

      {/* 注册Agent模态框 */}
      <Modal
        title="注册新Agent"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleRegisterAgent}
        >
          <Form.Item
            name="agent_id"
            label="Agent ID"
            rules={[{ required: true, message: '请输入Agent ID' }]}
          >
            <Input placeholder="例如: scanner-001" />
          </Form.Item>
          
          <Form.Item
            name="name"
            label="Agent名称"
            rules={[{ required: true, message: '请输入Agent名称' }]}
          >
            <Input placeholder="例如: 扫描器-01" />
          </Form.Item>
          
          <Form.Item
            name="capabilities"
            label="能力列表"
            rules={[{ required: true, message: '请选择Agent能力' }]}
          >
            <Select
              mode="tags"
              placeholder="选择或输入Agent能力"
              options={[
                { value: 'subfinder', label: 'subfinder' },
                { value: 'nmap', label: 'nmap' },
                { value: 'nuclei', label: 'nuclei' },
                { value: 'httpx', label: 'httpx' },
                { value: 'assetfinder', label: 'assetfinder' }
              ]}
            />
          </Form.Item>
          
          <Form.Item
            name="max_capacity"
            label="最大容量"
            rules={[{ required: true, message: '请输入最大容量' }]}
          >
            <Input type="number" placeholder="例如: 10" />
          </Form.Item>
          
          <Form.Item
            name="location"
            label="位置"
          >
            <Input placeholder="例如: 北京" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TaskAgentManager;
