import React, { memo, useMemo, useCallback } from 'react';
import { Card, Table, Button, Tag, Space, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/es/table';

// 优化的卡片组件
export const OptimizedCard = memo<{
  title?: string;
  children: React.ReactNode;
  loading?: boolean;
  extra?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}>(({ title, children, loading, extra, className, style }) => {
  return (
    <Card
      title={title}
      loading={loading}
      extra={extra}
      className={className}
      style={style}
    >
      {children}
    </Card>
  );
});

OptimizedCard.displayName = 'OptimizedCard';

// 优化的表格行组件
export const OptimizedTableRow = memo<{
  record: any;
  columns: ColumnsType<any>;
  index: number;
}>(({ record, columns, index }) => {
  return (
    <tr>
      {columns.map((column, colIndex) => (
        <td key={colIndex}>
          {column.render 
            ? column.render(record[column.dataIndex as string], record, index)
            : record[column.dataIndex as string]
          }
        </td>
      ))}
    </tr>
  );
});

OptimizedTableRow.displayName = 'OptimizedTableRow';

// 优化的状态标签组件
export const StatusTag = memo<{
  status: string;
  colorMap?: Record<string, string>;
}>(({ status, colorMap = {} }) => {
  const color = useMemo(() => {
    const defaultColors: Record<string, string> = {
      active: 'green',
      inactive: 'red',
      pending: 'orange',
      success: 'green',
      error: 'red',
      warning: 'orange',
      online: 'green',
      offline: 'red'
    };
    return colorMap[status] || defaultColors[status] || 'default';
  }, [status, colorMap]);

  return <Tag color={color}>{status}</Tag>;
});

StatusTag.displayName = 'StatusTag';

// 优化的操作按钮组
export const ActionButtons = memo<{
  actions: Array<{
    key: string;
    label: string;
    onClick: () => void;
    type?: 'primary' | 'default' | 'dashed' | 'link' | 'text';
    danger?: boolean;
    disabled?: boolean;
    icon?: React.ReactNode;
    tooltip?: string;
  }>;
}>(({ actions }) => {
  const renderButton = useCallback((action: any) => {
    const button = (
      <Button
        key={action.key}
        type={action.type || 'default'}
        danger={action.danger}
        disabled={action.disabled}
        icon={action.icon}
        onClick={action.onClick}
        size="small"
      >
        {action.label}
      </Button>
    );

    return action.tooltip ? (
      <Tooltip key={action.key} title={action.tooltip}>
        {button}
      </Tooltip>
    ) : button;
  }, []);

  return (
    <Space size="small">
      {actions.map(renderButton)}
    </Space>
  );
});

ActionButtons.displayName = 'ActionButtons';

// 优化的数据展示组件
export const DataDisplay = memo<{
  data: Record<string, any>;
  fields: Array<{
    key: string;
    label: string;
    render?: (value: any) => React.ReactNode;
  }>;
  layout?: 'horizontal' | 'vertical';
}>(({ data, fields, layout = 'horizontal' }) => {
  const renderedFields = useMemo(() => {
    return fields.map(field => ({
      ...field,
      value: field.render ? field.render(data[field.key]) : data[field.key]
    }));
  }, [data, fields]);

  if (layout === 'vertical') {
    return (
      <Space direction="vertical" style={{ width: '100%' }}>
        {renderedFields.map(field => (
          <div key={field.key}>
            <strong>{field.label}: </strong>
            {field.value}
          </div>
        ))}
      </Space>
    );
  }

  return (
    <Space wrap>
      {renderedFields.map(field => (
        <span key={field.key}>
          <strong>{field.label}: </strong>
          {field.value}
        </span>
      ))}
    </Space>
  );
});

DataDisplay.displayName = 'DataDisplay';

// 优化的列表项组件
export const OptimizedListItem = memo<{
  item: any;
  renderContent: (item: any) => React.ReactNode;
  actions?: React.ReactNode;
  className?: string;
}>(({ item, renderContent, actions, className }) => {
  const content = useMemo(() => renderContent(item), [item, renderContent]);
  
  return (
    <div className={className} style={{ padding: '12px', borderBottom: '1px solid #f0f0f0' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div style={{ flex: 1 }}>
          {content}
        </div>
        {actions && (
          <div style={{ marginLeft: '16px' }}>
            {actions}
          </div>
        )}
      </div>
    </div>
  );
});

OptimizedListItem.displayName = 'OptimizedListItem';

// 优化的搜索结果高亮组件
export const HighlightText = memo<{
  text: string;
  searchTerm?: string;
  highlightStyle?: React.CSSProperties;
}>(({ text, searchTerm, highlightStyle = { backgroundColor: 'yellow' } }) => {
  const highlightedText = useMemo(() => {
    if (!searchTerm || !text) return text;
    
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <span key={index} style={highlightStyle}>
          {part}
        </span>
      ) : part
    );
  }, [text, searchTerm, highlightStyle]);

  return <>{highlightedText}</>;
});

HighlightText.displayName = 'HighlightText';

// 优化的空状态组件
export const EmptyState = memo<{
  description?: string;
  image?: React.ReactNode;
  action?: React.ReactNode;
}>(({ description = '暂无数据', image, action }) => {
  return (
    <div style={{ textAlign: 'center', padding: '40px 20px' }}>
      {image && <div style={{ marginBottom: '16px' }}>{image}</div>}
      <p style={{ color: '#999', marginBottom: action ? '16px' : 0 }}>
        {description}
      </p>
      {action}
    </div>
  );
});

EmptyState.displayName = 'EmptyState';
