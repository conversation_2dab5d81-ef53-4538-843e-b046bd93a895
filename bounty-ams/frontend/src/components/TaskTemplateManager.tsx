import React, { useState, useEffect, useMemo } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  message,
  Tooltip,
  Typography,
  Row,
  Col,
  Tabs,
  Drawer,
  Steps,
  Descriptions,
  Badge,
  Divider,
  Alert,
  Switch,
  InputN<PERSON>ber,
  Collapse,
  Tree,
  CodeEditor
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  PlayCircleOutlined,
  SettingOutlined,
  CodeOutlined,
  BranchesOutlined,
  ThunderboltOutlined,
  RobotOutlined,
  ExportOutlined,
  ImportOutlined
} from '@ant-design/icons';
import { OptimizedCard } from './OptimizedComponents';
import { taskTemplateService, newWorkflowService } from '../services/api';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;
const { TextArea } = Input;

interface TaskTemplate {
  template_id: string;
  name: string;
  version: string;
  description: string;
  category: string;
  tags: string[];
  parameters: Record<string, any>;
  tools: ToolConfig[];
  execution: Record<string, any>;
  output_schema: Record<string, any>;
  created_at: string;
  updated_at: string;
}

interface ToolConfig {
  tool_name: string;
  version: string;
  command: string;
  args: string[];
  env: Record<string, string>;
  output_format: string;
  output_parser: string;
  timeout: number;
  retry_count: number;
}

interface Workflow {
  workflow_id: string;
  name: string;
  version: string;
  description: string;
  parameters: Record<string, any>;
  stages: WorkflowStage[];
  error_handling: Record<string, any>;
  output_aggregation: Record<string, any>;
}

interface WorkflowStage {
  stage_id: string;
  name: string;
  tasks: TaskDefinition[];
  parallel: boolean;
  condition: string;
  timeout?: number;
  on_failure: string;
}

interface TaskDefinition {
  task_id: string;
  template_id: string;
  parameters: Record<string, any>;
  condition: string;
  depends_on: string[];
  timeout?: number;
  retry_count: number;
  on_failure: string;
}

const TaskTemplateManager: React.FC = () => {
  const [templates, setTemplates] = useState<TaskTemplate[]>([]);
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('templates');
  
  // 模态框状态
  const [templateModalVisible, setTemplateModalVisible] = useState(false);
  const [workflowModalVisible, setWorkflowModalVisible] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<TaskTemplate | null>(null);
  const [editingWorkflow, setEditingWorkflow] = useState<Workflow | null>(null);
  
  // 表单实例
  const [templateForm] = Form.useForm();
  const [workflowForm] = Form.useForm();

  useEffect(() => {
    loadTemplates();
    loadWorkflows();
  }, []);

  const loadTemplates = async () => {
    setLoading(true);
    try {
      const response = await taskTemplateService.getTemplates();
      setTemplates(response);
    } catch (error) {
      console.error('加载模板失败:', error);
      message.error('加载模板失败');
    } finally {
      setLoading(false);
    }
  };

  const loadWorkflows = async () => {
    try {
      const response = await newWorkflowService.getWorkflows();
      setWorkflows(response);
    } catch (error) {
      console.error('加载工作流失败:', error);
      message.error('加载工作流失败');
    }
  };

  // 模板表格列定义
  const templateColumns = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: TaskTemplate) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.template_id} v{record.version}
          </Text>
        </div>
      )
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      render: (category: string) => (
        <Tag color="blue">{category}</Tag>
      )
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      render: (tags: string[]) => (
        <Space wrap>
          {tags.map(tag => (
            <Tag key={tag} size="small">{tag}</Tag>
          ))}
        </Space>
      )
    },
    {
      title: '工具',
      dataIndex: 'tools',
      key: 'tools',
      render: (tools: ToolConfig[]) => (
        <Space wrap>
          {tools.map(tool => (
            <Badge key={tool.tool_name} count={tool.tool_name} style={{ backgroundColor: '#52c41a' }} />
          ))}
        </Space>
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: TaskTemplate) => (
        <Space>
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => handleEditTemplate(record)}
            />
          </Tooltip>
          <Tooltip title="复制">
            <Button 
              type="text" 
              icon={<CopyOutlined />} 
              onClick={() => handleCopyTemplate(record)}
            />
          </Tooltip>
          <Tooltip title="执行">
            <Button 
              type="text" 
              icon={<PlayCircleOutlined />} 
              onClick={() => handleExecuteTemplate(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button 
              type="text" 
              danger 
              icon={<DeleteOutlined />} 
              onClick={() => handleDeleteTemplate(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  // 工作流表格列定义
  const workflowColumns = [
    {
      title: '工作流名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Workflow) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.workflow_id} v{record.version}
          </Text>
        </div>
      )
    },
    {
      title: '阶段数',
      dataIndex: 'stages',
      key: 'stages',
      render: (stages: WorkflowStage[]) => (
        <Badge count={stages.length} style={{ backgroundColor: '#1890ff' }} />
      )
    },
    {
      title: '任务数',
      dataIndex: 'stages',
      key: 'task_count',
      render: (stages: WorkflowStage[]) => {
        const totalTasks = stages.reduce((sum, stage) => sum + stage.tasks.length, 0);
        return <Badge count={totalTasks} style={{ backgroundColor: '#52c41a' }} />;
      }
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: Workflow) => (
        <Space>
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => handleEditWorkflow(record)}
            />
          </Tooltip>
          <Tooltip title="可视化">
            <Button 
              type="text" 
              icon={<BranchesOutlined />} 
              onClick={() => handleVisualizeWorkflow(record)}
            />
          </Tooltip>
          <Tooltip title="执行">
            <Button 
              type="text" 
              icon={<PlayCircleOutlined />} 
              onClick={() => handleExecuteWorkflow(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button 
              type="text" 
              danger 
              icon={<DeleteOutlined />} 
              onClick={() => handleDeleteWorkflow(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  // 事件处理函数
  const handleEditTemplate = (template: TaskTemplate) => {
    setEditingTemplate(template);
    templateForm.setFieldsValue(template);
    setTemplateModalVisible(true);
  };

  const handleCopyTemplate = (template: TaskTemplate) => {
    const newTemplate = {
      ...template,
      template_id: `${template.template_id}_copy`,
      name: `${template.name} (副本)`
    };
    setEditingTemplate(newTemplate);
    templateForm.setFieldsValue(newTemplate);
    setTemplateModalVisible(true);
  };

  const handleExecuteTemplate = (template: TaskTemplate) => {
    message.info(`执行模板: ${template.name}`);
    // 这里应该跳转到任务执行页面或打开执行对话框
  };

  const handleDeleteTemplate = (template: TaskTemplate) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除模板 "${template.name}" 吗？`,
      onOk: () => {
        setTemplates(templates.filter(t => t.template_id !== template.template_id));
        message.success('删除成功');
      }
    });
  };

  const handleEditWorkflow = (workflow: Workflow) => {
    setEditingWorkflow(workflow);
    workflowForm.setFieldsValue(workflow);
    setWorkflowModalVisible(true);
  };

  const handleVisualizeWorkflow = (workflow: Workflow) => {
    message.info(`可视化工作流: ${workflow.name}`);
    // 这里应该打开工作流可视化编辑器
  };

  const handleExecuteWorkflow = (workflow: Workflow) => {
    message.info(`执行工作流: ${workflow.name}`);
    // 这里应该跳转到工作流执行页面
  };

  const handleDeleteWorkflow = (workflow: Workflow) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除工作流 "${workflow.name}" 吗？`,
      onOk: () => {
        setWorkflows(workflows.filter(w => w.workflow_id !== workflow.workflow_id));
        message.success('删除成功');
      }
    });
  };

  return (
    <div style={{ padding: '24px' }}>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <OptimizedCard
            title={
              <Space>
                <RobotOutlined />
                任务模板与工作流管理
              </Space>
            }
            extra={
              <Space>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={() => {
                    if (activeTab === 'templates') {
                      setEditingTemplate(null);
                      templateForm.resetFields();
                      setTemplateModalVisible(true);
                    } else {
                      setEditingWorkflow(null);
                      workflowForm.resetFields();
                      setWorkflowModalVisible(true);
                    }
                  }}
                >
                  {activeTab === 'templates' ? '新建模板' : '新建工作流'}
                </Button>
                <Button icon={<ImportOutlined />}>导入</Button>
                <Button icon={<ExportOutlined />}>导出</Button>
              </Space>
            }
          >
            <Tabs activeKey={activeTab} onChange={setActiveTab}>
              <TabPane 
                tab={
                  <Space>
                    <ThunderboltOutlined />
                    任务模板
                    <Badge count={templates.length} style={{ backgroundColor: '#52c41a' }} />
                  </Space>
                } 
                key="templates"
              >
                <Table
                  columns={templateColumns}
                  dataSource={templates}
                  rowKey="template_id"
                  loading={loading}
                  pagination={{
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 个模板`
                  }}
                />
              </TabPane>
              
              <TabPane 
                tab={
                  <Space>
                    <BranchesOutlined />
                    工作流
                    <Badge count={workflows.length} style={{ backgroundColor: '#1890ff' }} />
                  </Space>
                } 
                key="workflows"
              >
                <Table
                  columns={workflowColumns}
                  dataSource={workflows}
                  rowKey="workflow_id"
                  loading={loading}
                  pagination={{
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 个工作流`
                  }}
                />
              </TabPane>
            </Tabs>
          </OptimizedCard>
        </Col>
      </Row>

      {/* 模板编辑模态框 */}
      <Modal
        title={editingTemplate ? '编辑任务模板' : '新建任务模板'}
        open={templateModalVisible}
        onCancel={() => setTemplateModalVisible(false)}
        width={800}
        footer={null}
      >
        {/* 模板编辑表单内容 */}
        <Alert 
          message="任务模板编辑器" 
          description="配置任务的执行参数、工具链和输出格式" 
          type="info" 
          showIcon 
          style={{ marginBottom: 16 }}
        />
      </Modal>

      {/* 工作流编辑模态框 */}
      <Modal
        title={editingWorkflow ? '编辑工作流' : '新建工作流'}
        open={workflowModalVisible}
        onCancel={() => setWorkflowModalVisible(false)}
        width={1000}
        footer={null}
      >
        {/* 工作流编辑表单内容 */}
        <Alert 
          message="工作流编辑器" 
          description="设计复杂的多阶段任务执行流程" 
          type="info" 
          showIcon 
          style={{ marginBottom: 16 }}
        />
      </Modal>
    </div>
  );
};

export default TaskTemplateManager;
