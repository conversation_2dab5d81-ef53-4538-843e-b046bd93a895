import React, { useState, useEffect, useMemo } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Progress,
  Typography,
  Row,
  Col,
  Statistic,
  Timeline,
  Descriptions,
  Badge,
  Divider,
  Alert,
  Tabs,
  Drawer,
  Modal,
  Select,
  Input,
  DatePicker,
  Tooltip,
  Empty
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
  ThunderboltOutlined,
  RobotOutlined,
  MonitorOutlined,
  BarChartOutlined,
  FilterOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { OptimizedCard, StatusTag } from './OptimizedComponents';
import { taskExecutionService, newAgentService } from '../services/api';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

interface TaskExecution {
  execution_id: string;
  template_id: string;
  template_name: string;
  status: 'pending' | 'running' | 'success' | 'failed' | 'cancelled' | 'timeout';
  agent_id?: string;
  agent_name?: string;
  start_time?: string;
  end_time?: string;
  duration?: number;
  progress?: number;
  output?: any;
  error?: string;
  logs: string[];
  metadata: Record<string, any>;
}

interface WorkflowExecution {
  execution_id: string;
  workflow_id: string;
  workflow_name: string;
  status: 'pending' | 'running' | 'success' | 'failed' | 'cancelled' | 'paused';
  start_time?: string;
  end_time?: string;
  current_stage?: string;
  progress?: number;
  task_executions: Record<string, TaskExecution>;
  stage_status: Record<string, string>;
  output?: any;
  error?: string;
}

interface AgentStatus {
  agent_id: string;
  name: string;
  status: 'online' | 'offline' | 'busy' | 'maintenance';
  current_load: number;
  max_capacity: number;
  last_heartbeat: string;
  running_tasks: string[];
  capabilities: string[];
}

const TaskExecutionMonitor: React.FC = () => {
  const [taskExecutions, setTaskExecutions] = useState<TaskExecution[]>([]);
  const [workflowExecutions, setWorkflowExecutions] = useState<WorkflowExecution[]>([]);
  const [agentStatuses, setAgentStatuses] = useState<AgentStatus[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('tasks');
  const [selectedExecution, setSelectedExecution] = useState<TaskExecution | WorkflowExecution | null>(null);
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);
  const [filters, setFilters] = useState({
    status: undefined,
    agent_id: undefined,
    date_range: undefined
  });

  useEffect(() => {
    loadExecutions();
    loadAgentStatuses();
    
    // 设置定时刷新
    const interval = setInterval(() => {
      loadExecutions();
      loadAgentStatuses();
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const loadExecutions = async () => {
    setLoading(true);
    try {
      // 加载任务执行记录
      const taskResponse = await taskExecutionService.getExecutions({ limit: 50 });
      setTaskExecutions(taskResponse);

      // 加载工作流执行记录
      const workflowResponse = await taskExecutionService.getWorkflowExecutions({ limit: 50 });
      setWorkflowExecutions(workflowResponse);
    } catch (error) {
      console.error('加载执行记录失败:', error);
      message.error('加载执行记录失败');
    } finally {
      setLoading(false);
    }
  };

  const loadAgentStatuses = async () => {
    try {
      const response = await newAgentService.getAgents();
      setAgentStatuses(response);
    } catch (error) {
      console.error('加载Agent状态失败:', error);
      message.error('加载Agent状态失败');
    }
  };

  // 统计数据
  const statistics = useMemo(() => {
    const taskStats = {
      total: taskExecutions.length,
      running: taskExecutions.filter(t => t.status === 'running').length,
      success: taskExecutions.filter(t => t.status === 'success').length,
      failed: taskExecutions.filter(t => t.status === 'failed').length,
      pending: taskExecutions.filter(t => t.status === 'pending').length
    };

    const workflowStats = {
      total: workflowExecutions.length,
      running: workflowExecutions.filter(w => w.status === 'running').length,
      success: workflowExecutions.filter(w => w.status === 'success').length,
      failed: workflowExecutions.filter(w => w.status === 'failed').length
    };

    const agentStats = {
      total: agentStatuses.length,
      online: agentStatuses.filter(a => a.status === 'online' || a.status === 'busy').length,
      offline: agentStatuses.filter(a => a.status === 'offline').length,
      busy: agentStatuses.filter(a => a.status === 'busy').length
    };

    return { taskStats, workflowStats, agentStats };
  }, [taskExecutions, workflowExecutions, agentStatuses]);

  // 任务表格列定义
  const taskColumns = [
    {
      title: '任务ID',
      dataIndex: 'execution_id',
      key: 'execution_id',
      width: 120,
      render: (text: string) => (
        <Text code style={{ fontSize: '12px' }}>{text.slice(-8)}</Text>
      )
    },
    {
      title: '模板',
      dataIndex: 'template_name',
      key: 'template_name',
      render: (text: string, record: TaskExecution) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.template_id}
          </Text>
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          pending: { color: 'default', icon: <ClockCircleOutlined /> },
          running: { color: 'processing', icon: <SyncOutlined spin /> },
          success: { color: 'success', icon: <CheckCircleOutlined /> },
          failed: { color: 'error', icon: <ExclamationCircleOutlined /> },
          cancelled: { color: 'default', icon: <StopOutlined /> },
          timeout: { color: 'warning', icon: <ClockCircleOutlined /> }
        };
        
        const config = statusConfig[status as keyof typeof statusConfig];
        return (
          <Badge 
            status={config.color as any} 
            text={
              <Space>
                {config.icon}
                {status}
              </Space>
            } 
          />
        );
      }
    },
    {
      title: 'Agent',
      dataIndex: 'agent_name',
      key: 'agent_name',
      render: (text: string) => text ? <Tag icon={<RobotOutlined />}>{text}</Tag> : '-'
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number, record: TaskExecution) => (
        <div style={{ width: 100 }}>
          <Progress 
            percent={progress || 0} 
            size="small" 
            status={record.status === 'failed' ? 'exception' : 'active'}
          />
        </div>
      )
    },
    {
      title: '执行时间',
      key: 'duration',
      render: (_, record: TaskExecution) => {
        if (record.duration) {
          return `${Math.round(record.duration)}s`;
        }
        if (record.start_time) {
          const duration = dayjs().diff(dayjs(record.start_time), 'second');
          return `${duration}s`;
        }
        return '-';
      }
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: TaskExecution) => (
        <Space>
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              onClick={() => {
                setSelectedExecution(record);
                setDetailDrawerVisible(true);
              }}
            />
          </Tooltip>
          {record.status === 'running' && (
            <Tooltip title="停止任务">
              <Button 
                type="text" 
                danger 
                icon={<StopOutlined />} 
                onClick={() => handleStopTask(record.execution_id)}
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ];

  // Agent状态卡片
  const renderAgentCard = (agent: AgentStatus) => (
    <Card
      key={agent.agent_id}
      size="small"
      title={
        <Space>
          <RobotOutlined />
          {agent.name}
          <StatusTag 
            status={agent.status} 
            colorMap={{
              online: 'green',
              busy: 'orange',
              offline: 'red',
              maintenance: 'blue'
            }}
          />
        </Space>
      }
      extra={
        <Text type="secondary">
          {dayjs(agent.last_heartbeat).fromNow()}
        </Text>
      }
    >
      <Row gutter={16}>
        <Col span={12}>
          <Statistic 
            title="负载" 
            value={agent.current_load} 
            suffix={`/ ${agent.max_capacity}`}
            valueStyle={{ fontSize: 16 }}
          />
          <Progress 
            percent={(agent.current_load / agent.max_capacity) * 100} 
            size="small" 
            showInfo={false}
          />
        </Col>
        <Col span={12}>
          <Statistic 
            title="运行任务" 
            value={agent.running_tasks.length}
            valueStyle={{ fontSize: 16 }}
          />
        </Col>
      </Row>
      
      <Divider style={{ margin: '8px 0' }} />
      
      <div>
        <Text strong style={{ fontSize: 12 }}>能力:</Text>
        <div style={{ marginTop: 4 }}>
          {agent.capabilities.map(cap => (
            <Tag key={cap} size="small" style={{ margin: '1px' }}>
              {cap}
            </Tag>
          ))}
        </div>
      </div>
    </Card>
  );

  const handleStopTask = (executionId: string) => {
    Modal.confirm({
      title: '确认停止任务',
      content: '确定要停止这个任务吗？',
      onOk: () => {
        // 调用停止任务API
        console.log('停止任务:', executionId);
      }
    });
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 统计概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <OptimizedCard title="任务统计">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic title="总数" value={statistics.taskStats.total} />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="运行中" 
                  value={statistics.taskStats.running} 
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="成功" 
                  value={statistics.taskStats.success} 
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="失败" 
                  value={statistics.taskStats.failed} 
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Col>
            </Row>
          </OptimizedCard>
        </Col>
        
        <Col span={8}>
          <OptimizedCard title="工作流统计">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic title="总数" value={statistics.workflowStats.total} />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="运行中" 
                  value={statistics.workflowStats.running} 
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="成功" 
                  value={statistics.workflowStats.success} 
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="失败" 
                  value={statistics.workflowStats.failed} 
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Col>
            </Row>
          </OptimizedCard>
        </Col>
        
        <Col span={8}>
          <OptimizedCard title="Agent状态">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic title="总数" value={statistics.agentStats.total} />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="在线" 
                  value={statistics.agentStats.online} 
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="忙碌" 
                  value={statistics.agentStats.busy} 
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="离线" 
                  value={statistics.agentStats.offline} 
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Col>
            </Row>
          </OptimizedCard>
        </Col>
      </Row>

      {/* 主要内容 */}
      <OptimizedCard
        title={
          <Space>
            <MonitorOutlined />
            任务执行监控
          </Space>
        }
        extra={
          <Space>
            <Button icon={<FilterOutlined />}>过滤</Button>
            <Button icon={<DownloadOutlined />}>导出</Button>
            <Button icon={<ReloadOutlined />} onClick={loadExecutions}>
              刷新
            </Button>
          </Space>
        }
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane 
            tab={
              <Space>
                <ThunderboltOutlined />
                任务执行
                <Badge count={statistics.taskStats.running} style={{ backgroundColor: '#1890ff' }} />
              </Space>
            } 
            key="tasks"
          >
            <Table
              columns={taskColumns}
              dataSource={taskExecutions}
              rowKey="execution_id"
              loading={loading}
              size="small"
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个任务`
              }}
            />
          </TabPane>
          
          <TabPane 
            tab={
              <Space>
                <RobotOutlined />
                Agent状态
                <Badge count={statistics.agentStats.online} style={{ backgroundColor: '#52c41a' }} />
              </Space>
            } 
            key="agents"
          >
            <Row gutter={[16, 16]}>
              {agentStatuses.map(agent => (
                <Col key={agent.agent_id} span={8}>
                  {renderAgentCard(agent)}
                </Col>
              ))}
            </Row>
          </TabPane>
        </Tabs>
      </OptimizedCard>

      {/* 详情抽屉 */}
      <Drawer
        title="执行详情"
        placement="right"
        width={600}
        open={detailDrawerVisible}
        onClose={() => setDetailDrawerVisible(false)}
      >
        {selectedExecution && (
          <div>
            <Descriptions column={1} bordered size="small">
              <Descriptions.Item label="执行ID">
                {selectedExecution.execution_id}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <StatusTag status={selectedExecution.status} />
              </Descriptions.Item>
              {'template_name' in selectedExecution && (
                <Descriptions.Item label="模板">
                  {selectedExecution.template_name}
                </Descriptions.Item>
              )}
              {'agent_name' in selectedExecution && selectedExecution.agent_name && (
                <Descriptions.Item label="Agent">
                  {selectedExecution.agent_name}
                </Descriptions.Item>
              )}
              {selectedExecution.start_time && (
                <Descriptions.Item label="开始时间">
                  {dayjs(selectedExecution.start_time).format('YYYY-MM-DD HH:mm:ss')}
                </Descriptions.Item>
              )}
            </Descriptions>

            {'logs' in selectedExecution && selectedExecution.logs.length > 0 && (
              <>
                <Divider>执行日志</Divider>
                <Timeline size="small">
                  {selectedExecution.logs.map((log, index) => (
                    <Timeline.Item key={index}>
                      <Text style={{ fontSize: 12 }}>{log}</Text>
                    </Timeline.Item>
                  ))}
                </Timeline>
              </>
            )}

            {selectedExecution.output && (
              <>
                <Divider>执行结果</Divider>
                <pre style={{ 
                  background: '#f5f5f5', 
                  padding: 12, 
                  borderRadius: 4,
                  fontSize: 12,
                  maxHeight: 200,
                  overflow: 'auto'
                }}>
                  {JSON.stringify(selectedExecution.output, null, 2)}
                </pre>
              </>
            )}
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default TaskExecutionMonitor;
