import { assetService, taskService, agentService, dashboardService, enhancedSearchService } from './api';
import { apiCache, longTermCache, shortTermCache } from './cache';

// 优化后的API服务
export class OptimizedApiService {
  // 批量加载仪表板数据
  static async loadDashboardData() {
    const cacheKey = 'dashboard_data';
    
    try {
      // 使用短期缓存，因为仪表板数据变化较频繁
      return await shortTermCache.wrapApiCall(
        cacheKey,
        async () => {
          // 并行加载所有数据
          const [
            assetStatsResponse,
            taskStatsResponse,
            agentResponse,
            recentAssetsResponse,
            recentTasksResponse
          ] = await Promise.all([
            assetService.getAssetStats().catch(() => ({ data: {} })),
            taskService.getTaskStats().catch(() => ({ data: {} })),
            agentService.getAgents().catch(() => ({ data: { agents: [] } })),
            assetService.searchAssets({
              size: 10,
              sort: 'discovered_at:desc'
            }).catch(() => ({ data: { assets: [] } })),
            taskService.getTasks({
              size: 10,
              sort: 'created_at:desc'
            }).catch(() => ({ data: { tasks: [] } }))
          ]);

          const agents = agentResponse.data?.agents || [];
          const assetStats = assetStatsResponse.data || {};
          const taskStats = taskStatsResponse.data || {};

          return {
            assetStats,
            taskStats,
            agentStats: {
              total: agents.length,
              active: agents.filter((a: any) => a.status === 'active').length,
              idle: agents.filter((a: any) => a.status === 'idle').length,
              offline: agents.filter((a: any) => a.status === 'offline').length,
            },
            recentAssets: recentAssetsResponse.data?.assets || [],
            recentTasks: recentTasksResponse.data?.tasks || []
          };
        },
        {},
        2 * 60 * 1000 // 2分钟缓存
      );
    } catch (error) {
      console.error('批量加载仪表板数据失败:', error);
      throw error;
    }
  }

  // 批量加载平台和项目数据
  static async loadPlatformsAndProjects() {
    const cacheKey = 'platforms_projects';
    
    try {
      // 使用长期缓存，因为平台和项目数据变化较少
      return await longTermCache.wrapApiCall(
        cacheKey,
        async () => {
          // 先获取模型类型
          const modelTypesResponse = await assetService.getDynamicModelTypes();
          const modelTypes = modelTypesResponse.data;
          
          const platformType = modelTypes.find((t: any) => t.name === 'platform');
          const projectType = modelTypes.find((t: any) => t.name === 'project');
          
          const promises = [];
          
          if (platformType) {
            promises.push(assetService.getDynamicEntities(platformType.id));
          } else {
            promises.push(Promise.resolve({ data: [] }));
          }
          
          if (projectType) {
            promises.push(assetService.getDynamicEntities(projectType.id));
          } else {
            promises.push(Promise.resolve({ data: [] }));
          }
          
          const [platformsResponse, projectsResponse] = await Promise.all(promises);
          
          // 保持原有的数据结构，不进行转换
          const platforms = platformsResponse.data || [];
          const projects = projectsResponse.data || [];

          return {
            platforms,
            projects,
            modelTypes: {
              platform: platformType,
              project: projectType
            }
          };
        },
        {},
        15 * 60 * 1000 // 15分钟缓存
      );
    } catch (error) {
      console.error('批量加载平台和项目数据失败:', error);
      return {
        platforms: [],
        projects: [],
        modelTypes: { platform: null, project: null }
      };
    }
  }

  // 批量加载资产统计数据
  static async loadAssetStats() {
    const cacheKey = 'asset_stats';
    
    try {
      return await apiCache.wrapApiCall(
        cacheKey,
        async () => {
          const [statsResponse, timelineResponse] = await Promise.all([
            assetService.getAssetStats().catch(() => ({ data: {} })),
            assetService.getAssetTimeline(30).catch(() => ({ data: { timeline: [] } }))
          ]);

          return {
            stats: statsResponse.data || {},
            timeline: timelineResponse.data?.timeline || []
          };
        },
        {},
        5 * 60 * 1000 // 5分钟缓存
      );
    } catch (error) {
      console.error('加载资产统计数据失败:', error);
      throw error;
    }
  }

  // 优化的项目资产计数（使用动态模型UUID）
  static async loadProjectAssetCounts(projectIds: string[]) {
    const cacheKey = `project_asset_counts_${projectIds.join(',')}`;
    
    try {
      return await apiCache.wrapApiCall(
        cacheKey,
        async () => {
          const counts: { [key: string]: number } = {};
          
          // 并行查询每个项目的资产数量
          const countPromises = projectIds.map(async (projectId) => {
            try {
              // 使用基于动态模型的资产API
              const response = await assetService.searchAssets({
                project_id: projectId, // 使用UUID格式的项目ID
                size: 0, // 只获取计数，不获取具体数据
                include_aggregations: true
              });
              
              return {
                projectId,
                count: response.data?.total || 0
              };
            } catch (error) {
              console.warn(`查询项目 ${projectId} 的资产数量失败:`, error);
              return {
                projectId,
                count: 0
              };
            }
          });
          
          const results = await Promise.all(countPromises);
          
          // 构建结果对象
          results.forEach(({ projectId, count }) => {
            counts[projectId] = count;
          });
          
          return counts;
        },
        {},
        5 * 60 * 1000 // 5分钟缓存
      );
    } catch (error) {
      console.error('批量加载项目资产数量失败:', error);
      return {};
    }
  }

  // 批量加载平台资产数量（使用动态模型UUID）
  static async loadPlatformAssetCounts(platformIds: string[]) {
    const cacheKey = `platform_asset_counts_${platformIds.join(',')}`;
    
    try {
      return await apiCache.wrapApiCall(
        cacheKey,
        async () => {
          const counts: { [key: string]: number } = {};
          
          // 并行查询每个平台的资产数量
          const countPromises = platformIds.map(async (platformId) => {
            try {
              // 使用基于动态模型的资产API
              const response = await assetService.searchAssets({
                platform_id: platformId, // 使用UUID格式的平台ID
                size: 0, // 只获取计数，不获取具体数据
                include_aggregations: true
              });
              
              return {
                platformId,
                count: response.data?.total || 0
              };
            } catch (error) {
              console.warn(`查询平台 ${platformId} 的资产数量失败:`, error);
              return {
                platformId,
                count: 0
              };
            }
          });
          
          const results = await Promise.all(countPromises);
          
          // 构建结果对象
          results.forEach(({ platformId, count }) => {
            counts[platformId] = count;
          });
          
          return counts;
        },
        {},
        5 * 60 * 1000 // 5分钟缓存
      );
    } catch (error) {
      console.error('批量加载平台资产数量失败:', error);
      return {};
    }
  }

  // 优化的Widget数据加载
  static async loadWidgetData(widgetConfig: any) {
    const cacheKey = `widget_data_${JSON.stringify(widgetConfig)}`;
    
    try {
      return await apiCache.wrapApiCall(
        cacheKey,
        async () => {
          const response = await dashboardService.getWidgetData(widgetConfig);
          return response.data;
        },
        widgetConfig,
        3 * 60 * 1000 // 3分钟缓存
      );
    } catch (error) {
      console.error('加载Widget数据失败:', error);
      throw error;
    }
  }

  // 优化的搜索功能（防抖）
  static async searchWithDebounce(searchParams: any, delay: number = 300) {
    const cacheKey = `search_${JSON.stringify(searchParams)}`;
    
    // 防抖处理
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(async () => {
        try {
          const result = await apiCache.wrapApiCall(
            cacheKey,
            async () => {
              const response = await enhancedSearchService.advancedSearch(searchParams);
              return response.data;
            },
            searchParams,
            2 * 60 * 1000 // 2分钟缓存
          );
          resolve(result);
        } catch (error) {
          reject(error);
        }
      }, delay);

      // 清除之前的超时
      if ((globalThis as any)._searchTimeoutId) {
        clearTimeout((globalThis as any)._searchTimeoutId);
      }
      (globalThis as any)._searchTimeoutId = timeoutId;
    });
  }

  // 预加载常用数据
  static async preloadCommonData() {
    try {
      // 预加载平台和项目数据
      this.loadPlatformsAndProjects();
      
      // 预加载资产统计
      this.loadAssetStats();
      
      // 预加载Widget模板
      longTermCache.wrapApiCall(
        'widget_templates',
        () => dashboardService.getWidgetTemplates(),
        {},
        30 * 60 * 1000 // 30分钟缓存
      );
    } catch (error) {
      console.error('预加载数据失败:', error);
    }
  }

  // 清除所有缓存
  static clearAllCache() {
    apiCache.clearAll();
    longTermCache.clearAll();
    shortTermCache.clearAll();
  }

  // 清除特定类型的缓存
  static clearCache(type: 'dashboard' | 'platforms' | 'assets' | 'all') {
    switch (type) {
      case 'dashboard':
        apiCache.clear('dashboard_data');
        shortTermCache.clear('dashboard_data');
        break;
      case 'platforms':
        longTermCache.clear('platforms_projects');
        break;
      case 'assets':
        apiCache.clear('asset_stats');
        break;
      case 'all':
        this.clearAllCache();
        break;
    }
  }

  // 获取缓存统计信息
  static getCacheStats() {
    return {
      apiCache: apiCache.getStats(),
      longTermCache: longTermCache.getStats(),
      shortTermCache: shortTermCache.getStats()
    };
  }
}

// 创建防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: number;
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

// 创建节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

export default OptimizedApiService; 