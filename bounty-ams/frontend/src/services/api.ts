import axios from 'axios';

// 使用代理，所以直接使用 /api 而不是完整URL
const API_BASE_URL = '/api';

export const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000, // 增加到10秒
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // 只在非登录请求时记录日志，避免日志污染
    if (!config.url?.includes('/auth/login')) {
      console.log('API Request:', config.method?.toUpperCase(), config.url, 'with auth:', !!token);
    }
    
    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    // 只在非登录请求时记录日志
    if (!response.config.url?.includes('/auth/login')) {
      console.log('API Response:', response.status, response.config.url);
    }
    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.status, error.response?.data, error.config?.url);
    
    if (error.response?.status === 401) {
      // 更智能的401错误处理
      const isLoginRequest = error.config?.url?.includes('/auth/login');
      const isAuthMeRequest = error.config?.url?.includes('/auth/me');
      const isCurrentlyOnLoginPage = window.location.pathname === '/login';
      
      if (!isLoginRequest && !isCurrentlyOnLoginPage) {
        console.log('Token expired or invalid, clearing auth data');
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        delete api.defaults.headers.common['Authorization'];
        
        // 对于用户信息请求，立即跳转
        if (isAuthMeRequest) {
          window.location.href = '/login';
        } else {
          // 对于其他请求，给用户提示并延迟跳转
          console.warn('API请求认证失败，请重新登录');
          setTimeout(() => {
            if (window.location.pathname !== '/login') {
              window.location.href = '/login';
            }
          }, 2000);
        }
      }
    }
    return Promise.reject(error);
  }
);

// API Services
export const authService = {
  login: (username: string, password: string) =>
    api.post('/auth/login', { username, password }),
  
  getCurrentUser: () => api.get('/auth/me'),
};

export const assetService = {
  // 使用基于动态模型的统一资产API
  searchAssets: (params: any) => api.get('/assets/search', { params }),
  
  getAssetStats: () => api.get('/assets/statistics'),
  
  getAssetTypes: () => api.get('/assets/statistics'),
  
  getAssetSources: () => api.get('/assets/statistics'),
  
  getAvailableFields: () => api.get('/dynamic-models/field-types'),
  
  getAssetTimeline: (days: number = 30) => 
    api.get(`/assets/statistics`),
  
  getAssetDetails: (assetId: string) => 
    api.get(`/assets/${assetId}`),
  
  processAssets: (limit: number = 100) => 
    api.get(`/assets/search?limit=${limit}`),
  
  // 地理分布洞察
  getGeoInsights: (params?: any) => api.get('/assets/search', { params }),
  
  // 资产数据导入
  ingestAssets: (assetsData: any[]) => api.post('/assets/ingest', assetsData),
  
  // 更新资产
  updateAsset: (assetId: string, data: any) => api.put(`/assets/${assetId}`, data),
  
  // 删除资产
  deleteAsset: (assetId: string) => api.delete(`/assets/${assetId}`),
  
  // 动态模型相关方法
  getDynamicModelTypes: () => api.get('/dynamic-models/types'),
  
  getDynamicEntities: (modelTypeId: string, params?: any) => 
    api.get(`/dynamic-models/entities`, { 
      params: { 
        model_type_id: modelTypeId,
        ...params 
      }
    }),
  
  getDynamicEntitiesCount: (modelTypeId: string, params?: any) => 
    api.get(`/dynamic-models/entities/count`, { 
      params: { 
        model_type_id: modelTypeId,
        ...params 
      }
    }),
  
  createDynamicEntity: (data: any) => api.post('/dynamic-models/entities', data),
  
  updateDynamicEntity: (entityId: string, data: any) => 
    api.put(`/dynamic-models/entities/${entityId}`, data),
  
  deleteDynamicEntity: (entityId: string) => 
    api.delete(`/dynamic-models/entities/${entityId}`),

  // 获取所有资产（聚合查询） - 保持兼容性但推荐使用searchAssets
  getAllAssets: (params?: any) => 
    api.get('/unified-assets/search', { params }),

  // 获取所有资产数量 - 保持兼容性
  getAllAssetsCount: (params?: any) => {
    // 使用searchAssets但只返回总数
    return api.get('/unified-assets/search', { 
      params: { ...params, limit: 0 } 
    }).then(response => ({
      data: { count: response.data.total }
    }));
  },
};

export const workflowService = {
  getWorkflows: () => api.get('/workflows'),
  
  createWorkflow: (data: any) => api.post('/workflows', data),
  
  getWorkflowDetails: (workflowId: string) => 
    api.get(`/workflows/${workflowId}`),
  
  executeWorkflow: (workflowId: string, target: string) => 
    api.post(`/workflows/${workflowId}/execute`, { target }),
};

export const agentService = {
  getAgents: () => api.get('/agents/'),
  
  registerAgent: (data: any) => api.post('/agents/register', data),
  
  getAgentDetails: (agentId: string) => 
    api.get(`/agents/${agentId}`),
  
  updateAgentStatus: (agentId: string, status: string) => 
    api.post(`/agents/${agentId}/status`, { status }),
  
  deleteAgent: (agentId: string) => 
    api.delete(`/agents/${agentId}`),
  
  pingAgent: (agentId: string) =>
    api.post(`/agents/${agentId}/ping`),
    
  // Agent远程控制命令
  sendCommand: (agentId: string, action: string, parameters?: any) =>
    api.post(`/agents/${agentId}/command`, { action, parameters }),
  
  // 具体控制方法
  pauseAgent: (agentId: string) =>
    api.post(`/agents/${agentId}/command`, { action: 'pause' }),
    
  resumeAgent: (agentId: string) =>
    api.post(`/agents/${agentId}/command`, { action: 'resume' }),
    
  stopAgent: (agentId: string) =>
    api.post(`/agents/${agentId}/command`, { action: 'stop' }),
    
  restartAgent: (agentId: string) =>
    api.post(`/agents/${agentId}/command`, { action: 'restart' }),
    
  cancelAgentTasks: (agentId: string) =>
    api.post(`/agents/${agentId}/command`, { action: 'cancel_tasks' }),
};

export const taskService = {
  getTasks: (params: any) => api.get('/tasks', { params }),
  
  createTask: (data: any) => {
    // 如果是多个目标，使用批量创建API
    if (data.targets && Array.isArray(data.targets) && data.targets.length > 1) {
      return api.post('/tasks/batch', data);
    }
    // 单个目标使用原有API（保持向后兼容）
    return api.post('/tasks', data);
  },
  
  getTaskDetails: (taskId: string) => 
    api.get(`/tasks/${taskId}`),
  
  updateTaskStatus: (taskId: string, status: string) => 
    api.post(`/tasks/${taskId}/status`, { status }),
    
  cancelTask: (taskId: string) => 
    api.post(`/tasks/${taskId}/cancel`),
    
  retryTask: (taskId: string) => 
    api.post(`/tasks/${taskId}/retry`),
    
  deleteTask: (taskId: string) => 
    api.delete(`/tasks/${taskId}`),
  
  getTaskStats: () => api.get('/tasks/stats'),
  
  // 批量操作
  bulkCancelTasks: (taskIds: string[]) => 
    api.post('/tasks/bulk-cancel', { task_ids: taskIds }),
    
  bulkDeleteTasks: (taskIds: string[]) => 
    api.post('/tasks/bulk-delete', { task_ids: taskIds }),
};

export const dynamicModelService = {
  // Model Types
  getModelTypes: (params?: any) => api.get('/dynamic-models/types', { params }),
  
  createModelType: (data: any) => api.post('/dynamic-models/types', data),
  
  getModelType: (modelTypeId: string) => 
    api.get(`/dynamic-models/types/${modelTypeId}`),
  
  updateModelType: (modelTypeId: string, data: any) => 
    api.put(`/dynamic-models/types/${modelTypeId}`, data),
  
  deleteModelType: (modelTypeId: string) => 
    api.delete(`/dynamic-models/types/${modelTypeId}`),
  
  // Entities
  getEntities: (params?: any) => api.get('/dynamic-models/entities', { params }),
  
  createEntity: (data: any) => api.post('/dynamic-models/entities', data),
  
  getEntity: (entityId: string) => 
    api.get(`/dynamic-models/entities/${entityId}`),
  
  updateEntity: (entityId: string, data: any) => 
    api.put(`/dynamic-models/entities/${entityId}`, data),
  
  deleteEntity: (entityId: string) => 
    api.delete(`/dynamic-models/entities/${entityId}`),
  
  // Field Types
  getFieldTypes: () => api.get('/dynamic-models/field-types/'),
};

// ES深度集成服务
export const enhancedSearchService = {
  // 高级搜索
  advancedSearch: (searchParams: any) => 
    api.post('/enhanced-search/assets', searchParams),
  
  // 资产洞察分析
  getAssetInsights: (platformId?: string, projectId?: string) => 
    api.get('/enhanced-search/assets/insights', { 
      params: { platform_id: platformId, project_id: projectId }
    }),
  
  // 相似资产搜索
  getSimilarAssets: (assetId: string, size: number = 10) => 
    api.get(`/enhanced-search/assets/${assetId}/similar`, { 
      params: { size }
    }),
  
  // 搜索建议
  getSearchSuggestions: (query: string, field: string = 'asset_value') => 
    api.get('/enhanced-search/suggestions', { 
      params: { query, field }
    }),
  
  // 批量更新资产
  bulkUpdateAssets: (updates: any[]) => 
    api.post('/enhanced-search/assets/bulk-update', updates),
  
  // 处理原始资产数据
  processRawAssets: (rawAssets: any[]) => 
    api.post('/enhanced-search/assets/process', rawAssets),
  
  // 平台资产汇总
  getPlatformAssetSummary: (platformId: string) => 
    api.get(`/enhanced-search/platforms/${platformId}/assets/summary`),
  
  // 项目资产汇总
  getProjectAssetSummary: (projectId: string) => 
    api.get(`/enhanced-search/projects/${projectId}/assets/summary`),
  
  // 导出资产
  exportAssets: (searchParams: any, format: string = 'json') => 
    api.post('/enhanced-search/assets/export', searchParams, { 
      params: { export_format: format }
    }),
  
  // 重建索引
  reindexAssets: () => 
    api.post('/enhanced-search/maintenance/reindex'),
  
  // 健康检查
  healthCheck: () => 
    api.get('/enhanced-search/health'),
  
  // 安全分析
  securityAnalysis: (platformId?: string, riskLevel: string = 'high') => 
    api.post('/enhanced-search/security-analysis', {}, { 
      params: { platform_id: platformId, risk_level: riskLevel }
    }),
  
  // 自定义查询构建器
  buildCustomQuery: (queryConfig: any, execute: boolean = true) => 
    api.post('/enhanced-search/query-builder', queryConfig, { 
      params: { execute }
    }),
  
  // 高级数据处理管道
  advancedDataProcessing: (rawAssets: any[], autoCorrelate: boolean = true, autoIndex: boolean = true) => 
    api.post('/enhanced-search/data-pipeline/process', rawAssets, { 
      params: { auto_correlate: autoCorrelate, auto_index: autoIndex }
    }),
  
  // 获取资产关联情报
  getAssetCorrelations: (platformId?: string, projectId?: string, correlationType: string = 'all') => 
    api.get('/enhanced-search/intelligence/correlations', { 
      params: { 
        platform_id: platformId, 
        project_id: projectId, 
        correlation_type: correlationType 
      }
    }),
  
  // 综合风险评估
  comprehensiveRiskAssessment: (platformId?: string, projectId?: string, includeRemediation: boolean = true) => 
    api.get('/enhanced-search/intelligence/risk-assessment', { 
      params: { 
        platform_id: platformId, 
        project_id: projectId, 
        include_remediation: includeRemediation 
      }
    }),
  
  // 获取处理统计
  getProcessingStatistics: () => 
    api.get('/enhanced-search/processing/statistics'),
};

// Agent密钥管理服务
export const agentKeyService = {
  // 获取密钥列表
  getKeys: (params?: any) => api.get('/agent-keys/', { params }),
  
  // 获取密钥统计信息
  getStats: () => api.get('/agent-keys/stats'),
  
  // 创建密钥
  createKey: (data: any) => api.post('/agent-keys/', data),
  
  // 获取密钥详情
  getKey: (keyId: string) => api.get(`/agent-keys/${keyId}`),
  
  // 更新密钥
  updateKey: (keyId: string, data: any) => api.put(`/agent-keys/${keyId}`, data),
  
  // 撤销密钥
  revokeKey: (keyId: string) => api.post(`/agent-keys/${keyId}/revoke`),
  
  // 暂停密钥
  suspendKey: (keyId: string) => api.post(`/agent-keys/${keyId}/suspend`),
  
  // 激活密钥
  activateKey: (keyId: string) => api.post(`/agent-keys/${keyId}/activate`),
  
  // 删除密钥
  deleteKey: (keyId: string) => api.delete(`/agent-keys/${keyId}`),
  
  // 批量撤销密钥
  batchRevokeKeys: (keyIds: string[]) => api.post('/agent-keys/batch/revoke', keyIds),
  
  // 清理过期密钥
  cleanupExpiredKeys: () => api.post('/agent-keys/cleanup/expired'),
};

// 仪表板数据服务
export const dashboardService = {
  // 获取仪表板概览
  getOverview: () => api.get('/dashboard/overview'),
  
  // 获取资产统计 - 使用真实的API
  getAssetStats: () => api.get('/assets/statistics'),
  
  // 获取任务统计
  getTaskStats: () => api.get('/tasks/stats'),
  
  // 获取代理统计
  getAgentStats: () => api.get('/agents/stats/system'),
  
  // 获取最近资产
  getRecentAssets: (limit: number = 10) => 
    api.get('/assets/search', { 
      params: { 
        limit, 
        sort: 'discovered_at:desc' 
      } 
    }),
  
  // 获取最近任务  
  getRecentTasks: (limit: number = 10) => 
    api.get('/tasks', { 
      params: { 
        size: limit, 
        sort: 'created_at:desc' 
      } 
    }),
  
  // 获取发现资产统计
  getDiscoveredAssetStats: () => api.get('/discovered-assets/stats'),
  
  // 获取组件数据
  getWidgetData: (widgetConfig: any) => 
    api.post('/dashboard/widget/data', widgetConfig),
  
  // 获取组件模板
  getWidgetTemplates: () => api.get('/dashboard/widget/templates'),
  
  // 获取风险分析
  getRiskAnalysis: () => api.get('/dashboard/risk-analysis'),
  
  // 执行自定义查询
  executeCustomQuery: (queryConfig: any) => 
    api.post('/dashboard/query/custom', queryConfig),
  
  // 获取可聚合字段
  getAggregatableFields: () => api.get('/dashboard/aggregations/fields'),
  
  // 健康检查
  healthCheck: () => api.get('/dashboard/health'),
};

// 数据导入服务
export const dataImportService = {
  // 分析上传文件
  analyzeFile: (formData: FormData) => {
    const apiWithFile = axios.create({
      baseURL: API_BASE_URL,
      timeout: 120000, // 分析大文件需要更长时间 (2分钟)
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    // 添加认证头
    const token = localStorage.getItem('token');
    if (token) {
      apiWithFile.defaults.headers['Authorization'] = `Bearer ${token}`;
    }
    
    return apiWithFile.post('/data-import/analyze', formData);
  },
  
  // 执行数据导入
  executeImport: (formData: FormData) => {
    const apiWithFile = axios.create({
      baseURL: API_BASE_URL,
      timeout: 600000, // 大文件导入可能需要很长时间 (10分钟)
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    // 添加认证头
    const token = localStorage.getItem('token');
    if (token) {
      apiWithFile.defaults.headers['Authorization'] = `Bearer ${token}`;
    }
    
    return apiWithFile.post('/data-import/execute', formData);
  },
  
  // 获取可用的资产类型
  getAssetTypes: () => api.get('/data-import/asset-types'),
  
  // 获取支持的字段类型
  getFieldTypes: () => api.get('/data-import/field-types'),
  
  // 获取平台项目列表
  getPlatforms: () => api.get('/data-import/platforms'),
  
  // 为资产类型添加字段
  addFieldsToAssetType: (assetTypeId: string, fields: any[]) =>
    api.post(`/data-import/asset-types/${assetTypeId}/fields`, { fields }),
};

// ==================== 新的任务管理系统服务 ====================

// 任务模板服务 (基于动态模型)
export const taskTemplateService = {
  // 获取任务模板列表
  getTemplates: async (params?: {
    category?: string;
    tags?: string;
    search?: string;
    is_active?: boolean;
    skip?: number;
    limit?: number;
  }) => {
    const response = await api.get('/task-management/templates', { params });
    return response.data;
  },

  // 创建任务模板
  createTemplate: async (templateData: any) => {
    const response = await api.post('/task-management/templates', templateData);
    return response.data;
  },

  // 获取任务模板详情
  getTemplate: async (templateId: string) => {
    const response = await api.get(`/task-management/templates/${templateId}`);
    return response.data;
  },

  // 更新任务模板
  updateTemplate: async (templateId: string, templateData: any) => {
    const response = await api.put(`/task-management/templates/${templateId}`, templateData);
    return response.data;
  },

  // 删除任务模板
  deleteTemplate: async (templateId: string) => {
    const response = await api.delete(`/task-management/templates/${templateId}`);
    return response.data;
  }
};

// 新的工作流服务 (基于动态模型)
export const newWorkflowService = {
  // 获取工作流列表
  getWorkflows: async (params?: {
    search?: string;
    is_active?: boolean;
    skip?: number;
    limit?: number;
  }) => {
    const response = await api.get('/task-management/workflows', { params });
    return response.data;
  },

  // 创建工作流
  createWorkflow: async (workflowData: any) => {
    const response = await api.post('/task-management/workflows', workflowData);
    return response.data;
  },

  // 获取工作流详情
  getWorkflow: async (workflowId: string) => {
    const response = await api.get(`/task-management/workflows/${workflowId}`);
    return response.data;
  },

  // 更新工作流
  updateWorkflow: async (workflowId: string, workflowData: any) => {
    const response = await api.put(`/task-management/workflows/${workflowId}`, workflowData);
    return response.data;
  },

  // 删除工作流
  deleteWorkflow: async (workflowId: string) => {
    const response = await api.delete(`/task-management/workflows/${workflowId}`);
    return response.data;
  },

  // 执行工作流
  executeWorkflow: async (workflowId: string, parameters: any) => {
    const response = await api.post('/task-management/execute', {
      template_id: workflowId,
      parameters
    });
    return response.data;
  }
};

// 任务执行服务 (基于动态模型)
export const taskExecutionService = {
  // 执行任务
  executeTask: async (templateId: string, parameters: any, priority: number = 2) => {
    const response = await api.post('/task-management/execute', {
      template_id: templateId,
      parameters,
      priority
    });
    return response.data;
  },

  // 获取任务执行列表
  getExecutions: async (params?: {
    status?: string;
    template_id?: string;
    agent_id?: string;
    skip?: number;
    limit?: number;
  }) => {
    const response = await api.get('/task-management/executions', { params });
    return response.data;
  },

  // 获取任务执行详情
  getExecution: async (executionId: string) => {
    const response = await api.get(`/task-management/executions/${executionId}`);
    return response.data;
  },

  // 取消任务执行
  cancelExecution: async (executionId: string) => {
    const response = await api.post(`/task-management/executions/${executionId}/cancel`);
    return response.data;
  },

  // 获取工作流执行列表 (暂时映射到任务执行)
  getWorkflowExecutions: async (params?: {
    status?: string;
    workflow_id?: string;
    skip?: number;
    limit?: number;
  }) => {
    return await taskExecutionService.getExecutions(params);
  },

  // 获取工作流执行详情 (暂时映射到任务执行)
  getWorkflowExecution: async (executionId: string) => {
    return await taskExecutionService.getExecution(executionId);
  }
};

// 新的Agent服务
export const newAgentService = {
  // 获取Agent列表
  getAgents: async () => {
    const response = await api.get('/tasks/agents');
    return response.data;
  },

  // 注册Agent
  registerAgent: async (agentData: any) => {
    const response = await api.post('/tasks/agents', agentData);
    return response.data;
  },

  // Agent心跳
  sendHeartbeat: async (agentId: string, heartbeatData: any) => {
    const response = await api.post(`/tasks/agents/${agentId}/heartbeat`, heartbeatData);
    return response.data;
  }
};

// 监控服务
export const monitoringService = {
  // 获取系统统计
  getSystemStats: async () => {
    const response = await api.get('/tasks/stats');
    return response.data;
  },

  // 获取队列状态
  getQueueStatus: async () => {
    const response = await api.get('/tasks/queue/status');
    return response.data;
  },

  // 获取任务日志
  getTaskLogs: async (executionId: string) => {
    const response = await api.get(`/tasks/executions/${executionId}/logs`);
    return response.data;
  }
};

export default api;